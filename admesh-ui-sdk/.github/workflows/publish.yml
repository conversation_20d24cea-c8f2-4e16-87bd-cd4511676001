name: Publish AdMesh UI SDK & Deploy to Vercel

on:
  push:
    branches: [main]
    paths:
      - 'package.json'
      - 'src/**'
      - 'docs/**'
      - '.storybook/**'
      - '.github/workflows/publish.yml'
  workflow_dispatch:

permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.0'
          registry-url: 'https://registry.npmjs.org'
          cache: 'npm'

      - name: Verify Node.js version
        run: |
          echo "Node.js version: $(node --version)"
          echo "NPM version: $(npm --version)"
          echo "PATH: $PATH"
          which node

      - name: Install dependencies
        run: npm ci

      - name: Install docs dependencies
        run: cd docs && npm ci

      - name: Run linting
        run: npm run lint

      - name: Build library
        run: npm run build

      - name: Build documentation and Storybook
        id: build-all
        run: npm run build:all
        
      - name: Check if version changed
        id: version-check
        run: |
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          PUBLISHED_VERSION=$(npm view @admesh/ui-sdk version 2>/dev/null || echo "0.0.0")
          echo "current=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "published=$PUBLISHED_VERSION" >> $GITHUB_OUTPUT
          if [ "$CURRENT_VERSION" != "$PUBLISHED_VERSION" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Publish to NPM
        if: steps.version-check.outputs.changed == 'true'
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          
      - name: Create GitHub Release
        if: steps.version-check.outputs.changed == 'true'
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.version-check.outputs.current }}
          name: Release v${{ steps.version-check.outputs.current }}
          body: |
            ## Changes in v${{ steps.version-check.outputs.current }}

            - Updated AdMesh UI SDK components
            - Fixed TypeScript errors and Node.js compatibility
            - Improved Storybook deployment and GitHub Actions workflow
            - See [CHANGELOG.md](./CHANGELOG.md) for detailed changes

            ## Installation

            ```bash
            npm install admesh-ui-sdk@${{ steps.version-check.outputs.current }}
            ```

            ## Hosted Documentation & Storybook

            - **Documentation**: [https://admesh-ui-sdk.vercel.app/](https://admesh-ui-sdk.vercel.app/)
            - **Storybook**: [https://admesh-ui-sdk.vercel.app/storybook/](https://admesh-ui-sdk.vercel.app/storybook/)
          draft: false
          prerelease: false

      # Vercel deployment is handled automatically via GitHub integration
      # No manual deployment step needed - Vercel deploys on push to main

      - name: Deploy to Chromatic (Optional)
        if: always() && steps.build-all.conclusion == 'success'
        uses: chromaui/action@latest
        continue-on-error: true
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          token: ${{ secrets.GITHUB_TOKEN }}
          storybookBuildDir: storybook-static
