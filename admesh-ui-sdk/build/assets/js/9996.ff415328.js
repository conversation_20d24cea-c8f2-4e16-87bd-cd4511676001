"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[9996],{9996:(e,a,r)=>{r.d(a,{diagram:()=>c});var s=r(1388),t=r(6627),n=r(2107),d=r(8731),i={parse:(0,n.K2)((async e=>{const a=await(0,d.qg)("info",e);n.Rm.debug(a)}),"parse")},o={version:s.n.version},c={parser:i,db:{getVersion:(0,n.K2)((()=>o.version),"getVersion")},renderer:{draw:(0,n.K2)(((e,a,r)=>{n.Rm.debug("rendering info diagram\n"+e);const s=(0,t.D)(a);(0,n.a$)(s,100,400,!0);s.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${r}`)}),"draw")}}}}]);