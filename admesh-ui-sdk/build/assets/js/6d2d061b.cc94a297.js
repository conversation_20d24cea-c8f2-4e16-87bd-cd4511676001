"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[1016],{7063:(n,e,i)=>{i.r(e),i.d(e,{assets:()=>l,contentTitle:()=>o,default:()=>h,frontMatter:()=>r,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"typescript-sdk/installation","title":"TypeScript SDK Installation","description":"Technical guide for installing and configuring the AdMesh TypeScript SDK for Node.js applications and serverless functions.","source":"@site/docs/typescript-sdk/installation.md","sourceDirName":"typescript-sdk","slug":"/typescript-sdk/installation","permalink":"/typescript-sdk/installation","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/typescript-sdk/installation.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Python SDK Installation","permalink":"/python-sdk/installation"},"next":{"title":"UI SDK Installation","permalink":"/ui-sdk/installation"}}');var t=i(4848),a=i(8453);const r={sidebar_position:1},o="TypeScript SDK Installation",l={},c=[{value:"Installation",id:"installation",level:2},{value:"NPM Installation",id:"npm-installation",level:3},{value:"Yarn Installation",id:"yarn-installation",level:3},{value:"PNPM Installation",id:"pnpm-installation",level:3},{value:"Basic Configuration",id:"basic-configuration",level:2},{value:"Environment Setup",id:"environment-setup",level:3},{value:"Client Initialization",id:"client-initialization",level:3},{value:"Basic Usage",id:"basic-usage",level:2},{value:"Get Recommendations",id:"get-recommendations",level:3},{value:"Configuration Options",id:"configuration-options",level:2},{value:"Client Configuration",id:"client-configuration",level:3},{value:"Error Handling",id:"error-handling",level:2},{value:"Next Steps",id:"next-steps",level:2}];function d(n){const e={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,a.R)(),...n.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.header,{children:(0,t.jsx)(e.h1,{id:"typescript-sdk-installation",children:"TypeScript SDK Installation"})}),"\n",(0,t.jsx)(e.p,{children:"Technical guide for installing and configuring the AdMesh TypeScript SDK for Node.js applications and serverless functions."}),"\n",(0,t.jsx)(e.h2,{id:"installation",children:"Installation"}),"\n",(0,t.jsx)(e.h3,{id:"npm-installation",children:"NPM Installation"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-bash",children:"npm install admesh\n"})}),"\n",(0,t.jsx)(e.h3,{id:"yarn-installation",children:"Yarn Installation"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-bash",children:"yarn add admesh\n"})}),"\n",(0,t.jsx)(e.h3,{id:"pnpm-installation",children:"PNPM Installation"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-bash",children:"pnpm add admesh\n"})}),"\n",(0,t.jsx)(e.h2,{id:"basic-configuration",children:"Basic Configuration"}),"\n",(0,t.jsx)(e.h3,{id:"environment-setup",children:"Environment Setup"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-typescript",children:"// .env\nADMESH_API_KEY=your_api_key_here\nADMESH_BASE_URL=https://api.useadmesh.com\n"})}),"\n",(0,t.jsx)(e.h3,{id:"client-initialization",children:"Client Initialization"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\n// Environment variable configuration (recommended)\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY\n});\n\n// Direct configuration\nconst client = new Admesh({\n  apiKey: 'your_api_key_here',\n  baseUrl: 'https://api.useadmesh.com'\n});\n"})}),"\n",(0,t.jsx)(e.h2,{id:"basic-usage",children:"Basic Usage"}),"\n",(0,t.jsx)(e.h3,{id:"get-recommendations",children:"Get Recommendations"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY\n});\n\nasync function getRecommendations() {\n  try {\n    const response = await client.recommend.getRecommendations({\n      query: 'Enterprise CRM solutions',\n      format: 'auto'\n    });\n    \n    console.log('Recommendations:', response.recommendations);\n  } catch (error) {\n    console.error('Error:', error);\n  }\n}\n"})}),"\n",(0,t.jsx)(e.h2,{id:"configuration-options",children:"Configuration Options"}),"\n",(0,t.jsx)(e.h3,{id:"client-configuration",children:"Client Configuration"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-typescript",children:"interface AdmeshConfig {\n  apiKey: string;\n  baseUrl?: string;\n  timeout?: number;\n  retries?: number;\n}\n\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY,\n  baseUrl: 'https://api.useadmesh.com',\n  timeout: 30000,\n  retries: 3\n});\n"})}),"\n",(0,t.jsx)(e.h2,{id:"error-handling",children:"Error Handling"}),"\n",(0,t.jsx)(e.pre,{children:(0,t.jsx)(e.code,{className:"language-typescript",children:"import Admesh, { AdmeshError, AuthenticationError } from 'admesh';\n\ntry {\n  const response = await client.recommend.getRecommendations({\n    query: 'CRM software'\n  });\n} catch (error) {\n  if (error instanceof AuthenticationError) {\n    console.error('Authentication failed:', error.message);\n  } else if (error instanceof AdmeshError) {\n    console.error('AdMesh API error:', error.message);\n  } else {\n    console.error('Unexpected error:', error);\n  }\n}\n"})}),"\n",(0,t.jsx)(e.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.strong,{children:(0,t.jsx)(e.a,{href:"/getting-started/overview",children:"Getting Started"})})," - Core concepts and setup"]}),"\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.strong,{children:(0,t.jsx)(e.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.strong,{children:(0,t.jsx)(e.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Implementation examples"]}),"\n"]})]})}function h(n={}){const{wrapper:e}={...(0,a.R)(),...n.components};return e?(0,t.jsx)(e,{...n,children:(0,t.jsx)(d,{...n})}):d(n)}}}]);