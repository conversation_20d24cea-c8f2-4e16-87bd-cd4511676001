(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[8938],{1400:(t,e,i)=>{"use strict";i.d(e,{m:()=>r});var n=i(2107),r=class{constructor(t){this.init=t,this.records=this.init()}static{(0,n.K2)(this,"ImperativeState")}reset(){this.records=this.init()}}},1709:function(t,e,i){var n;n=function(t){return(()=>{"use strict";var e={45:(t,e,i)=>{var n={};n.layoutBase=i(551),n.CoSEConstants=i(806),n.CoSEEdge=i(767),n.CoSEGraph=i(880),n.CoSEGraphManager=i(578),n.CoSELayout=i(765),n.CoSENode=i(991),n.ConstraintHandler=i(902),t.exports=n},806:(t,e,i)=>{var n=i(551).FDLayoutConstants;function r(){}for(var o in n)r[o]=n[o];r.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,r.DEFAULT_RADIAL_SEPARATION=n.DEFAULT_EDGE_LENGTH,r.DEFAULT_COMPONENT_SEPERATION=60,r.TILE=!0,r.TILING_PADDING_VERTICAL=10,r.TILING_PADDING_HORIZONTAL=10,r.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,r.ENFORCE_CONSTRAINTS=!0,r.APPLY_LAYOUT=!0,r.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,r.TREE_REDUCTION_ON_INCREMENTAL=!0,r.PURE_INCREMENTAL=r.DEFAULT_INCREMENTAL,t.exports=r},767:(t,e,i)=>{var n=i(551).FDLayoutEdge;function r(t,e,i){n.call(this,t,e,i)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},880:(t,e,i)=>{var n=i(551).LGraph;function r(t,e,i){n.call(this,t,e,i)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},578:(t,e,i)=>{var n=i(551).LGraphManager;function r(t){n.call(this,t)}for(var o in r.prototype=Object.create(n.prototype),n)r[o]=n[o];t.exports=r},765:(t,e,i)=>{var n=i(551).FDLayout,r=i(578),o=i(880),s=i(991),a=i(767),h=i(806),l=i(902),c=i(551).FDLayoutConstants,d=i(551).LayoutConstants,g=i(551).Point,u=i(551).PointD,f=i(551).DimensionD,p=i(551).Layout,v=i(551).Integer,y=i(551).IGeometry,m=i(551).LGraph,E=i(551).Transform,N=i(551).LinkedList;function T(){n.call(this),this.toBeTiled={},this.constraints={}}for(var A in T.prototype=Object.create(n.prototype),n)T[A]=n[A];T.prototype.newGraphManager=function(){var t=new r(this);return this.graphManager=t,t},T.prototype.newGraph=function(t){return new o(null,this.graphManager,t)},T.prototype.newNode=function(t){return new s(this.graphManager,t)},T.prototype.newEdge=function(t){return new a(null,null,t)},T.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=c.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=c.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},T.prototype.initSpringEmbedder=function(){n.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/c.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},T.prototype.layout=function(){return d.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},T.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental)h.TREE_REDUCTION_ON_INCREMENTAL&&(this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation(),e=new Set(this.getAllNodes()),i=this.nodesWithGravity.filter((function(t){return e.has(t)})),this.graphManager.setAllNodesToApplyGravitation(i));else{var t=this.getFlatForest();if(t.length>0)this.positionNodesRadially(t);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var e=new Set(this.getAllNodes()),i=this.nodesWithGravity.filter((function(t){return e.has(t)}));this.graphManager.setAllNodesToApplyGravitation(i),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(l.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),h.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},T.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}if(this.totalIterations%c.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged()){if(!(this.prunedNodesAll.length>0))return!0;this.isTreeGrowing=!0}this.coolingCycle++,0==this.layoutQuality?this.coolingAdjuster=this.coolingCycle:1==this.layoutQuality&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),e=this.nodesWithGravity.filter((function(e){return t.has(e)}));this.graphManager.setAllNodesToApplyGravitation(e),this.graphManager.updateBounds(),this.updateGrid(),h.PURE_INCREMENTAL?this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),h.PURE_INCREMENTAL?this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=c.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var i=!this.isTreeGrowing&&!this.isGrowthFinished,n=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(i,n),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},T.prototype.getPositionsData=function(){for(var t=this.graphManager.getAllNodes(),e={},i=0;i<t.length;i++){var n=t[i].rect,r=t[i].id;e[r]={id:r,x:n.getCenterX(),y:n.getCenterY(),w:n.width,h:n.height}}return e},T.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var t=!1;if("during"===c.ANIMATE)this.emit("layoutstarted");else{for(;!t;)t=this.tick();this.graphManager.updateBounds()}},T.prototype.moveNodes=function(){for(var t=this.getAllNodes(),e=0;e<t.length;e++)t[e].calculateDisplacement();for(Object.keys(this.constraints).length>0&&this.updateDisplacements(),e=0;e<t.length;e++)t[e].move()},T.prototype.initConstraintVariables=function(){var t=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var e=this.graphManager.getAllNodes(),i=0;i<e.length;i++){var n=e[i];this.idToNodeMap.set(n.id,n)}var r=function e(i){for(var n,r=i.getChild().getNodes(),o=0,s=0;s<r.length;s++)null==(n=r[s]).getChild()?t.fixedNodeSet.has(n.id)&&(o+=100):o+=e(n);return o};if(this.constraints.fixedNodeConstraint)for(this.constraints.fixedNodeConstraint.forEach((function(e){t.fixedNodeSet.add(e.nodeId)})),e=this.graphManager.getAllNodes(),i=0;i<e.length;i++)if(null!=(n=e[i]).getChild()){var o=r(n);o>0&&(n.fixedNodeWeight=o)}if(this.constraints.relativePlacementConstraint){var s=new Map,a=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach((function(e){t.fixedNodesOnHorizontal.add(e),t.fixedNodesOnVertical.add(e)})),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical){var l=this.constraints.alignmentConstraint.vertical;for(i=0;i<l.length;i++)this.dummyToNodeForVerticalAlignment.set("dummy"+i,[]),l[i].forEach((function(e){s.set(e,"dummy"+i),t.dummyToNodeForVerticalAlignment.get("dummy"+i).push(e),t.fixedNodeSet.has(e)&&t.fixedNodesOnHorizontal.add("dummy"+i)}))}if(this.constraints.alignmentConstraint.horizontal){var c=this.constraints.alignmentConstraint.horizontal;for(i=0;i<c.length;i++)this.dummyToNodeForHorizontalAlignment.set("dummy"+i,[]),c[i].forEach((function(e){a.set(e,"dummy"+i),t.dummyToNodeForHorizontalAlignment.get("dummy"+i).push(e),t.fixedNodeSet.has(e)&&t.fixedNodesOnVertical.add("dummy"+i)}))}}if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(t){var e,i,n;for(n=t.length-1;n>=2*t.length/3;n--)e=Math.floor(Math.random()*(n+1)),i=t[n],t[n]=t[e],t[e]=i;return t},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach((function(e){if(e.left){var i=s.has(e.left)?s.get(e.left):e.left,n=s.has(e.right)?s.get(e.right):e.right;t.nodesInRelativeHorizontal.includes(i)||(t.nodesInRelativeHorizontal.push(i),t.nodeToRelativeConstraintMapHorizontal.set(i,[]),t.dummyToNodeForVerticalAlignment.has(i)?t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(i)[0]).getCenterX()):t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(i).getCenterX())),t.nodesInRelativeHorizontal.includes(n)||(t.nodesInRelativeHorizontal.push(n),t.nodeToRelativeConstraintMapHorizontal.set(n,[]),t.dummyToNodeForVerticalAlignment.has(n)?t.nodeToTempPositionMapHorizontal.set(n,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(n)[0]).getCenterX()):t.nodeToTempPositionMapHorizontal.set(n,t.idToNodeMap.get(n).getCenterX())),t.nodeToRelativeConstraintMapHorizontal.get(i).push({right:n,gap:e.gap}),t.nodeToRelativeConstraintMapHorizontal.get(n).push({left:i,gap:e.gap})}else{var r=a.has(e.top)?a.get(e.top):e.top,o=a.has(e.bottom)?a.get(e.bottom):e.bottom;t.nodesInRelativeVertical.includes(r)||(t.nodesInRelativeVertical.push(r),t.nodeToRelativeConstraintMapVertical.set(r,[]),t.dummyToNodeForHorizontalAlignment.has(r)?t.nodeToTempPositionMapVertical.set(r,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(r)[0]).getCenterY()):t.nodeToTempPositionMapVertical.set(r,t.idToNodeMap.get(r).getCenterY())),t.nodesInRelativeVertical.includes(o)||(t.nodesInRelativeVertical.push(o),t.nodeToRelativeConstraintMapVertical.set(o,[]),t.dummyToNodeForHorizontalAlignment.has(o)?t.nodeToTempPositionMapVertical.set(o,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(o)[0]).getCenterY()):t.nodeToTempPositionMapVertical.set(o,t.idToNodeMap.get(o).getCenterY())),t.nodeToRelativeConstraintMapVertical.get(r).push({bottom:o,gap:e.gap}),t.nodeToRelativeConstraintMapVertical.get(o).push({top:r,gap:e.gap})}}));else{var d=new Map,g=new Map;this.constraints.relativePlacementConstraint.forEach((function(t){if(t.left){var e=s.has(t.left)?s.get(t.left):t.left,i=s.has(t.right)?s.get(t.right):t.right;d.has(e)?d.get(e).push(i):d.set(e,[i]),d.has(i)?d.get(i).push(e):d.set(i,[e])}else{var n=a.has(t.top)?a.get(t.top):t.top,r=a.has(t.bottom)?a.get(t.bottom):t.bottom;g.has(n)?g.get(n).push(r):g.set(n,[r]),g.has(r)?g.get(r).push(n):g.set(r,[n])}}));var u=function(t,e){var i=[],n=[],r=new N,o=new Set,s=0;return t.forEach((function(a,h){if(!o.has(h)){i[s]=[],n[s]=!1;var l=h;for(r.push(l),o.add(l),i[s].push(l);0!=r.length;)l=r.shift(),e.has(l)&&(n[s]=!0),t.get(l).forEach((function(t){o.has(t)||(r.push(t),o.add(t),i[s].push(t))}));s++}})),{components:i,isFixed:n}},f=u(d,t.fixedNodesOnHorizontal);this.componentsOnHorizontal=f.components,this.fixedComponentsOnHorizontal=f.isFixed;var p=u(g,t.fixedNodesOnVertical);this.componentsOnVertical=p.components,this.fixedComponentsOnVertical=p.isFixed}}},T.prototype.updateDisplacements=function(){var t=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach((function(e){var i=t.idToNodeMap.get(e.nodeId);i.displacementX=0,i.displacementY=0})),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var e=this.constraints.alignmentConstraint.vertical,i=0;i<e.length;i++){for(var n=0,r=0;r<e[i].length;r++){if(this.fixedNodeSet.has(e[i][r])){n=0;break}n+=this.idToNodeMap.get(e[i][r]).displacementX}var o=n/e[i].length;for(r=0;r<e[i].length;r++)this.idToNodeMap.get(e[i][r]).displacementX=o}if(this.constraints.alignmentConstraint.horizontal){var s=this.constraints.alignmentConstraint.horizontal;for(i=0;i<s.length;i++){var a=0;for(r=0;r<s[i].length;r++){if(this.fixedNodeSet.has(s[i][r])){a=0;break}a+=this.idToNodeMap.get(s[i][r]).displacementY}var l=a/s[i].length;for(r=0;r<s[i].length;r++)this.idToNodeMap.get(s[i][r]).displacementY=l}}}if(this.constraints.relativePlacementConstraint)if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach((function(e){if(!t.fixedNodesOnHorizontal.has(e)){var i=0;i=t.dummyToNodeForVerticalAlignment.has(e)?t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(e)[0]).displacementX:t.idToNodeMap.get(e).displacementX,t.nodeToRelativeConstraintMapHorizontal.get(e).forEach((function(n){var r;n.right?(r=t.nodeToTempPositionMapHorizontal.get(n.right)-t.nodeToTempPositionMapHorizontal.get(e)-i)<n.gap&&(i-=n.gap-r):(r=t.nodeToTempPositionMapHorizontal.get(e)-t.nodeToTempPositionMapHorizontal.get(n.left)+i)<n.gap&&(i+=n.gap-r)})),t.nodeToTempPositionMapHorizontal.set(e,t.nodeToTempPositionMapHorizontal.get(e)+i),t.dummyToNodeForVerticalAlignment.has(e)?t.dummyToNodeForVerticalAlignment.get(e).forEach((function(e){t.idToNodeMap.get(e).displacementX=i})):t.idToNodeMap.get(e).displacementX=i}})),this.nodesInRelativeVertical.forEach((function(e){if(!t.fixedNodesOnHorizontal.has(e)){var i=0;i=t.dummyToNodeForHorizontalAlignment.has(e)?t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(e)[0]).displacementY:t.idToNodeMap.get(e).displacementY,t.nodeToRelativeConstraintMapVertical.get(e).forEach((function(n){var r;n.bottom?(r=t.nodeToTempPositionMapVertical.get(n.bottom)-t.nodeToTempPositionMapVertical.get(e)-i)<n.gap&&(i-=n.gap-r):(r=t.nodeToTempPositionMapVertical.get(e)-t.nodeToTempPositionMapVertical.get(n.top)+i)<n.gap&&(i+=n.gap-r)})),t.nodeToTempPositionMapVertical.set(e,t.nodeToTempPositionMapVertical.get(e)+i),t.dummyToNodeForHorizontalAlignment.has(e)?t.dummyToNodeForHorizontalAlignment.get(e).forEach((function(e){t.idToNodeMap.get(e).displacementY=i})):t.idToNodeMap.get(e).displacementY=i}}));else{for(i=0;i<this.componentsOnHorizontal.length;i++){var c=this.componentsOnHorizontal[i];if(this.fixedComponentsOnHorizontal[i])for(r=0;r<c.length;r++)this.dummyToNodeForVerticalAlignment.has(c[r])?this.dummyToNodeForVerticalAlignment.get(c[r]).forEach((function(e){t.idToNodeMap.get(e).displacementX=0})):this.idToNodeMap.get(c[r]).displacementX=0;else{var d=0,g=0;for(r=0;r<c.length;r++)this.dummyToNodeForVerticalAlignment.has(c[r])?(d+=(f=this.dummyToNodeForVerticalAlignment.get(c[r])).length*this.idToNodeMap.get(f[0]).displacementX,g+=f.length):(d+=this.idToNodeMap.get(c[r]).displacementX,g++);var u=d/g;for(r=0;r<c.length;r++)this.dummyToNodeForVerticalAlignment.has(c[r])?this.dummyToNodeForVerticalAlignment.get(c[r]).forEach((function(e){t.idToNodeMap.get(e).displacementX=u})):this.idToNodeMap.get(c[r]).displacementX=u}}for(i=0;i<this.componentsOnVertical.length;i++)if(c=this.componentsOnVertical[i],this.fixedComponentsOnVertical[i])for(r=0;r<c.length;r++)this.dummyToNodeForHorizontalAlignment.has(c[r])?this.dummyToNodeForHorizontalAlignment.get(c[r]).forEach((function(e){t.idToNodeMap.get(e).displacementY=0})):this.idToNodeMap.get(c[r]).displacementY=0;else{for(d=0,g=0,r=0;r<c.length;r++){var f;this.dummyToNodeForHorizontalAlignment.has(c[r])?(d+=(f=this.dummyToNodeForHorizontalAlignment.get(c[r])).length*this.idToNodeMap.get(f[0]).displacementY,g+=f.length):(d+=this.idToNodeMap.get(c[r]).displacementY,g++)}for(u=d/g,r=0;r<c.length;r++)this.dummyToNodeForHorizontalAlignment.has(c[r])?this.dummyToNodeForHorizontalAlignment.get(c[r]).forEach((function(e){t.idToNodeMap.get(e).displacementY=u})):this.idToNodeMap.get(c[r]).displacementY=u}}},T.prototype.calculateNodesToApplyGravitationTo=function(){var t,e,i=[],n=this.graphManager.getGraphs(),r=n.length;for(e=0;e<r;e++)(t=n[e]).updateConnected(),t.isConnected||(i=i.concat(t.getNodes()));return i},T.prototype.createBendpoints=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());var e,i=new Set;for(e=0;e<t.length;e++){var n=t[e];if(!i.has(n)){var r=n.getSource(),o=n.getTarget();if(r==o)n.getBendpoints().push(new u),n.getBendpoints().push(new u),this.createDummyNodesForBendpoints(n),i.add(n);else{var s=[];if(s=(s=s.concat(r.getEdgeListToNode(o))).concat(o.getEdgeListToNode(r)),!i.has(s[0])){var a;if(s.length>1)for(a=0;a<s.length;a++){var h=s[a];h.getBendpoints().push(new u),this.createDummyNodesForBendpoints(h)}s.forEach((function(t){i.add(t)}))}}}if(i.size==t.length)break}},T.prototype.positionNodesRadially=function(t){for(var e=new g(0,0),i=Math.ceil(Math.sqrt(t.length)),n=0,r=0,o=0,s=new u(0,0),a=0;a<t.length;a++){a%i==0&&(o=0,r=n,0!=a&&(r+=h.DEFAULT_COMPONENT_SEPERATION),n=0);var l=t[a],c=p.findCenterOfTree(l);e.x=o,e.y=r,(s=T.radialLayout(l,c,e)).y>n&&(n=Math.floor(s.y)),o=Math.floor(s.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(d.WORLD_CENTER_X-s.x/2,d.WORLD_CENTER_Y-s.y/2))},T.radialLayout=function(t,e,i){var n=Math.max(this.maxDiagonalInTree(t),h.DEFAULT_RADIAL_SEPARATION);T.branchRadialLayout(e,null,0,359,0,n);var r=m.calculateBounds(t),o=new E;o.setDeviceOrgX(r.getMinX()),o.setDeviceOrgY(r.getMinY()),o.setWorldOrgX(i.x),o.setWorldOrgY(i.y);for(var s=0;s<t.length;s++)t[s].transform(o);var a=new u(r.getMaxX(),r.getMaxY());return o.inverseTransformPoint(a)},T.branchRadialLayout=function(t,e,i,n,r,o){var s=(n-i+1)/2;s<0&&(s+=180);var a=(s+i)%360*y.TWO_PI/360,h=(Math.cos(a),r*Math.cos(a)),l=r*Math.sin(a);t.setCenter(h,l);var c=[],d=(c=c.concat(t.getEdges())).length;null!=e&&d--;for(var g,u=0,f=c.length,p=t.getEdgesBetween(e);p.length>1;){var v=p[0];p.splice(0,1);var m=c.indexOf(v);m>=0&&c.splice(m,1),f--,d--}g=null!=e?(c.indexOf(p[0])+1)%f:0;for(var E=Math.abs(n-i)/d,N=g;u!=d;N=++N%f){var A=c[N].getOtherEnd(t);if(A!=e){var w=(i+u*E)%360,L=(w+E)%360;T.branchRadialLayout(A,t,w,L,r+o,o),u++}}},T.maxDiagonalInTree=function(t){for(var e=v.MIN_VALUE,i=0;i<t.length;i++){var n=t[i].getDiagonal();n>e&&(e=n)}return e},T.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},T.prototype.groupZeroDegreeMembers=function(){var t=this,e={};this.memberGroups={},this.idToDummyNode={};for(var i=[],n=this.graphManager.getAllNodes(),r=0;r<n.length;r++){var o=(a=n[r]).getParent();0!==this.getNodeDegreeWithChildren(a)||null!=o.id&&this.getToBeTiled(o)||i.push(a)}for(r=0;r<i.length;r++){var a,h=(a=i[r]).getParent().id;void 0===e[h]&&(e[h]=[]),e[h]=e[h].concat(a)}Object.keys(e).forEach((function(i){if(e[i].length>1){var n="DummyCompound_"+i;t.memberGroups[n]=e[i];var r=e[i][0].getParent(),o=new s(t.graphManager);o.id=n,o.paddingLeft=r.paddingLeft||0,o.paddingRight=r.paddingRight||0,o.paddingBottom=r.paddingBottom||0,o.paddingTop=r.paddingTop||0,t.idToDummyNode[n]=o;var a=t.getGraphManager().add(t.newGraph(),o),h=r.getChild();h.add(o);for(var l=0;l<e[i].length;l++){var c=e[i][l];h.remove(c),a.add(c)}}}))},T.prototype.clearCompounds=function(){var t={},e={};this.performDFSOnCompounds();for(var i=0;i<this.compoundOrder.length;i++)e[this.compoundOrder[i].id]=this.compoundOrder[i],t[this.compoundOrder[i].id]=[].concat(this.compoundOrder[i].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[i].getChild()),this.compoundOrder[i].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(t,e)},T.prototype.clearZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach((function(i){var n=t.idToDummyNode[i];if(e[i]=t.tileNodes(t.memberGroups[i],n.paddingLeft+n.paddingRight),n.rect.width=e[i].width,n.rect.height=e[i].height,n.setCenter(e[i].centerX,e[i].centerY),n.labelMarginLeft=0,n.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var r=n.rect.width,o=n.rect.height;n.labelWidth&&("left"==n.labelPosHorizontal?(n.rect.x-=n.labelWidth,n.setWidth(r+n.labelWidth),n.labelMarginLeft=n.labelWidth):"center"==n.labelPosHorizontal&&n.labelWidth>r?(n.rect.x-=(n.labelWidth-r)/2,n.setWidth(n.labelWidth),n.labelMarginLeft=(n.labelWidth-r)/2):"right"==n.labelPosHorizontal&&n.setWidth(r+n.labelWidth)),n.labelHeight&&("top"==n.labelPosVertical?(n.rect.y-=n.labelHeight,n.setHeight(o+n.labelHeight),n.labelMarginTop=n.labelHeight):"center"==n.labelPosVertical&&n.labelHeight>o?(n.rect.y-=(n.labelHeight-o)/2,n.setHeight(n.labelHeight),n.labelMarginTop=(n.labelHeight-o)/2):"bottom"==n.labelPosVertical&&n.setHeight(o+n.labelHeight))}}))},T.prototype.repopulateCompounds=function(){for(var t=this.compoundOrder.length-1;t>=0;t--){var e=this.compoundOrder[t],i=e.id,n=e.paddingLeft,r=e.paddingTop,o=e.labelMarginLeft,s=e.labelMarginTop;this.adjustLocations(this.tiledMemberPack[i],e.rect.x,e.rect.y,n,r,o,s)}},T.prototype.repopulateZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack;Object.keys(e).forEach((function(i){var n=t.idToDummyNode[i],r=n.paddingLeft,o=n.paddingTop,s=n.labelMarginLeft,a=n.labelMarginTop;t.adjustLocations(e[i],n.rect.x,n.rect.y,r,o,s,a)}))},T.prototype.getToBeTiled=function(t){var e=t.id;if(null!=this.toBeTiled[e])return this.toBeTiled[e];var i=t.getChild();if(null==i)return this.toBeTiled[e]=!1,!1;for(var n=i.getNodes(),r=0;r<n.length;r++){var o=n[r];if(this.getNodeDegree(o)>0)return this.toBeTiled[e]=!1,!1;if(null!=o.getChild()){if(!this.getToBeTiled(o))return this.toBeTiled[e]=!1,!1}else this.toBeTiled[o.id]=!1}return this.toBeTiled[e]=!0,!0},T.prototype.getNodeDegree=function(t){t.id;for(var e=t.getEdges(),i=0,n=0;n<e.length;n++){var r=e[n];r.getSource().id!==r.getTarget().id&&(i+=1)}return i},T.prototype.getNodeDegreeWithChildren=function(t){var e=this.getNodeDegree(t);if(null==t.getChild())return e;for(var i=t.getChild().getNodes(),n=0;n<i.length;n++){var r=i[n];e+=this.getNodeDegreeWithChildren(r)}return e},T.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},T.prototype.fillCompexOrderByDFS=function(t){for(var e=0;e<t.length;e++){var i=t[e];null!=i.getChild()&&this.fillCompexOrderByDFS(i.getChild().getNodes()),this.getToBeTiled(i)&&this.compoundOrder.push(i)}},T.prototype.adjustLocations=function(t,e,i,n,r,o,s){i+=r+s;for(var a=e+=n+o,h=0;h<t.rows.length;h++){var l=t.rows[h];e=a;for(var c=0,d=0;d<l.length;d++){var g=l[d];g.rect.x=e,g.rect.y=i,e+=g.rect.width+t.horizontalPadding,g.rect.height>c&&(c=g.rect.height)}i+=c+t.verticalPadding}},T.prototype.tileCompoundMembers=function(t,e){var i=this;this.tiledMemberPack=[],Object.keys(t).forEach((function(n){var r=e[n];if(i.tiledMemberPack[n]=i.tileNodes(t[n],r.paddingLeft+r.paddingRight),r.rect.width=i.tiledMemberPack[n].width,r.rect.height=i.tiledMemberPack[n].height,r.setCenter(i.tiledMemberPack[n].centerX,i.tiledMemberPack[n].centerY),r.labelMarginLeft=0,r.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var o=r.rect.width,s=r.rect.height;r.labelWidth&&("left"==r.labelPosHorizontal?(r.rect.x-=r.labelWidth,r.setWidth(o+r.labelWidth),r.labelMarginLeft=r.labelWidth):"center"==r.labelPosHorizontal&&r.labelWidth>o?(r.rect.x-=(r.labelWidth-o)/2,r.setWidth(r.labelWidth),r.labelMarginLeft=(r.labelWidth-o)/2):"right"==r.labelPosHorizontal&&r.setWidth(o+r.labelWidth)),r.labelHeight&&("top"==r.labelPosVertical?(r.rect.y-=r.labelHeight,r.setHeight(s+r.labelHeight),r.labelMarginTop=r.labelHeight):"center"==r.labelPosVertical&&r.labelHeight>s?(r.rect.y-=(r.labelHeight-s)/2,r.setHeight(r.labelHeight),r.labelMarginTop=(r.labelHeight-s)/2):"bottom"==r.labelPosVertical&&r.setHeight(s+r.labelHeight))}}))},T.prototype.tileNodes=function(t,e){var i=this.tileNodesByFavoringDim(t,e,!0),n=this.tileNodesByFavoringDim(t,e,!1),r=this.getOrgRatio(i);return this.getOrgRatio(n)<r?n:i},T.prototype.getOrgRatio=function(t){var e=t.width/t.height;return e<1&&(e=1/e),e},T.prototype.calcIdealRowWidth=function(t,e){var i=h.TILING_PADDING_VERTICAL,n=h.TILING_PADDING_HORIZONTAL,r=t.length,o=0,s=0,a=0;t.forEach((function(t){o+=t.getWidth(),s+=t.getHeight(),t.getWidth()>a&&(a=t.getWidth())}));var l,c=o/r,d=s/r,g=Math.pow(i-n,2)+4*(c+n)*(d+i)*r,u=(n-i+Math.sqrt(g))/(2*(c+n));e?(l=Math.ceil(u))==u&&l++:l=Math.floor(u);var f=l*(c+n)-n;return a>f&&(f=a),f+=2*n},T.prototype.tileNodesByFavoringDim=function(t,e,i){var n=h.TILING_PADDING_VERTICAL,r=h.TILING_PADDING_HORIZONTAL,o=h.TILING_COMPARE_BY,s={rows:[],rowWidth:[],rowHeight:[],width:0,height:e,verticalPadding:n,horizontalPadding:r,centerX:0,centerY:0};o&&(s.idealRowWidth=this.calcIdealRowWidth(t,i));var a=function(t){return t.rect.width*t.rect.height},l=function(t,e){return a(e)-a(t)};t.sort((function(t,e){var i=l;return s.idealRowWidth?(i=o)(t.id,e.id):i(t,e)}));for(var c=0,d=0,g=0;g<t.length;g++)c+=(u=t[g]).getCenterX(),d+=u.getCenterY();for(s.centerX=c/t.length,s.centerY=d/t.length,g=0;g<t.length;g++){var u=t[g];if(0==s.rows.length)this.insertNodeToRow(s,u,0,e);else if(this.canAddHorizontal(s,u.rect.width,u.rect.height)){var f=s.rows.length-1;s.idealRowWidth||(f=this.getShortestRowIndex(s)),this.insertNodeToRow(s,u,f,e)}else this.insertNodeToRow(s,u,s.rows.length,e);this.shiftToLastRow(s)}return s},T.prototype.insertNodeToRow=function(t,e,i,n){var r=n;i==t.rows.length&&(t.rows.push([]),t.rowWidth.push(r),t.rowHeight.push(0));var o=t.rowWidth[i]+e.rect.width;t.rows[i].length>0&&(o+=t.horizontalPadding),t.rowWidth[i]=o,t.width<o&&(t.width=o);var s=e.rect.height;i>0&&(s+=t.verticalPadding);var a=0;s>t.rowHeight[i]&&(a=t.rowHeight[i],t.rowHeight[i]=s,a=t.rowHeight[i]-a),t.height+=a,t.rows[i].push(e)},T.prototype.getShortestRowIndex=function(t){for(var e=-1,i=Number.MAX_VALUE,n=0;n<t.rows.length;n++)t.rowWidth[n]<i&&(e=n,i=t.rowWidth[n]);return e},T.prototype.getLongestRowIndex=function(t){for(var e=-1,i=Number.MIN_VALUE,n=0;n<t.rows.length;n++)t.rowWidth[n]>i&&(e=n,i=t.rowWidth[n]);return e},T.prototype.canAddHorizontal=function(t,e,i){if(t.idealRowWidth){var n=t.rows.length-1;return t.rowWidth[n]+e+t.horizontalPadding<=t.idealRowWidth}var r=this.getShortestRowIndex(t);if(r<0)return!0;var o=t.rowWidth[r];if(o+t.horizontalPadding+e<=t.width)return!0;var s,a,h=0;return t.rowHeight[r]<i&&r>0&&(h=i+t.verticalPadding-t.rowHeight[r]),s=t.width-o>=e+t.horizontalPadding?(t.height+h)/(o+e+t.horizontalPadding):(t.height+h)/t.width,h=i+t.verticalPadding,(a=t.width<e?(t.height+h)/e:(t.height+h)/t.width)<1&&(a=1/a),s<1&&(s=1/s),s<a},T.prototype.shiftToLastRow=function(t){var e=this.getLongestRowIndex(t),i=t.rowWidth.length-1,n=t.rows[e],r=n[n.length-1],o=r.width+t.horizontalPadding;if(t.width-t.rowWidth[i]>o&&e!=i){n.splice(-1,1),t.rows[i].push(r),t.rowWidth[e]=t.rowWidth[e]-o,t.rowWidth[i]=t.rowWidth[i]+o,t.width=t.rowWidth[instance.getLongestRowIndex(t)];for(var s=Number.MIN_VALUE,a=0;a<n.length;a++)n[a].height>s&&(s=n[a].height);e>0&&(s+=t.verticalPadding);var h=t.rowHeight[e]+t.rowHeight[i];t.rowHeight[e]=s,t.rowHeight[i]<r.height+t.verticalPadding&&(t.rowHeight[i]=r.height+t.verticalPadding);var l=t.rowHeight[e]+t.rowHeight[i];t.height+=l-h,this.shiftToLastRow(t)}},T.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},T.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},T.prototype.reduceTrees=function(){for(var t,e=[],i=!0;i;){var n=this.graphManager.getAllNodes(),r=[];i=!1;for(var o=0;o<n.length;o++)if(1==(t=n[o]).getEdges().length&&!t.getEdges()[0].isInterGraph&&null==t.getChild()){if(h.PURE_INCREMENTAL){var s=t.getEdges()[0].getOtherEnd(t),a=new f(t.getCenterX()-s.getCenterX(),t.getCenterY()-s.getCenterY());r.push([t,t.getEdges()[0],t.getOwner(),a])}else r.push([t,t.getEdges()[0],t.getOwner()]);i=!0}if(1==i){for(var l=[],c=0;c<r.length;c++)1==r[c][0].getEdges().length&&(l.push(r[c]),r[c][0].getOwner().remove(r[c][0]));e.push(l),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=e},T.prototype.growTree=function(t){for(var e,i=t[t.length-1],n=0;n<i.length;n++)e=i[n],this.findPlaceforPrunedNode(e),e[2].add(e[0]),e[2].add(e[1],e[1].source,e[1].target);t.splice(t.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},T.prototype.findPlaceforPrunedNode=function(t){var e,i,n=t[0];if(i=n==t[1].source?t[1].target:t[1].source,h.PURE_INCREMENTAL)n.setCenter(i.getCenterX()+t[3].getWidth(),i.getCenterY()+t[3].getHeight());else{var r=i.startX,o=i.finishX,s=i.startY,a=i.finishY,l=[0,0,0,0];if(s>0)for(var d=r;d<=o;d++)l[0]+=this.grid[d][s-1].length+this.grid[d][s].length-1;if(o<this.grid.length-1)for(d=s;d<=a;d++)l[1]+=this.grid[o+1][d].length+this.grid[o][d].length-1;if(a<this.grid[0].length-1)for(d=r;d<=o;d++)l[2]+=this.grid[d][a+1].length+this.grid[d][a].length-1;if(r>0)for(d=s;d<=a;d++)l[3]+=this.grid[r-1][d].length+this.grid[r][d].length-1;for(var g,u,f=v.MAX_VALUE,p=0;p<l.length;p++)l[p]<f?(f=l[p],g=1,u=p):l[p]==f&&g++;if(3==g&&0==f)0==l[0]&&0==l[1]&&0==l[2]?e=1:0==l[0]&&0==l[1]&&0==l[3]?e=0:0==l[0]&&0==l[2]&&0==l[3]?e=3:0==l[1]&&0==l[2]&&0==l[3]&&(e=2);else if(2==g&&0==f){var y=Math.floor(2*Math.random());e=0==l[0]&&0==l[1]?0==y?0:1:0==l[0]&&0==l[2]?0==y?0:2:0==l[0]&&0==l[3]?0==y?0:3:0==l[1]&&0==l[2]?0==y?1:2:0==l[1]&&0==l[3]?0==y?1:3:0==y?2:3}else e=4==g&&0==f?y=Math.floor(4*Math.random()):u;0==e?n.setCenter(i.getCenterX(),i.getCenterY()-i.getHeight()/2-c.DEFAULT_EDGE_LENGTH-n.getHeight()/2):1==e?n.setCenter(i.getCenterX()+i.getWidth()/2+c.DEFAULT_EDGE_LENGTH+n.getWidth()/2,i.getCenterY()):2==e?n.setCenter(i.getCenterX(),i.getCenterY()+i.getHeight()/2+c.DEFAULT_EDGE_LENGTH+n.getHeight()/2):n.setCenter(i.getCenterX()-i.getWidth()/2-c.DEFAULT_EDGE_LENGTH-n.getWidth()/2,i.getCenterY())}},t.exports=T},991:(t,e,i)=>{var n=i(551).FDLayoutNode,r=i(551).IMath;function o(t,e,i,r){n.call(this,t,e,i,r)}for(var s in o.prototype=Object.create(n.prototype),n)o[s]=n[s];o.prototype.calculateDisplacement=function(){var t=this.graphManager.getLayout();null!=this.getChild()&&this.fixedNodeWeight?(this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementX=t.coolingFactor*t.maxNodeDisplacement*r.sign(this.displacementX)),Math.abs(this.displacementY)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementY=t.coolingFactor*t.maxNodeDisplacement*r.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},o.prototype.propogateDisplacementToChildren=function(t,e){for(var i,n=this.getChild().getNodes(),r=0;r<n.length;r++)null==(i=n[r]).getChild()?(i.displacementX+=t,i.displacementY+=e):i.propogateDisplacementToChildren(t,e)},o.prototype.move=function(){var t=this.graphManager.getLayout();null!=this.child&&0!=this.child.getNodes().length||(this.moveBy(this.displacementX,this.displacementY),t.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},o.prototype.setPred1=function(t){this.pred1=t},o.prototype.getPred1=function(){return pred1},o.prototype.getPred2=function(){return pred2},o.prototype.setNext=function(t){this.next=t},o.prototype.getNext=function(){return next},o.prototype.setProcessed=function(t){this.processed=t},o.prototype.isProcessed=function(){return processed},t.exports=o},902:(t,e,i)=>{function n(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}var r=i(806),o=i(551).LinkedList,s=i(551).Matrix,a=i(551).SVD;function h(){}h.handleConstraints=function(t){var e={};e.fixedNodeConstraint=t.constraints.fixedNodeConstraint,e.alignmentConstraint=t.constraints.alignmentConstraint,e.relativePlacementConstraint=t.constraints.relativePlacementConstraint;for(var i=new Map,h=new Map,l=[],c=[],d=t.getAllNodes(),g=0,u=0;u<d.length;u++){var f=d[u];null==f.getChild()&&(h.set(f.id,g++),l.push(f.getCenterX()),c.push(f.getCenterY()),i.set(f.id,f))}e.relativePlacementConstraint&&e.relativePlacementConstraint.forEach((function(t){t.gap||0==t.gap||(t.left?t.gap=r.DEFAULT_EDGE_LENGTH+i.get(t.left).getWidth()/2+i.get(t.right).getWidth()/2:t.gap=r.DEFAULT_EDGE_LENGTH+i.get(t.top).getHeight()/2+i.get(t.bottom).getHeight()/2)}));var p=function(t){var e=0,i=0;return t.forEach((function(t){e+=l[h.get(t)],i+=c[h.get(t)]})),{x:e/t.size,y:i/t.size}},v=function(t,e,i,r,s){var a=new Map;t.forEach((function(t,e){a.set(e,0)})),t.forEach((function(t,e){t.forEach((function(t){a.set(t.id,a.get(t.id)+1)}))}));var d=new Map,g=new Map,u=new o;a.forEach((function(t,n){0==t?(u.push(n),i||("horizontal"==e?d.set(n,h.has(n)?l[h.get(n)]:r.get(n)):d.set(n,h.has(n)?c[h.get(n)]:r.get(n)))):d.set(n,Number.NEGATIVE_INFINITY),i&&g.set(n,new Set([n]))})),i&&s.forEach((function(t){var n=[];if(t.forEach((function(t){i.has(t)&&n.push(t)})),n.length>0){var o=0;n.forEach((function(t){"horizontal"==e?(d.set(t,h.has(t)?l[h.get(t)]:r.get(t)),o+=d.get(t)):(d.set(t,h.has(t)?c[h.get(t)]:r.get(t)),o+=d.get(t))})),o/=n.length,t.forEach((function(t){i.has(t)||d.set(t,o)}))}else{var s=0;t.forEach((function(t){s+="horizontal"==e?h.has(t)?l[h.get(t)]:r.get(t):h.has(t)?c[h.get(t)]:r.get(t)})),s/=t.length,t.forEach((function(t){d.set(t,s)}))}}));for(var f=function(){var n=u.shift();t.get(n).forEach((function(t){if(d.get(t.id)<d.get(n)+t.gap)if(i&&i.has(t.id)){var o=void 0;if(o="horizontal"==e?h.has(t.id)?l[h.get(t.id)]:r.get(t.id):h.has(t.id)?c[h.get(t.id)]:r.get(t.id),d.set(t.id,o),o<d.get(n)+t.gap){var s=d.get(n)+t.gap-o;g.get(n).forEach((function(t){d.set(t,d.get(t)-s)}))}}else d.set(t.id,d.get(n)+t.gap);a.set(t.id,a.get(t.id)-1),0==a.get(t.id)&&u.push(t.id),i&&g.set(t.id,function(t,e){var i=new Set(t),n=!0,r=!1,o=void 0;try{for(var s,a=e[Symbol.iterator]();!(n=(s=a.next()).done);n=!0){var h=s.value;i.add(h)}}catch(l){r=!0,o=l}finally{try{!n&&a.return&&a.return()}finally{if(r)throw o}}return i}(g.get(n),g.get(t.id)))}))};0!=u.length;)f();if(i){var p=new Set;t.forEach((function(t,e){0==t.length&&p.add(e)}));var v=[];g.forEach((function(t,e){if(p.has(e)){var r=!1,o=!0,s=!1,a=void 0;try{for(var h,l=t[Symbol.iterator]();!(o=(h=l.next()).done);o=!0){var c=h.value;i.has(c)&&(r=!0)}}catch(u){s=!0,a=u}finally{try{!o&&l.return&&l.return()}finally{if(s)throw a}}if(!r){var d=!1,g=void 0;v.forEach((function(e,i){e.has([].concat(n(t))[0])&&(d=!0,g=i)})),d?t.forEach((function(t){v[g].add(t)})):v.push(new Set(t))}}})),v.forEach((function(t,i){var n=Number.POSITIVE_INFINITY,o=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY,a=Number.NEGATIVE_INFINITY,g=!0,u=!1,f=void 0;try{for(var p,v=t[Symbol.iterator]();!(g=(p=v.next()).done);g=!0){var y=p.value,m=void 0;m="horizontal"==e?h.has(y)?l[h.get(y)]:r.get(y):h.has(y)?c[h.get(y)]:r.get(y);var E=d.get(y);m<n&&(n=m),m>s&&(s=m),E<o&&(o=E),E>a&&(a=E)}}catch(C){u=!0,f=C}finally{try{!g&&v.return&&v.return()}finally{if(u)throw f}}var N=(n+s)/2-(o+a)/2,T=!0,A=!1,w=void 0;try{for(var L,I=t[Symbol.iterator]();!(T=(L=I.next()).done);T=!0){var _=L.value;d.set(_,d.get(_)+N)}}catch(C){A=!0,w=C}finally{try{!T&&I.return&&I.return()}finally{if(A)throw w}}}))}return d},y=function(t){var e=0,i=0,n=0,r=0;if(t.forEach((function(t){t.left?l[h.get(t.left)]-l[h.get(t.right)]>=0?e++:i++:c[h.get(t.top)]-c[h.get(t.bottom)]>=0?n++:r++})),e>i&&n>r)for(var o=0;o<h.size;o++)l[o]=-1*l[o],c[o]=-1*c[o];else if(e>i)for(var s=0;s<h.size;s++)l[s]=-1*l[s];else if(n>r)for(var a=0;a<h.size;a++)c[a]=-1*c[a]},m=function(t){var e=[],i=new o,n=new Set,r=0;return t.forEach((function(o,s){if(!n.has(s)){e[r]=[];var a=s;for(i.push(a),n.add(a),e[r].push(a);0!=i.length;)a=i.shift(),t.get(a).forEach((function(t){n.has(t.id)||(i.push(t.id),n.add(t.id),e[r].push(t.id))}));r++}})),e},E=function(t){var e=new Map;return t.forEach((function(t,i){e.set(i,[])})),t.forEach((function(t,i){t.forEach((function(t){e.get(i).push(t),e.get(t.id).push({id:i,gap:t.gap,direction:t.direction})}))})),e},N=function(t){var e=new Map;return t.forEach((function(t,i){e.set(i,[])})),t.forEach((function(t,i){t.forEach((function(t){e.get(t.id).push({id:i,gap:t.gap,direction:t.direction})}))})),e},T=[],A=[],w=!1,L=!1,I=new Set,_=new Map,C=new Map,M=[];if(e.fixedNodeConstraint&&e.fixedNodeConstraint.forEach((function(t){I.add(t.nodeId)})),e.relativePlacementConstraint&&(e.relativePlacementConstraint.forEach((function(t){t.left?(_.has(t.left)?_.get(t.left).push({id:t.right,gap:t.gap,direction:"horizontal"}):_.set(t.left,[{id:t.right,gap:t.gap,direction:"horizontal"}]),_.has(t.right)||_.set(t.right,[])):(_.has(t.top)?_.get(t.top).push({id:t.bottom,gap:t.gap,direction:"vertical"}):_.set(t.top,[{id:t.bottom,gap:t.gap,direction:"vertical"}]),_.has(t.bottom)||_.set(t.bottom,[]))})),C=E(_),M=m(C)),r.TRANSFORM_ON_CONSTRAINT_HANDLING){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>1)e.fixedNodeConstraint.forEach((function(t,e){T[e]=[t.position.x,t.position.y],A[e]=[l[h.get(t.nodeId)],c[h.get(t.nodeId)]]})),w=!0;else if(e.alignmentConstraint)!function(){var t=0;if(e.alignmentConstraint.vertical){for(var i=e.alignmentConstraint.vertical,r=function(e){var r=new Set;i[e].forEach((function(t){r.add(t)}));var o=new Set([].concat(n(r)).filter((function(t){return I.has(t)}))),s=void 0;s=o.size>0?l[h.get(o.values().next().value)]:p(r).x,i[e].forEach((function(e){T[t]=[s,c[h.get(e)]],A[t]=[l[h.get(e)],c[h.get(e)]],t++}))},o=0;o<i.length;o++)r(o);w=!0}if(e.alignmentConstraint.horizontal){for(var s=e.alignmentConstraint.horizontal,a=function(e){var i=new Set;s[e].forEach((function(t){i.add(t)}));var r=new Set([].concat(n(i)).filter((function(t){return I.has(t)}))),o=void 0;o=r.size>0?l[h.get(r.values().next().value)]:p(i).y,s[e].forEach((function(e){T[t]=[l[h.get(e)],o],A[t]=[l[h.get(e)],c[h.get(e)]],t++}))},d=0;d<s.length;d++)a(d);w=!0}e.relativePlacementConstraint&&(L=!0)}();else if(e.relativePlacementConstraint){for(var x=0,O=0,D=0;D<M.length;D++)M[D].length>x&&(x=M[D].length,O=D);if(x<C.size/2)y(e.relativePlacementConstraint),w=!1,L=!1;else{var R=new Map,b=new Map,G=[];M[O].forEach((function(t){_.get(t).forEach((function(e){"horizontal"==e.direction?(R.has(t)?R.get(t).push(e):R.set(t,[e]),R.has(e.id)||R.set(e.id,[]),G.push({left:t,right:e.id})):(b.has(t)?b.get(t).push(e):b.set(t,[e]),b.has(e.id)||b.set(e.id,[]),G.push({top:t,bottom:e.id}))}))})),y(G),L=!1;var F=v(R,"horizontal"),S=v(b,"vertical");M[O].forEach((function(t,e){A[e]=[l[h.get(t)],c[h.get(t)]],T[e]=[],F.has(t)?T[e][0]=F.get(t):T[e][0]=l[h.get(t)],S.has(t)?T[e][1]=S.get(t):T[e][1]=c[h.get(t)]})),w=!0}}if(w){for(var P,U=s.transpose(T),Y=s.transpose(A),k=0;k<U.length;k++)U[k]=s.multGamma(U[k]),Y[k]=s.multGamma(Y[k]);var H=s.multMat(U,s.transpose(Y)),X=a.svd(H);P=s.multMat(X.V,s.transpose(X.U));for(var z=0;z<h.size;z++){var V=[l[z],c[z]],B=[P[0][0],P[1][0]],W=[P[0][1],P[1][1]];l[z]=s.dotProduct(V,B),c[z]=s.dotProduct(V,W)}L&&y(e.relativePlacementConstraint)}}if(r.ENFORCE_CONSTRAINTS){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>0){var j={x:0,y:0};e.fixedNodeConstraint.forEach((function(t,e){var i,n,r={x:l[h.get(t.nodeId)],y:c[h.get(t.nodeId)]},o=t.position,s=(n=r,{x:(i=o).x-n.x,y:i.y-n.y});j.x+=s.x,j.y+=s.y})),j.x/=e.fixedNodeConstraint.length,j.y/=e.fixedNodeConstraint.length,l.forEach((function(t,e){l[e]+=j.x})),c.forEach((function(t,e){c[e]+=j.y})),e.fixedNodeConstraint.forEach((function(t){l[h.get(t.nodeId)]=t.position.x,c[h.get(t.nodeId)]=t.position.y}))}if(e.alignmentConstraint){if(e.alignmentConstraint.vertical)for(var q=e.alignmentConstraint.vertical,$=function(t){var e=new Set;q[t].forEach((function(t){e.add(t)}));var i=new Set([].concat(n(e)).filter((function(t){return I.has(t)}))),r=void 0;r=i.size>0?l[h.get(i.values().next().value)]:p(e).x,e.forEach((function(t){I.has(t)||(l[h.get(t)]=r)}))},K=0;K<q.length;K++)$(K);if(e.alignmentConstraint.horizontal)for(var Z=e.alignmentConstraint.horizontal,Q=function(t){var e=new Set;Z[t].forEach((function(t){e.add(t)}));var i=new Set([].concat(n(e)).filter((function(t){return I.has(t)}))),r=void 0;r=i.size>0?c[h.get(i.values().next().value)]:p(e).y,e.forEach((function(t){I.has(t)||(c[h.get(t)]=r)}))},J=0;J<Z.length;J++)Q(J)}e.relativePlacementConstraint&&function(){var t=new Map,i=new Map,n=new Map,r=new Map,o=new Map,s=new Map,a=new Set,d=new Set;if(I.forEach((function(t){a.add(t),d.add(t)})),e.alignmentConstraint){if(e.alignmentConstraint.vertical)for(var g=e.alignmentConstraint.vertical,u=function(e){n.set("dummy"+e,[]),g[e].forEach((function(i){t.set(i,"dummy"+e),n.get("dummy"+e).push(i),I.has(i)&&a.add("dummy"+e)})),o.set("dummy"+e,l[h.get(g[e][0])])},f=0;f<g.length;f++)u(f);if(e.alignmentConstraint.horizontal)for(var p=e.alignmentConstraint.horizontal,y=function(t){r.set("dummy"+t,[]),p[t].forEach((function(e){i.set(e,"dummy"+t),r.get("dummy"+t).push(e),I.has(e)&&d.add("dummy"+t)})),s.set("dummy"+t,c[h.get(p[t][0])])},T=0;T<p.length;T++)y(T)}var A=new Map,w=new Map,L=function(e){_.get(e).forEach((function(n){var r=void 0,o=void 0;"horizontal"==n.direction?(r=t.get(e)?t.get(e):e,o=t.get(n.id)?{id:t.get(n.id),gap:n.gap,direction:n.direction}:n,A.has(r)?A.get(r).push(o):A.set(r,[o]),A.has(o.id)||A.set(o.id,[])):(r=i.get(e)?i.get(e):e,o=i.get(n.id)?{id:i.get(n.id),gap:n.gap,direction:n.direction}:n,w.has(r)?w.get(r).push(o):w.set(r,[o]),w.has(o.id)||w.set(o.id,[]))}))},C=!0,M=!1,x=void 0;try{for(var O,D=_.keys()[Symbol.iterator]();!(C=(O=D.next()).done);C=!0)L(O.value)}catch(tt){M=!0,x=tt}finally{try{!C&&D.return&&D.return()}finally{if(M)throw x}}var R=E(A),b=E(w),G=m(R),F=m(b),S=N(A),P=N(w),U=[],Y=[];G.forEach((function(t,e){U[e]=[],t.forEach((function(t){0==S.get(t).length&&U[e].push(t)}))})),F.forEach((function(t,e){Y[e]=[],t.forEach((function(t){0==P.get(t).length&&Y[e].push(t)}))}));var k=v(A,"horizontal",a,o,U),H=v(w,"vertical",d,s,Y),X=function(t){n.get(t)?n.get(t).forEach((function(e){l[h.get(e)]=k.get(t)})):l[h.get(t)]=k.get(t)},z=!0,V=!1,B=void 0;try{for(var W,j=k.keys()[Symbol.iterator]();!(z=(W=j.next()).done);z=!0)X(W.value)}catch(tt){V=!0,B=tt}finally{try{!z&&j.return&&j.return()}finally{if(V)throw B}}var q=function(t){r.get(t)?r.get(t).forEach((function(e){c[h.get(e)]=H.get(t)})):c[h.get(t)]=H.get(t)},$=!0,K=!1,Z=void 0;try{for(var Q,J=H.keys()[Symbol.iterator]();!($=(Q=J.next()).done);$=!0)q(Q.value)}catch(tt){K=!0,Z=tt}finally{try{!$&&J.return&&J.return()}finally{if(K)throw Z}}}()}for(var tt=0;tt<d.length;tt++){var et=d[tt];null==et.getChild()&&et.setCenter(l[h.get(et.id)],c[h.get(et.id)])}},t.exports=h},551:e=>{e.exports=t}},i={},n=function t(n){var r=i[n];if(void 0!==r)return r.exports;var o=i[n]={exports:{}};return e[n](o,o.exports,t),o.exports}(45);return n})()},t.exports=n(i(6679))},6527:function(t,e,i){var n;n=function(t){return(()=>{"use strict";var e={658:t=>{t.exports=null!=Object.assign?Object.assign.bind(Object):function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return i.forEach((function(e){Object.keys(e).forEach((function(i){return t[i]=e[i]}))})),t}},548:(t,e,i)=>{var n=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var i=[],n=!0,r=!1,o=void 0;try{for(var s,a=t[Symbol.iterator]();!(n=(s=a.next()).done)&&(i.push(s.value),!e||i.length!==e);n=!0);}catch(h){r=!0,o=h}finally{try{!n&&a.return&&a.return()}finally{if(r)throw o}}return i}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},r=i(140).layoutBase.LinkedList,o={getTopMostNodes:function(t){for(var e={},i=0;i<t.length;i++)e[t[i].id()]=!0;var n=t.filter((function(t,i){"number"==typeof t&&(t=i);for(var n=t.parent()[0];null!=n;){if(e[n.id()])return!1;n=n.parent()[0]}return!0}));return n},connectComponents:function(t,e,i,n){var o=new r,s=new Set,a=[],h=void 0,l=void 0,c=void 0,d=!1,g=1,u=[],f=[],p=function(){var n=t.collection();f.push(n);var r=i[0],p=t.collection();p.merge(r).merge(r.descendants().intersection(e)),a.push(r),p.forEach((function(t){o.push(t),s.add(t),n.merge(t)}));for(var v=function(){r=o.shift();var l=t.collection();r.neighborhood().nodes().forEach((function(t){e.intersection(r.edgesWith(t)).length>0&&l.merge(t)}));for(var c=0;c<l.length;c++){var d=l[c];null==(h=i.intersection(d.union(d.ancestors())))||s.has(h[0])||h.union(h.descendants()).forEach((function(t){o.push(t),s.add(t),n.merge(t),i.has(t)&&a.push(t)}))}};0!=o.length;)v();if(n.forEach((function(t){e.intersection(t.connectedEdges()).forEach((function(t){n.has(t.source())&&n.has(t.target())&&n.merge(t)}))})),a.length==i.length&&(d=!0),!d||d&&g>1){l=a[0],c=l.connectedEdges().length,a.forEach((function(t){t.connectedEdges().length<c&&(c=t.connectedEdges().length,l=t)})),u.push(l.id());var y=t.collection();y.merge(a[0]),a.forEach((function(t){y.merge(t)})),a=[],i=i.difference(y),g++}};do{p()}while(!d);return n&&u.length>0&&n.set("dummy"+(n.size+1),u),f},relocateComponent:function(t,e,i){if(!i.fixedNodeConstraint){var r=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY,s=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY;if("draft"==i.quality){var h=!0,l=!1,c=void 0;try{for(var d,g=e.nodeIndexes[Symbol.iterator]();!(h=(d=g.next()).done);h=!0){var u=d.value,f=n(u,2),p=f[0],v=f[1],y=i.cy.getElementById(p);if(y){var m=y.boundingBox(),E=e.xCoords[v]-m.w/2,N=e.xCoords[v]+m.w/2,T=e.yCoords[v]-m.h/2,A=e.yCoords[v]+m.h/2;E<r&&(r=E),N>o&&(o=N),T<s&&(s=T),A>a&&(a=A)}}}catch(C){l=!0,c=C}finally{try{!h&&g.return&&g.return()}finally{if(l)throw c}}var w=t.x-(o+r)/2,L=t.y-(a+s)/2;e.xCoords=e.xCoords.map((function(t){return t+w})),e.yCoords=e.yCoords.map((function(t){return t+L}))}else{Object.keys(e).forEach((function(t){var i=e[t],n=i.getRect().x,h=i.getRect().x+i.getRect().width,l=i.getRect().y,c=i.getRect().y+i.getRect().height;n<r&&(r=n),h>o&&(o=h),l<s&&(s=l),c>a&&(a=c)}));var I=t.x-(o+r)/2,_=t.y-(a+s)/2;Object.keys(e).forEach((function(t){var i=e[t];i.setCenter(i.getCenterX()+I,i.getCenterY()+_)}))}}},calcBoundingBox:function(t,e,i,n){for(var r=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER,a=Number.MIN_SAFE_INTEGER,h=void 0,l=void 0,c=void 0,d=void 0,g=t.descendants().not(":parent"),u=g.length,f=0;f<u;f++){var p=g[f];r>(h=e[n.get(p.id())]-p.width()/2)&&(r=h),o<(l=e[n.get(p.id())]+p.width()/2)&&(o=l),s>(c=i[n.get(p.id())]-p.height()/2)&&(s=c),a<(d=i[n.get(p.id())]+p.height()/2)&&(a=d)}var v={};return v.topLeftX=r,v.topLeftY=s,v.width=o-r,v.height=a-s,v},calcParentsWithoutChildren:function(t,e){var i=t.collection();return e.nodes(":parent").forEach((function(t){var e=!1;t.children().forEach((function(t){"none"!=t.css("display")&&(e=!0)})),e||i.merge(t)})),i}};t.exports=o},816:(t,e,i)=>{var n=i(548),r=i(140).CoSELayout,o=i(140).CoSENode,s=i(140).layoutBase.PointD,a=i(140).layoutBase.DimensionD,h=i(140).layoutBase.LayoutConstants,l=i(140).layoutBase.FDLayoutConstants,c=i(140).CoSEConstants;t.exports={coseLayout:function(t,e){var i=t.cy,d=t.eles,g=d.nodes(),u=d.edges(),f=void 0,p=void 0,v=void 0,y={};t.randomize&&(f=e.nodeIndexes,p=e.xCoords,v=e.yCoords);var m=function(t){return"function"==typeof t},E=function(t,e){return m(t)?t(e):t},N=n.calcParentsWithoutChildren(i,d);null!=t.nestingFactor&&(c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.nestingFactor),null!=t.gravity&&(c.DEFAULT_GRAVITY_STRENGTH=l.DEFAULT_GRAVITY_STRENGTH=t.gravity),null!=t.numIter&&(c.MAX_ITERATIONS=l.MAX_ITERATIONS=t.numIter),null!=t.gravityRange&&(c.DEFAULT_GRAVITY_RANGE_FACTOR=l.DEFAULT_GRAVITY_RANGE_FACTOR=t.gravityRange),null!=t.gravityCompound&&(c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.gravityCompound),null!=t.gravityRangeCompound&&(c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.gravityRangeCompound),null!=t.initialEnergyOnIncremental&&(c.DEFAULT_COOLING_FACTOR_INCREMENTAL=l.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.initialEnergyOnIncremental),null!=t.tilingCompareBy&&(c.TILING_COMPARE_BY=t.tilingCompareBy),"proof"==t.quality?h.QUALITY=2:h.QUALITY=0,c.NODE_DIMENSIONS_INCLUDE_LABELS=l.NODE_DIMENSIONS_INCLUDE_LABELS=h.NODE_DIMENSIONS_INCLUDE_LABELS=t.nodeDimensionsIncludeLabels,c.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!t.randomize,c.ANIMATE=l.ANIMATE=h.ANIMATE=t.animate,c.TILE=t.tile,c.TILING_PADDING_VERTICAL="function"==typeof t.tilingPaddingVertical?t.tilingPaddingVertical.call():t.tilingPaddingVertical,c.TILING_PADDING_HORIZONTAL="function"==typeof t.tilingPaddingHorizontal?t.tilingPaddingHorizontal.call():t.tilingPaddingHorizontal,c.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!0,c.PURE_INCREMENTAL=!t.randomize,h.DEFAULT_UNIFORM_LEAF_NODE_SIZES=t.uniformNodeDimensions,"transformed"==t.step&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,c.ENFORCE_CONSTRAINTS=!1,c.APPLY_LAYOUT=!1),"enforced"==t.step&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!0,c.APPLY_LAYOUT=!1),"cose"==t.step&&(c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!1,c.APPLY_LAYOUT=!0),"all"==t.step&&(t.randomize?c.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:c.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,c.ENFORCE_CONSTRAINTS=!0,c.APPLY_LAYOUT=!0),t.fixedNodeConstraint||t.alignmentConstraint||t.relativePlacementConstraint?c.TREE_REDUCTION_ON_INCREMENTAL=!1:c.TREE_REDUCTION_ON_INCREMENTAL=!0;var T=new r,A=T.newGraphManager();return function t(e,i,r,h){for(var l=i.length,c=0;c<l;c++){var d=i[c],g=null;0==d.intersection(N).length&&(g=d.children());var u=void 0,m=d.layoutDimensions({nodeDimensionsIncludeLabels:h.nodeDimensionsIncludeLabels});if(null!=d.outerWidth()&&null!=d.outerHeight())if(h.randomize)if(d.isParent()){var T=n.calcBoundingBox(d,p,v,f);u=0==d.intersection(N).length?e.add(new o(r.graphManager,new s(T.topLeftX,T.topLeftY),new a(T.width,T.height))):e.add(new o(r.graphManager,new s(T.topLeftX,T.topLeftY),new a(parseFloat(m.w),parseFloat(m.h))))}else u=e.add(new o(r.graphManager,new s(p[f.get(d.id())]-m.w/2,v[f.get(d.id())]-m.h/2),new a(parseFloat(m.w),parseFloat(m.h))));else u=e.add(new o(r.graphManager,new s(d.position("x")-m.w/2,d.position("y")-m.h/2),new a(parseFloat(m.w),parseFloat(m.h))));else u=e.add(new o(this.graphManager));u.id=d.data("id"),u.nodeRepulsion=E(h.nodeRepulsion,d),u.paddingLeft=parseInt(d.css("padding")),u.paddingTop=parseInt(d.css("padding")),u.paddingRight=parseInt(d.css("padding")),u.paddingBottom=parseInt(d.css("padding")),h.nodeDimensionsIncludeLabels&&(u.labelWidth=d.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,u.labelHeight=d.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,u.labelPosVertical=d.css("text-valign"),u.labelPosHorizontal=d.css("text-halign")),y[d.data("id")]=u,isNaN(u.rect.x)&&(u.rect.x=0),isNaN(u.rect.y)&&(u.rect.y=0),null!=g&&g.length>0&&t(r.getGraphManager().add(r.newGraph(),u),g,r,h)}}(A.addRoot(),n.getTopMostNodes(g),T,t),function(e,i,n){for(var r=0,o=0,s=0;s<n.length;s++){var a=n[s],h=y[a.data("source")],d=y[a.data("target")];if(h&&d&&h!==d&&0==h.getEdgesBetween(d).length){var g=i.add(e.newEdge(),h,d);g.id=a.id(),g.idealLength=E(t.idealEdgeLength,a),g.edgeElasticity=E(t.edgeElasticity,a),r+=g.idealLength,o++}}null!=t.idealEdgeLength&&(o>0?c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=r/o:m(t.idealEdgeLength)?c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=50:c.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=t.idealEdgeLength,c.MIN_REPULSION_DIST=l.MIN_REPULSION_DIST=l.DEFAULT_EDGE_LENGTH/10,c.DEFAULT_RADIAL_SEPARATION=l.DEFAULT_EDGE_LENGTH)}(T,A,u),function(t,e){e.fixedNodeConstraint&&(t.constraints.fixedNodeConstraint=e.fixedNodeConstraint),e.alignmentConstraint&&(t.constraints.alignmentConstraint=e.alignmentConstraint),e.relativePlacementConstraint&&(t.constraints.relativePlacementConstraint=e.relativePlacementConstraint)}(T,t),T.runLayout(),y}}},212:(t,e,i)=>{var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=i(658),o=i(548),s=i(657).spectralLayout,a=i(816).coseLayout,h=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(t){return 4500},idealEdgeLength:function(t){return 50},edgeElasticity:function(t){return.45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),l=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=r({},h,e)}return n(t,[{key:"run",value:function(){var t=this.options,e=t.cy,i=t.eles,n=[],r=[],h=void 0,l=[];!t.fixedNodeConstraint||Array.isArray(t.fixedNodeConstraint)&&0!=t.fixedNodeConstraint.length||(t.fixedNodeConstraint=void 0),t.alignmentConstraint&&(!t.alignmentConstraint.vertical||Array.isArray(t.alignmentConstraint.vertical)&&0!=t.alignmentConstraint.vertical.length||(t.alignmentConstraint.vertical=void 0),!t.alignmentConstraint.horizontal||Array.isArray(t.alignmentConstraint.horizontal)&&0!=t.alignmentConstraint.horizontal.length||(t.alignmentConstraint.horizontal=void 0)),!t.relativePlacementConstraint||Array.isArray(t.relativePlacementConstraint)&&0!=t.relativePlacementConstraint.length||(t.relativePlacementConstraint=void 0),(t.fixedNodeConstraint||t.alignmentConstraint||t.relativePlacementConstraint)&&(t.tile=!1,t.packComponents=!1);var c=void 0,d=!1;if(e.layoutUtilities&&t.packComponents&&((c=e.layoutUtilities("get"))||(c=e.layoutUtilities()),d=!0),i.nodes().length>0)if(d){var g=o.getTopMostNodes(t.eles.nodes());if((h=o.connectComponents(e,t.eles,g)).forEach((function(t){var e=t.boundingBox();l.push({x:e.x1+e.w/2,y:e.y1+e.h/2})})),t.randomize&&h.forEach((function(e){t.eles=e,n.push(s(t))})),"default"==t.quality||"proof"==t.quality){var u=e.collection();if(t.tile){var f=new Map,p=0,v={nodeIndexes:f,xCoords:[],yCoords:[]},y=[];if(h.forEach((function(t,e){0==t.edges().length&&(t.nodes().forEach((function(e,i){u.merge(t.nodes()[i]),e.isParent()||(v.nodeIndexes.set(t.nodes()[i].id(),p++),v.xCoords.push(t.nodes()[0].position().x),v.yCoords.push(t.nodes()[0].position().y))})),y.push(e))})),u.length>1){var m=u.boundingBox();l.push({x:m.x1+m.w/2,y:m.y1+m.h/2}),h.push(u),n.push(v);for(var E=y.length-1;E>=0;E--)h.splice(y[E],1),n.splice(y[E],1),l.splice(y[E],1)}}h.forEach((function(e,i){t.eles=e,r.push(a(t,n[i])),o.relocateComponent(l[i],r[i],t)}))}else h.forEach((function(e,i){o.relocateComponent(l[i],n[i],t)}));var N=new Set;if(h.length>1){var T=[],A=i.filter((function(t){return"none"==t.css("display")}));h.forEach((function(e,i){var s=void 0;if("draft"==t.quality&&(s=n[i].nodeIndexes),e.nodes().not(A).length>0){var a={edges:[],nodes:[]},h=void 0;e.nodes().not(A).forEach((function(e){if("draft"==t.quality)if(e.isParent()){var l=o.calcBoundingBox(e,n[i].xCoords,n[i].yCoords,s);a.nodes.push({x:l.topLeftX,y:l.topLeftY,width:l.width,height:l.height})}else h=s.get(e.id()),a.nodes.push({x:n[i].xCoords[h]-e.boundingbox().w/2,y:n[i].yCoords[h]-e.boundingbox().h/2,width:e.boundingbox().w,height:e.boundingbox().h});else r[i][e.id()]&&a.nodes.push({x:r[i][e.id()].getLeft(),y:r[i][e.id()].getTop(),width:r[i][e.id()].getWidth(),height:r[i][e.id()].getHeight()})})),e.edges().forEach((function(e){var h=e.source(),l=e.target();if("none"!=h.css("display")&&"none"!=l.css("display"))if("draft"==t.quality){var c=s.get(h.id()),d=s.get(l.id()),g=[],u=[];if(h.isParent()){var f=o.calcBoundingBox(h,n[i].xCoords,n[i].yCoords,s);g.push(f.topLeftX+f.width/2),g.push(f.topLeftY+f.height/2)}else g.push(n[i].xCoords[c]),g.push(n[i].yCoords[c]);if(l.isParent()){var p=o.calcBoundingBox(l,n[i].xCoords,n[i].yCoords,s);u.push(p.topLeftX+p.width/2),u.push(p.topLeftY+p.height/2)}else u.push(n[i].xCoords[d]),u.push(n[i].yCoords[d]);a.edges.push({startX:g[0],startY:g[1],endX:u[0],endY:u[1]})}else r[i][h.id()]&&r[i][l.id()]&&a.edges.push({startX:r[i][h.id()].getCenterX(),startY:r[i][h.id()].getCenterY(),endX:r[i][l.id()].getCenterX(),endY:r[i][l.id()].getCenterY()})})),a.nodes.length>0&&(T.push(a),N.add(i))}}));var w=c.packComponents(T,t.randomize).shifts;if("draft"==t.quality)n.forEach((function(t,e){var i=t.xCoords.map((function(t){return t+w[e].dx})),n=t.yCoords.map((function(t){return t+w[e].dy}));t.xCoords=i,t.yCoords=n}));else{var L=0;N.forEach((function(t){Object.keys(r[t]).forEach((function(e){var i=r[t][e];i.setCenter(i.getCenterX()+w[L].dx,i.getCenterY()+w[L].dy)})),L++}))}}}else{var I=t.eles.boundingBox();if(l.push({x:I.x1+I.w/2,y:I.y1+I.h/2}),t.randomize){var _=s(t);n.push(_)}"default"==t.quality||"proof"==t.quality?(r.push(a(t,n[0])),o.relocateComponent(l[0],r[0],t)):o.relocateComponent(l[0],n[0],t)}var C=function(e,i){if("default"==t.quality||"proof"==t.quality){"number"==typeof e&&(e=i);var o=void 0,s=void 0,a=e.data("id");return r.forEach((function(t){a in t&&(o={x:t[a].getRect().getCenterX(),y:t[a].getRect().getCenterY()},s=t[a])})),t.nodeDimensionsIncludeLabels&&(s.labelWidth&&("left"==s.labelPosHorizontal?o.x+=s.labelWidth/2:"right"==s.labelPosHorizontal&&(o.x-=s.labelWidth/2)),s.labelHeight&&("top"==s.labelPosVertical?o.y+=s.labelHeight/2:"bottom"==s.labelPosVertical&&(o.y-=s.labelHeight/2))),null==o&&(o={x:e.position("x"),y:e.position("y")}),{x:o.x,y:o.y}}var h=void 0;return n.forEach((function(t){var i=t.nodeIndexes.get(e.id());null!=i&&(h={x:t.xCoords[i],y:t.yCoords[i]})})),null==h&&(h={x:e.position("x"),y:e.position("y")}),{x:h.x,y:h.y}};if("default"==t.quality||"proof"==t.quality||t.randomize){var M=o.calcParentsWithoutChildren(e,i),x=i.filter((function(t){return"none"==t.css("display")}));t.eles=i.not(x),i.nodes().not(":parent").not(x).layoutPositions(this,t,C),M.length>0&&M.forEach((function(t){t.position(C(t))}))}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),t}();t.exports=l},657:(t,e,i)=>{var n=i(548),r=i(140).layoutBase.Matrix,o=i(140).layoutBase.SVD;t.exports={spectralLayout:function(t){var e=t.cy,i=t.eles,s=i.nodes(),a=i.nodes(":parent"),h=new Map,l=new Map,c=new Map,d=[],g=[],u=[],f=[],p=[],v=[],y=[],m=[],E=void 0,N=1e8,T=1e-9,A=t.piTol,w=t.samplingType,L=t.nodeSeparation,I=void 0,_=function(t,e,i){for(var n=[],r=0,o=0,s=0,a=void 0,h=[],c=0,g=1,u=0;u<E;u++)h[u]=N;for(n[o]=t,h[t]=0;o>=r;){s=n[r++];for(var f=d[s],y=0;y<f.length;y++)h[a=l.get(f[y])]==N&&(h[a]=h[s]+1,n[++o]=a);v[s][e]=h[s]*L}if(i){for(var m=0;m<E;m++)v[m][e]<p[m]&&(p[m]=v[m][e]);for(var T=0;T<E;T++)p[T]>c&&(c=p[T],g=T)}return g};n.connectComponents(e,i,n.getTopMostNodes(s),h),a.forEach((function(t){n.connectComponents(e,i,n.getTopMostNodes(t.descendants().intersection(i)),h)}));for(var C=0,M=0;M<s.length;M++)s[M].isParent()||l.set(s[M].id(),C++);var x=!0,O=!1,D=void 0;try{for(var R,b=h.keys()[Symbol.iterator]();!(x=(R=b.next()).done);x=!0){var G=R.value;l.set(G,C++)}}catch(K){O=!0,D=K}finally{try{!x&&b.return&&b.return()}finally{if(O)throw D}}for(var F=0;F<l.size;F++)d[F]=[];a.forEach((function(t){for(var e=t.children().intersection(i);0==e.nodes(":childless").length;)e=e.nodes()[0].children().intersection(i);var n=0,r=e.nodes(":childless")[0].connectedEdges().length;e.nodes(":childless").forEach((function(t,e){t.connectedEdges().length<r&&(r=t.connectedEdges().length,n=e)})),c.set(t.id(),e.nodes(":childless")[n].id())})),s.forEach((function(t){var e=void 0;e=t.isParent()?l.get(c.get(t.id())):l.get(t.id()),t.neighborhood().nodes().forEach((function(n){i.intersection(t.edgesWith(n)).length>0&&(n.isParent()?d[e].push(c.get(n.id())):d[e].push(n.id()))}))}));var S=function(t){var i=l.get(t),n=void 0;h.get(t).forEach((function(r){n=e.getElementById(r).isParent()?c.get(r):r,d[i].push(n),d[l.get(n)].push(t)}))},P=!0,U=!1,Y=void 0;try{for(var k,H=h.keys()[Symbol.iterator]();!(P=(k=H.next()).done);P=!0)S(k.value)}catch(K){U=!0,Y=K}finally{try{!P&&H.return&&H.return()}finally{if(U)throw Y}}var X=void 0;if((E=l.size)>2){I=E<t.sampleSize?E:t.sampleSize;for(var z=0;z<E;z++)v[z]=[];for(var V=0;V<I;V++)m[V]=[];return"draft"==t.quality||"all"==t.step?(function(t){var e=void 0;if(t){e=Math.floor(Math.random()*E);for(var i=0;i<E;i++)p[i]=N;for(var n=0;n<I;n++)f[n]=e,e=_(e,n,t)}else{!function(){for(var t=0,e=0,i=!1;e<I;){t=Math.floor(Math.random()*E),i=!1;for(var n=0;n<e;n++)if(f[n]==t){i=!0;break}i||(f[e]=t,e++)}}();for(var r=0;r<I;r++)_(f[r],r,t)}for(var o=0;o<E;o++)for(var s=0;s<I;s++)v[o][s]*=v[o][s];for(var a=0;a<I;a++)y[a]=[];for(var h=0;h<I;h++)for(var l=0;l<I;l++)y[h][l]=v[f[l]][h]}(w),function(){for(var t=o.svd(y),e=t.S,i=t.U,n=t.V,s=e[0]*e[0]*e[0],a=[],h=0;h<I;h++){a[h]=[];for(var l=0;l<I;l++)a[h][l]=0,h==l&&(a[h][l]=e[h]/(e[h]*e[h]+s/(e[h]*e[h])))}m=r.multMat(r.multMat(n,a),r.transpose(i))}(),function(){for(var t=void 0,e=void 0,i=[],n=[],o=[],s=[],a=0;a<E;a++)i[a]=Math.random(),n[a]=Math.random();i=r.normalize(i),n=r.normalize(n);for(var h=T,l=T,c=void 0;;){for(var d=0;d<E;d++)o[d]=i[d];if(i=r.multGamma(r.multL(r.multGamma(o),v,m)),t=r.dotProduct(o,i),i=r.normalize(i),h=r.dotProduct(o,i),(c=Math.abs(h/l))<=1+A&&c>=1)break;l=h}for(var f=0;f<E;f++)o[f]=i[f];for(l=T;;){for(var p=0;p<E;p++)s[p]=n[p];if(s=r.minusOp(s,r.multCons(o,r.dotProduct(o,s))),n=r.multGamma(r.multL(r.multGamma(s),v,m)),e=r.dotProduct(s,n),n=r.normalize(n),h=r.dotProduct(s,n),(c=Math.abs(h/l))<=1+A&&c>=1)break;l=h}for(var y=0;y<E;y++)s[y]=n[y];g=r.multCons(o,Math.sqrt(Math.abs(t))),u=r.multCons(s,Math.sqrt(Math.abs(e)))}(),X={nodeIndexes:l,xCoords:g,yCoords:u}):(l.forEach((function(t,i){g.push(e.getElementById(i).position("x")),u.push(e.getElementById(i).position("y"))})),X={nodeIndexes:l,xCoords:g,yCoords:u}),X}var B=l.keys(),W=e.getElementById(B.next().value),j=W.position(),q=W.outerWidth();if(g.push(j.x),u.push(j.y),2==E){var $=e.getElementById(B.next().value).outerWidth();g.push(j.x+q/2+$/2+t.idealEdgeLength),u.push(j.y)}return X={nodeIndexes:l,xCoords:g,yCoords:u}}}},579:(t,e,i)=>{var n=i(212),r=function(t){t&&t("layout","fcose",n)};"undefined"!=typeof cytoscape&&r(cytoscape),t.exports=r},140:e=>{e.exports=t}},i={},n=function t(n){var r=i[n];if(void 0!==r)return r.exports;var o=i[n]={exports:{}};return e[n](o,o.exports,t),o.exports}(579);return n})()},t.exports=n(i(1709))},6679:function(t){var e;e=function(){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.i=function(t){return t},i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=28)}([function(t,e,i){"use strict";function n(){}n.QUALITY=1,n.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,n.DEFAULT_INCREMENTAL=!1,n.DEFAULT_ANIMATION_ON_LAYOUT=!0,n.DEFAULT_ANIMATION_DURING_LAYOUT=!1,n.DEFAULT_ANIMATION_PERIOD=50,n.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,n.DEFAULT_GRAPH_MARGIN=15,n.NODE_DIMENSIONS_INCLUDE_LABELS=!1,n.SIMPLE_NODE_SIZE=40,n.SIMPLE_NODE_HALF_SIZE=n.SIMPLE_NODE_SIZE/2,n.EMPTY_COMPOUND_NODE_SIZE=40,n.MIN_EDGE_LENGTH=1,n.WORLD_BOUNDARY=1e6,n.INITIAL_WORLD_BOUNDARY=n.WORLD_BOUNDARY/1e3,n.WORLD_CENTER_X=1200,n.WORLD_CENTER_Y=900,t.exports=n},function(t,e,i){"use strict";var n=i(2),r=i(8),o=i(9);function s(t,e,i){n.call(this,i),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=i,this.bendpoints=[],this.source=t,this.target=e}for(var a in s.prototype=Object.create(n.prototype),n)s[a]=n[a];s.prototype.getSource=function(){return this.source},s.prototype.getTarget=function(){return this.target},s.prototype.isInterGraph=function(){return this.isInterGraph},s.prototype.getLength=function(){return this.length},s.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},s.prototype.getBendpoints=function(){return this.bendpoints},s.prototype.getLca=function(){return this.lca},s.prototype.getSourceInLca=function(){return this.sourceInLca},s.prototype.getTargetInLca=function(){return this.targetInLca},s.prototype.getOtherEnd=function(t){if(this.source===t)return this.target;if(this.target===t)return this.source;throw"Node is not incident with this edge"},s.prototype.getOtherEndInGraph=function(t,e){for(var i=this.getOtherEnd(t),n=e.getGraphManager().getRoot();;){if(i.getOwner()==e)return i;if(i.getOwner()==n)break;i=i.getOwner().getParent()}return null},s.prototype.updateLength=function(){var t=new Array(4);this.isOverlapingSourceAndTarget=r.getIntersection(this.target.getRect(),this.source.getRect(),t),this.isOverlapingSourceAndTarget||(this.lengthX=t[0]-t[2],this.lengthY=t[1]-t[3],Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},s.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},t.exports=s},function(t,e,i){"use strict";t.exports=function(t){this.vGraphObject=t}},function(t,e,i){"use strict";var n=i(2),r=i(10),o=i(13),s=i(0),a=i(16),h=i(5);function l(t,e,i,s){null==i&&null==s&&(s=e),n.call(this,s),null!=t.graphManager&&(t=t.graphManager),this.estimatedSize=r.MIN_VALUE,this.inclusionTreeDepth=r.MAX_VALUE,this.vGraphObject=s,this.edges=[],this.graphManager=t,this.rect=null!=i&&null!=e?new o(e.x,e.y,i.width,i.height):new o}for(var c in l.prototype=Object.create(n.prototype),n)l[c]=n[c];l.prototype.getEdges=function(){return this.edges},l.prototype.getChild=function(){return this.child},l.prototype.getOwner=function(){return this.owner},l.prototype.getWidth=function(){return this.rect.width},l.prototype.setWidth=function(t){this.rect.width=t},l.prototype.getHeight=function(){return this.rect.height},l.prototype.setHeight=function(t){this.rect.height=t},l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},l.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},l.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)},l.prototype.getRect=function(){return this.rect},l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},l.prototype.setRect=function(t,e){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=e.width,this.rect.height=e.height},l.prototype.setCenter=function(t,e){this.rect.x=t-this.rect.width/2,this.rect.y=e-this.rect.height/2},l.prototype.setLocation=function(t,e){this.rect.x=t,this.rect.y=e},l.prototype.moveBy=function(t,e){this.rect.x+=t,this.rect.y+=e},l.prototype.getEdgeListToNode=function(t){var e=[],i=this;return i.edges.forEach((function(n){if(n.target==t){if(n.source!=i)throw"Incorrect edge source!";e.push(n)}})),e},l.prototype.getEdgesBetween=function(t){var e=[],i=this;return i.edges.forEach((function(n){if(n.source!=i&&n.target!=i)throw"Incorrect edge source and/or target";n.target!=t&&n.source!=t||e.push(n)})),e},l.prototype.getNeighborsList=function(){var t=new Set,e=this;return e.edges.forEach((function(i){if(i.source==e)t.add(i.target);else{if(i.target!=e)throw"Incorrect incidency!";t.add(i.source)}})),t},l.prototype.withChildren=function(){var t=new Set;if(t.add(this),null!=this.child)for(var e=this.child.getNodes(),i=0;i<e.length;i++)e[i].withChildren().forEach((function(e){t.add(e)}));return t},l.prototype.getNoOfChildren=function(){var t=0;if(null==this.child)t=1;else for(var e=this.child.getNodes(),i=0;i<e.length;i++)t+=e[i].getNoOfChildren();return 0==t&&(t=1),t},l.prototype.getEstimatedSize=function(){if(this.estimatedSize==r.MIN_VALUE)throw"assert failed";return this.estimatedSize},l.prototype.calcEstimatedSize=function(){return null==this.child?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},l.prototype.scatter=function(){var t,e,i=-s.INITIAL_WORLD_BOUNDARY,n=s.INITIAL_WORLD_BOUNDARY;t=s.WORLD_CENTER_X+a.nextDouble()*(n-i)+i;var r=-s.INITIAL_WORLD_BOUNDARY,o=s.INITIAL_WORLD_BOUNDARY;e=s.WORLD_CENTER_Y+a.nextDouble()*(o-r)+r,this.rect.x=t,this.rect.y=e},l.prototype.updateBounds=function(){if(null==this.getChild())throw"assert failed";if(0!=this.getChild().getNodes().length){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),s.NODE_DIMENSIONS_INCLUDE_LABELS){var e=t.getRight()-t.getLeft(),i=t.getBottom()-t.getTop();this.labelWidth&&("left"==this.labelPosHorizontal?(this.rect.x-=this.labelWidth,this.setWidth(e+this.labelWidth)):"center"==this.labelPosHorizontal&&this.labelWidth>e?(this.rect.x-=(this.labelWidth-e)/2,this.setWidth(this.labelWidth)):"right"==this.labelPosHorizontal&&this.setWidth(e+this.labelWidth)),this.labelHeight&&("top"==this.labelPosVertical?(this.rect.y-=this.labelHeight,this.setHeight(i+this.labelHeight)):"center"==this.labelPosVertical&&this.labelHeight>i?(this.rect.y-=(this.labelHeight-i)/2,this.setHeight(this.labelHeight)):"bottom"==this.labelPosVertical&&this.setHeight(i+this.labelHeight))}}},l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==r.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},l.prototype.transform=function(t){var e=this.rect.x;e>s.WORLD_BOUNDARY?e=s.WORLD_BOUNDARY:e<-s.WORLD_BOUNDARY&&(e=-s.WORLD_BOUNDARY);var i=this.rect.y;i>s.WORLD_BOUNDARY?i=s.WORLD_BOUNDARY:i<-s.WORLD_BOUNDARY&&(i=-s.WORLD_BOUNDARY);var n=new h(e,i),r=t.inverseTransformPoint(n);this.setLocation(r.x,r.y)},l.prototype.getLeft=function(){return this.rect.x},l.prototype.getRight=function(){return this.rect.x+this.rect.width},l.prototype.getTop=function(){return this.rect.y},l.prototype.getBottom=function(){return this.rect.y+this.rect.height},l.prototype.getParent=function(){return null==this.owner?null:this.owner.getParent()},t.exports=l},function(t,e,i){"use strict";var n=i(0);function r(){}for(var o in n)r[o]=n[o];r.MAX_ITERATIONS=2500,r.DEFAULT_EDGE_LENGTH=50,r.DEFAULT_SPRING_STRENGTH=.45,r.DEFAULT_REPULSION_STRENGTH=4500,r.DEFAULT_GRAVITY_STRENGTH=.4,r.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,r.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,r.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,r.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,r.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,r.COOLING_ADAPTATION_FACTOR=.33,r.ADAPTATION_LOWER_NODE_LIMIT=1e3,r.ADAPTATION_UPPER_NODE_LIMIT=5e3,r.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,r.MAX_NODE_DISPLACEMENT=3*r.MAX_NODE_DISPLACEMENT_INCREMENTAL,r.MIN_REPULSION_DIST=r.DEFAULT_EDGE_LENGTH/10,r.CONVERGENCE_CHECK_PERIOD=100,r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,r.MIN_EDGE_LENGTH=1,r.GRID_CALCULATION_CHECK_PERIOD=10,t.exports=r},function(t,e,i){"use strict";function n(t,e){null==t&&null==e?(this.x=0,this.y=0):(this.x=t,this.y=e)}n.prototype.getX=function(){return this.x},n.prototype.getY=function(){return this.y},n.prototype.setX=function(t){this.x=t},n.prototype.setY=function(t){this.y=t},n.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},n.prototype.getCopy=function(){return new n(this.x,this.y)},n.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},t.exports=n},function(t,e,i){"use strict";var n=i(2),r=i(10),o=i(0),s=i(7),a=i(3),h=i(1),l=i(13),c=i(12),d=i(11);function g(t,e,i){n.call(this,i),this.estimatedSize=r.MIN_VALUE,this.margin=o.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=t,null!=e&&e instanceof s?this.graphManager=e:null!=e&&e instanceof Layout&&(this.graphManager=e.graphManager)}for(var u in g.prototype=Object.create(n.prototype),n)g[u]=n[u];g.prototype.getNodes=function(){return this.nodes},g.prototype.getEdges=function(){return this.edges},g.prototype.getGraphManager=function(){return this.graphManager},g.prototype.getParent=function(){return this.parent},g.prototype.getLeft=function(){return this.left},g.prototype.getRight=function(){return this.right},g.prototype.getTop=function(){return this.top},g.prototype.getBottom=function(){return this.bottom},g.prototype.isConnected=function(){return this.isConnected},g.prototype.add=function(t,e,i){if(null==e&&null==i){var n=t;if(null==this.graphManager)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(n)>-1)throw"Node already in graph!";return n.owner=this,this.getNodes().push(n),n}var r=t;if(!(this.getNodes().indexOf(e)>-1&&this.getNodes().indexOf(i)>-1))throw"Source or target not in graph!";if(e.owner!=i.owner||e.owner!=this)throw"Both owners must be this graph!";return e.owner!=i.owner?null:(r.source=e,r.target=i,r.isInterGraph=!1,this.getEdges().push(r),e.edges.push(r),i!=e&&i.edges.push(r),r)},g.prototype.remove=function(t){var e=t;if(t instanceof a){if(null==e)throw"Node is null!";if(null==e.owner||e.owner!=this)throw"Owner graph is invalid!";if(null==this.graphManager)throw"Owner graph manager is invalid!";for(var i=e.edges.slice(),n=i.length,r=0;r<n;r++)(o=i[r]).isInterGraph?this.graphManager.remove(o):o.source.owner.remove(o);if(-1==(s=this.nodes.indexOf(e)))throw"Node not in owner node list!";this.nodes.splice(s,1)}else if(t instanceof h){var o;if(null==(o=t))throw"Edge is null!";if(null==o.source||null==o.target)throw"Source and/or target is null!";if(null==o.source.owner||null==o.target.owner||o.source.owner!=this||o.target.owner!=this)throw"Source and/or target owner is invalid!";var s,l=o.source.edges.indexOf(o),c=o.target.edges.indexOf(o);if(!(l>-1&&c>-1))throw"Source and/or target doesn't know this edge!";if(o.source.edges.splice(l,1),o.target!=o.source&&o.target.edges.splice(c,1),-1==(s=o.source.owner.getEdges().indexOf(o)))throw"Not in owner's edge list!";o.source.owner.getEdges().splice(s,1)}},g.prototype.updateLeftTop=function(){for(var t,e,i,n=r.MAX_VALUE,o=r.MAX_VALUE,s=this.getNodes(),a=s.length,h=0;h<a;h++){var l=s[h];n>(t=l.getTop())&&(n=t),o>(e=l.getLeft())&&(o=e)}return n==r.MAX_VALUE?null:(i=null!=s[0].getParent().paddingLeft?s[0].getParent().paddingLeft:this.margin,this.left=o-i,this.top=n-i,new c(this.left,this.top))},g.prototype.updateBounds=function(t){for(var e,i,n,o,s,a=r.MAX_VALUE,h=-r.MAX_VALUE,c=r.MAX_VALUE,d=-r.MAX_VALUE,g=this.nodes,u=g.length,f=0;f<u;f++){var p=g[f];t&&null!=p.child&&p.updateBounds(),a>(e=p.getLeft())&&(a=e),h<(i=p.getRight())&&(h=i),c>(n=p.getTop())&&(c=n),d<(o=p.getBottom())&&(d=o)}var v=new l(a,c,h-a,d-c);a==r.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),s=null!=g[0].getParent().paddingLeft?g[0].getParent().paddingLeft:this.margin,this.left=v.x-s,this.right=v.x+v.width+s,this.top=v.y-s,this.bottom=v.y+v.height+s},g.calculateBounds=function(t){for(var e,i,n,o,s=r.MAX_VALUE,a=-r.MAX_VALUE,h=r.MAX_VALUE,c=-r.MAX_VALUE,d=t.length,g=0;g<d;g++){var u=t[g];s>(e=u.getLeft())&&(s=e),a<(i=u.getRight())&&(a=i),h>(n=u.getTop())&&(h=n),c<(o=u.getBottom())&&(c=o)}return new l(s,h,a-s,c-h)},g.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},g.prototype.getEstimatedSize=function(){if(this.estimatedSize==r.MIN_VALUE)throw"assert failed";return this.estimatedSize},g.prototype.calcEstimatedSize=function(){for(var t=0,e=this.nodes,i=e.length,n=0;n<i;n++)t+=e[n].calcEstimatedSize();return this.estimatedSize=0==t?o.EMPTY_COMPOUND_NODE_SIZE:t/Math.sqrt(this.nodes.length),this.estimatedSize},g.prototype.updateConnected=function(){var t=this;if(0!=this.nodes.length){var e,i,n=new d,r=new Set,o=this.nodes[0];for(o.withChildren().forEach((function(t){n.push(t),r.add(t)}));0!==n.length;)for(var s=(e=(o=n.shift()).getEdges()).length,a=0;a<s;a++)null==(i=e[a].getOtherEndInGraph(o,this))||r.has(i)||i.withChildren().forEach((function(t){n.push(t),r.add(t)}));if(this.isConnected=!1,r.size>=this.nodes.length){var h=0;r.forEach((function(e){e.owner==t&&h++})),h==this.nodes.length&&(this.isConnected=!0)}}else this.isConnected=!0},t.exports=g},function(t,e,i){"use strict";var n,r=i(1);function o(t){n=i(6),this.layout=t,this.graphs=[],this.edges=[]}o.prototype.addRoot=function(){var t=this.layout.newGraph(),e=this.layout.newNode(null),i=this.add(t,e);return this.setRootGraph(i),this.rootGraph},o.prototype.add=function(t,e,i,n,r){if(null==i&&null==n&&null==r){if(null==t)throw"Graph is null!";if(null==e)throw"Parent node is null!";if(this.graphs.indexOf(t)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(t),null!=t.parent)throw"Already has a parent!";if(null!=e.child)throw"Already has a child!";return t.parent=e,e.child=t,t}r=i,i=t;var o=(n=e).getOwner(),s=r.getOwner();if(null==o||o.getGraphManager()!=this)throw"Source not in this graph mgr!";if(null==s||s.getGraphManager()!=this)throw"Target not in this graph mgr!";if(o==s)return i.isInterGraph=!1,o.add(i,n,r);if(i.isInterGraph=!0,i.source=n,i.target=r,this.edges.indexOf(i)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(i),null==i.source||null==i.target)throw"Edge source and/or target is null!";if(-1!=i.source.edges.indexOf(i)||-1!=i.target.edges.indexOf(i))throw"Edge already in source and/or target incidency list!";return i.source.edges.push(i),i.target.edges.push(i),i},o.prototype.remove=function(t){if(t instanceof n){var e=t;if(e.getGraphManager()!=this)throw"Graph not in this graph mgr";if(e!=this.rootGraph&&(null==e.parent||e.parent.graphManager!=this))throw"Invalid parent node!";for(var i,o=[],s=(o=o.concat(e.getEdges())).length,a=0;a<s;a++)i=o[a],e.remove(i);var h,l=[];for(s=(l=l.concat(e.getNodes())).length,a=0;a<s;a++)h=l[a],e.remove(h);e==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(e);this.graphs.splice(c,1),e.parent=null}else if(t instanceof r){if(null==(i=t))throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(null==i.source||null==i.target)throw"Source and/or target is null!";if(-1==i.source.edges.indexOf(i)||-1==i.target.edges.indexOf(i))throw"Source and/or target doesn't know this edge!";if(c=i.source.edges.indexOf(i),i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),null==i.source.owner||null==i.source.owner.getGraphManager())throw"Edge owner graph or owner graph manager is null!";if(-1==i.source.owner.getGraphManager().edges.indexOf(i))throw"Not in owner graph manager's edge list!";c=i.source.owner.getGraphManager().edges.indexOf(i),i.source.owner.getGraphManager().edges.splice(c,1)}},o.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},o.prototype.getGraphs=function(){return this.graphs},o.prototype.getAllNodes=function(){if(null==this.allNodes){for(var t=[],e=this.getGraphs(),i=e.length,n=0;n<i;n++)t=t.concat(e[n].getNodes());this.allNodes=t}return this.allNodes},o.prototype.resetAllNodes=function(){this.allNodes=null},o.prototype.resetAllEdges=function(){this.allEdges=null},o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},o.prototype.getAllEdges=function(){if(null==this.allEdges){for(var t=[],e=this.getGraphs(),i=(e.length,0);i<e.length;i++)t=t.concat(e[i].getEdges());t=t.concat(this.edges),this.allEdges=t}return this.allEdges},o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},o.prototype.setAllNodesToApplyGravitation=function(t){if(null!=this.allNodesToApplyGravitation)throw"assert failed";this.allNodesToApplyGravitation=t},o.prototype.getRoot=function(){return this.rootGraph},o.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=t,null==t.parent&&(t.parent=this.layout.newNode("Root node"))},o.prototype.getLayout=function(){return this.layout},o.prototype.isOneAncestorOfOther=function(t,e){if(null==t||null==e)throw"assert failed";if(t==e)return!0;for(var i,n=t.getOwner();null!=(i=n.getParent());){if(i==e)return!0;if(null==(n=i.getOwner()))break}for(n=e.getOwner();null!=(i=n.getParent());){if(i==t)return!0;if(null==(n=i.getOwner()))break}return!1},o.prototype.calcLowestCommonAncestors=function(){for(var t,e,i,n,r,o=this.getAllEdges(),s=o.length,a=0;a<s;a++)if(e=(t=o[a]).source,i=t.target,t.lca=null,t.sourceInLca=e,t.targetInLca=i,e!=i){for(n=e.getOwner();null==t.lca;){for(t.targetInLca=i,r=i.getOwner();null==t.lca;){if(r==n){t.lca=r;break}if(r==this.rootGraph)break;if(null!=t.lca)throw"assert failed";t.targetInLca=r.getParent(),r=t.targetInLca.getOwner()}if(n==this.rootGraph)break;null==t.lca&&(t.sourceInLca=n.getParent(),n=t.sourceInLca.getOwner())}if(null==t.lca)throw"assert failed"}else t.lca=e.getOwner()},o.prototype.calcLowestCommonAncestor=function(t,e){if(t==e)return t.getOwner();for(var i=t.getOwner();null!=i;){for(var n=e.getOwner();null!=n;){if(n==i)return n;n=n.getParent().getOwner()}i=i.getParent().getOwner()}return i},o.prototype.calcInclusionTreeDepths=function(t,e){var i;null==t&&null==e&&(t=this.rootGraph,e=1);for(var n=t.getNodes(),r=n.length,o=0;o<r;o++)(i=n[o]).inclusionTreeDepth=e,null!=i.child&&this.calcInclusionTreeDepths(i.child,e+1)},o.prototype.includesInvalidEdge=function(){for(var t,e=[],i=this.edges.length,n=0;n<i;n++)t=this.edges[n],this.isOneAncestorOfOther(t.source,t.target)&&e.push(t);for(n=0;n<e.length;n++)this.remove(e[n]);return!1},t.exports=o},function(t,e,i){"use strict";var n=i(12);function r(){}r.calcSeparationAmount=function(t,e,i,n){if(!t.intersects(e))throw"assert failed";var r=new Array(2);this.decideDirectionsForOverlappingNodes(t,e,r),i[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x),i[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y),t.getX()<=e.getX()&&t.getRight()>=e.getRight()?i[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight()):e.getX()<=t.getX()&&e.getRight()>=t.getRight()&&(i[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())),t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()?i[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom()):e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()&&(i[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom()));var o=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()&&(o=1);var s=o*i[0],a=i[1]/o;i[0]<a?a=i[0]:s=i[1],i[0]=-1*r[0]*(a/2+n),i[1]=-1*r[1]*(s/2+n)},r.decideDirectionsForOverlappingNodes=function(t,e,i){t.getCenterX()<e.getCenterX()?i[0]=-1:i[0]=1,t.getCenterY()<e.getCenterY()?i[1]=-1:i[1]=1},r.getIntersection2=function(t,e,i){var n=t.getCenterX(),r=t.getCenterY(),o=e.getCenterX(),s=e.getCenterY();if(t.intersects(e))return i[0]=n,i[1]=r,i[2]=o,i[3]=s,!0;var a=t.getX(),h=t.getY(),l=t.getRight(),c=t.getX(),d=t.getBottom(),g=t.getRight(),u=t.getWidthHalf(),f=t.getHeightHalf(),p=e.getX(),v=e.getY(),y=e.getRight(),m=e.getX(),E=e.getBottom(),N=e.getRight(),T=e.getWidthHalf(),A=e.getHeightHalf(),w=!1,L=!1;if(n===o){if(r>s)return i[0]=n,i[1]=h,i[2]=o,i[3]=E,!1;if(r<s)return i[0]=n,i[1]=d,i[2]=o,i[3]=v,!1}else if(r===s){if(n>o)return i[0]=a,i[1]=r,i[2]=y,i[3]=s,!1;if(n<o)return i[0]=l,i[1]=r,i[2]=p,i[3]=s,!1}else{var I=t.height/t.width,_=e.height/e.width,C=(s-r)/(o-n),M=void 0,x=void 0,O=void 0,D=void 0,R=void 0,b=void 0;if(-I===C?n>o?(i[0]=c,i[1]=d,w=!0):(i[0]=l,i[1]=h,w=!0):I===C&&(n>o?(i[0]=a,i[1]=h,w=!0):(i[0]=g,i[1]=d,w=!0)),-_===C?o>n?(i[2]=m,i[3]=E,L=!0):(i[2]=y,i[3]=v,L=!0):_===C&&(o>n?(i[2]=p,i[3]=v,L=!0):(i[2]=N,i[3]=E,L=!0)),w&&L)return!1;if(n>o?r>s?(M=this.getCardinalDirection(I,C,4),x=this.getCardinalDirection(_,C,2)):(M=this.getCardinalDirection(-I,C,3),x=this.getCardinalDirection(-_,C,1)):r>s?(M=this.getCardinalDirection(-I,C,1),x=this.getCardinalDirection(-_,C,3)):(M=this.getCardinalDirection(I,C,2),x=this.getCardinalDirection(_,C,4)),!w)switch(M){case 1:D=h,O=n+-f/C,i[0]=O,i[1]=D;break;case 2:O=g,D=r+u*C,i[0]=O,i[1]=D;break;case 3:D=d,O=n+f/C,i[0]=O,i[1]=D;break;case 4:O=c,D=r+-u*C,i[0]=O,i[1]=D}if(!L)switch(x){case 1:b=v,R=o+-A/C,i[2]=R,i[3]=b;break;case 2:R=N,b=s+T*C,i[2]=R,i[3]=b;break;case 3:b=E,R=o+A/C,i[2]=R,i[3]=b;break;case 4:R=m,b=s+-T*C,i[2]=R,i[3]=b}}return!1},r.getCardinalDirection=function(t,e,i){return t>e?i:1+i%4},r.getIntersection=function(t,e,i,r){if(null==r)return this.getIntersection2(t,e,i);var o,s,a,h,l,c,d,g=t.x,u=t.y,f=e.x,p=e.y,v=i.x,y=i.y,m=r.x,E=r.y;return 0===(d=(o=p-u)*(h=v-m)-(s=E-y)*(a=g-f))?null:new n((a*(c=m*y-v*E)-h*(l=f*u-g*p))/d,(s*l-o*c)/d)},r.angleOfVector=function(t,e,i,n){var r=void 0;return t!==i?(r=Math.atan((n-e)/(i-t)),i<t?r+=Math.PI:n<e&&(r+=this.TWO_PI)):r=n<e?this.ONE_AND_HALF_PI:this.HALF_PI,r},r.doIntersect=function(t,e,i,n){var r=t.x,o=t.y,s=e.x,a=e.y,h=i.x,l=i.y,c=n.x,d=n.y,g=(s-r)*(d-l)-(c-h)*(a-o);if(0===g)return!1;var u=((d-l)*(c-r)+(h-c)*(d-o))/g,f=((o-a)*(c-r)+(s-r)*(d-o))/g;return 0<u&&u<1&&0<f&&f<1},r.findCircleLineIntersections=function(t,e,i,n,r,o,s){var a=(i-t)*(i-t)+(n-e)*(n-e),h=2*((t-r)*(i-t)+(e-o)*(n-e)),l=(t-r)*(t-r)+(e-o)*(e-o)-s*s;if(h*h-4*a*l>=0){var c=(-h+Math.sqrt(h*h-4*a*l))/(2*a),d=(-h-Math.sqrt(h*h-4*a*l))/(2*a);return c>=0&&c<=1?[c]:d>=0&&d<=1?[d]:null}return null},r.HALF_PI=.5*Math.PI,r.ONE_AND_HALF_PI=1.5*Math.PI,r.TWO_PI=2*Math.PI,r.THREE_PI=3*Math.PI,t.exports=r},function(t,e,i){"use strict";function n(){}n.sign=function(t){return t>0?1:t<0?-1:0},n.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},n.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},t.exports=n},function(t,e,i){"use strict";function n(){}n.MAX_VALUE=2147483647,n.MIN_VALUE=-2147483648,t.exports=n},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=function(t){return{value:t,next:null,prev:null}},o=function(t,e,i,n){return null!==t?t.next=e:n.head=e,null!==i?i.prev=e:n.tail=e,e.prev=t,e.next=i,n.length++,e},s=function(t,e){var i=t.prev,n=t.next;return null!==i?i.next=n:e.head=n,null!==n?n.prev=i:e.tail=i,t.prev=t.next=null,e.length--,t},a=function(){function t(e){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.length=0,this.head=null,this.tail=null,null!=e&&e.forEach((function(t){return i.push(t)}))}return n(t,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,e){return o(e.prev,r(t),e,this)}},{key:"insertAfter",value:function(t,e){return o(e,r(t),e.next,this)}},{key:"insertNodeBefore",value:function(t,e){return o(e.prev,t,e,this)}},{key:"insertNodeAfter",value:function(t,e){return o(e,t,e.next,this)}},{key:"push",value:function(t){return o(this.tail,r(t),null,this)}},{key:"unshift",value:function(t){return o(null,r(t),this.head,this)}},{key:"remove",value:function(t){return s(t,this)}},{key:"pop",value:function(){return s(this.tail,this).value}},{key:"popNode",value:function(){return s(this.tail,this)}},{key:"shift",value:function(){return s(this.head,this).value}},{key:"shiftNode",value:function(){return s(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var e=1,i=this.head;e<t;)i=i.next,e++;return i.value}}},{key:"set_object_at",value:function(t,e){if(t<=this.length()){for(var i=1,n=this.head;i<t;)n=n.next,i++;n.value=e}}}]),t}();t.exports=a},function(t,e,i){"use strict";function n(t,e,i){this.x=null,this.y=null,null==t&&null==e&&null==i?(this.x=0,this.y=0):"number"==typeof t&&"number"==typeof e&&null==i?(this.x=t,this.y=e):"Point"==t.constructor.name&&null==e&&null==i&&(i=t,this.x=i.x,this.y=i.y)}n.prototype.getX=function(){return this.x},n.prototype.getY=function(){return this.y},n.prototype.getLocation=function(){return new n(this.x,this.y)},n.prototype.setLocation=function(t,e,i){"Point"==t.constructor.name&&null==e&&null==i?(i=t,this.setLocation(i.x,i.y)):"number"==typeof t&&"number"==typeof e&&null==i&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},n.prototype.move=function(t,e){this.x=t,this.y=e},n.prototype.translate=function(t,e){this.x+=t,this.y+=e},n.prototype.equals=function(t){if("Point"==t.constructor.name){var e=t;return this.x==e.x&&this.y==e.y}return this==t},n.prototype.toString=function(){return(new n).constructor.name+"[x="+this.x+",y="+this.y+"]"},t.exports=n},function(t,e,i){"use strict";function n(t,e,i,n){this.x=0,this.y=0,this.width=0,this.height=0,null!=t&&null!=e&&null!=i&&null!=n&&(this.x=t,this.y=e,this.width=i,this.height=n)}n.prototype.getX=function(){return this.x},n.prototype.setX=function(t){this.x=t},n.prototype.getY=function(){return this.y},n.prototype.setY=function(t){this.y=t},n.prototype.getWidth=function(){return this.width},n.prototype.setWidth=function(t){this.width=t},n.prototype.getHeight=function(){return this.height},n.prototype.setHeight=function(t){this.height=t},n.prototype.getRight=function(){return this.x+this.width},n.prototype.getBottom=function(){return this.y+this.height},n.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},n.prototype.getCenterX=function(){return this.x+this.width/2},n.prototype.getMinX=function(){return this.getX()},n.prototype.getMaxX=function(){return this.getX()+this.width},n.prototype.getCenterY=function(){return this.y+this.height/2},n.prototype.getMinY=function(){return this.getY()},n.prototype.getMaxY=function(){return this.getY()+this.height},n.prototype.getWidthHalf=function(){return this.width/2},n.prototype.getHeightHalf=function(){return this.height/2},t.exports=n},function(t,e,i){"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function r(){}r.lastID=0,r.createID=function(t){return r.isPrimitive(t)?t:(null!=t.uniqueID||(t.uniqueID=r.getString(),r.lastID++),t.uniqueID)},r.getString=function(t){return null==t&&(t=r.lastID),"Object#"+t},r.isPrimitive=function(t){var e=void 0===t?"undefined":n(t);return null==t||"object"!=e&&"function"!=e},t.exports=r},function(t,e,i){"use strict";function n(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}var r=i(0),o=i(7),s=i(3),a=i(1),h=i(6),l=i(5),c=i(17),d=i(29);function g(t){d.call(this),this.layoutQuality=r.QUALITY,this.createBendsAsNeeded=r.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=r.DEFAULT_INCREMENTAL,this.animationOnLayout=r.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=r.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=r.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=r.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new o(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,null!=t&&(this.isRemoteUse=t)}g.RANDOM_SEED=1,g.prototype=Object.create(d.prototype),g.prototype.getGraphManager=function(){return this.graphManager},g.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},g.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},g.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},g.prototype.newGraphManager=function(){var t=new o(this);return this.graphManager=t,t},g.prototype.newGraph=function(t){return new h(null,this.graphManager,t)},g.prototype.newNode=function(t){return new s(this.graphManager,t)},g.prototype.newEdge=function(t){return new a(null,null,t)},g.prototype.checkLayoutSuccess=function(){return null==this.graphManager.getRoot()||0==this.graphManager.getRoot().getNodes().length||this.graphManager.includesInvalidEdge()},g.prototype.runLayout=function(){var t;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),t=!this.checkLayoutSuccess()&&this.layout(),"during"!==r.ANIMATE&&(t&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,t)},g.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},g.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var t=this.graphManager.getAllEdges(),e=0;e<t.length;e++)t[e];var i=this.graphManager.getRoot().getNodes();for(e=0;e<i.length;e++)i[e];this.update(this.graphManager.getRoot())}},g.prototype.update=function(t){if(null==t)this.update2();else if(t instanceof s){var e=t;if(null!=e.getChild())for(var i=e.getChild().getNodes(),n=0;n<i.length;n++)update(i[n]);null!=e.vGraphObject&&e.vGraphObject.update(e)}else if(t instanceof a){var r=t;null!=r.vGraphObject&&r.vGraphObject.update(r)}else if(t instanceof h){var o=t;null!=o.vGraphObject&&o.vGraphObject.update(o)}},g.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=r.QUALITY,this.animationDuringLayout=r.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=r.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=r.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=r.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=r.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=r.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},g.prototype.transform=function(t){if(null==t)this.transform(new l(0,0));else{var e=new c,i=this.graphManager.getRoot().updateLeftTop();if(null!=i){e.setWorldOrgX(t.x),e.setWorldOrgY(t.y),e.setDeviceOrgX(i.x),e.setDeviceOrgY(i.y);for(var n=this.getAllNodes(),r=0;r<n.length;r++)n[r].transform(e)}}},g.prototype.positionNodesRandomly=function(t){if(null==t)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var e,i,n=t.getNodes(),r=0;r<n.length;r++)null==(i=(e=n[r]).getChild())||0==i.getNodes().length?e.scatter():(this.positionNodesRandomly(i),e.updateBounds())},g.prototype.getFlatForest=function(){for(var t=[],e=!0,i=this.graphManager.getRoot().getNodes(),r=!0,o=0;o<i.length;o++)null!=i[o].getChild()&&(r=!1);if(!r)return t;var s=new Set,a=[],h=new Map,l=[];for(l=l.concat(i);l.length>0&&e;){for(a.push(l[0]);a.length>0&&e;){var c=a[0];a.splice(0,1),s.add(c);var d=c.getEdges();for(o=0;o<d.length;o++){var g=d[o].getOtherEnd(c);if(h.get(c)!=g){if(s.has(g)){e=!1;break}a.push(g),h.set(g,c)}}}if(e){var u=[].concat(n(s));for(t.push(u),o=0;o<u.length;o++){var f=u[o],p=l.indexOf(f);p>-1&&l.splice(p,1)}s=new Set,h=new Map}else t=[]}return t},g.prototype.createDummyNodesForBendpoints=function(t){for(var e=[],i=t.source,n=this.graphManager.calcLowestCommonAncestor(t.source,t.target),r=0;r<t.bendpoints.length;r++){var o=this.newNode(null);o.setRect(new Point(0,0),new Dimension(1,1)),n.add(o);var s=this.newEdge(null);this.graphManager.add(s,i,o),e.add(o),i=o}return s=this.newEdge(null),this.graphManager.add(s,i,t.target),this.edgeToDummyNodes.set(t,e),t.isInterGraph()?this.graphManager.remove(t):n.remove(t),e},g.prototype.createBendpointsFromDummyNodes=function(){var t=[];t=t.concat(this.graphManager.getAllEdges()),t=[].concat(n(this.edgeToDummyNodes.keys())).concat(t);for(var e=0;e<t.length;e++){var i=t[e];if(i.bendpoints.length>0){for(var r=this.edgeToDummyNodes.get(i),o=0;o<r.length;o++){var s=r[o],a=new l(s.getCenterX(),s.getCenterY()),h=i.bendpoints.get(o);h.x=a.x,h.y=a.y,s.getOwner().remove(s)}this.graphManager.add(i,i.source,i.target)}}},g.transform=function(t,e,i,n){if(null!=i&&null!=n){var r=e;return t<=50?r-=(e-e/i)/50*(50-t):r+=(e*n-e)/50*(t-50),r}var o,s;return t<=50?(o=9*e/500,s=e/10):(o=9*e/50,s=-8*e),o*t+s},g.findCenterOfTree=function(t){var e=[];e=e.concat(t);var i=[],n=new Map,r=!1,o=null;1!=e.length&&2!=e.length||(r=!0,o=e[0]);for(var s=0;s<e.length;s++){var a=(c=e[s]).getNeighborsList().size;n.set(c,c.getNeighborsList().size),1==a&&i.push(c)}var h=[];for(h=h.concat(i);!r;){var l=[];for(l=l.concat(h),h=[],s=0;s<e.length;s++){var c=e[s],d=e.indexOf(c);d>=0&&e.splice(d,1),c.getNeighborsList().forEach((function(t){if(i.indexOf(t)<0){var e=n.get(t)-1;1==e&&h.push(t),n.set(t,e)}}))}i=i.concat(h),1!=e.length&&2!=e.length||(r=!0,o=e[0])}return o},g.prototype.setGraphManager=function(t){this.graphManager=t},t.exports=g},function(t,e,i){"use strict";function n(){}n.seed=1,n.x=0,n.nextDouble=function(){return n.x=1e4*Math.sin(n.seed++),n.x-Math.floor(n.x)},t.exports=n},function(t,e,i){"use strict";var n=i(5);function r(t,e){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}r.prototype.getWorldOrgX=function(){return this.lworldOrgX},r.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},r.prototype.getWorldOrgY=function(){return this.lworldOrgY},r.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},r.prototype.getWorldExtX=function(){return this.lworldExtX},r.prototype.setWorldExtX=function(t){this.lworldExtX=t},r.prototype.getWorldExtY=function(){return this.lworldExtY},r.prototype.setWorldExtY=function(t){this.lworldExtY=t},r.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},r.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},r.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},r.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},r.prototype.getDeviceExtX=function(){return this.ldeviceExtX},r.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},r.prototype.getDeviceExtY=function(){return this.ldeviceExtY},r.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},r.prototype.transformX=function(t){var e=0,i=this.lworldExtX;return 0!=i&&(e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/i),e},r.prototype.transformY=function(t){var e=0,i=this.lworldExtY;return 0!=i&&(e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/i),e},r.prototype.inverseTransformX=function(t){var e=0,i=this.ldeviceExtX;return 0!=i&&(e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/i),e},r.prototype.inverseTransformY=function(t){var e=0,i=this.ldeviceExtY;return 0!=i&&(e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/i),e},r.prototype.inverseTransformPoint=function(t){return new n(this.inverseTransformX(t.x),this.inverseTransformY(t.y))},t.exports=r},function(t,e,i){"use strict";var n=i(15),r=i(4),o=i(0),s=i(8),a=i(9);function h(){n.call(this),this.useSmartIdealEdgeLengthCalculation=r.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=r.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=r.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=r.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=r.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*r.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=r.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=r.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=r.MAX_ITERATIONS}for(var l in h.prototype=Object.create(n.prototype),n)h[l]=n[l];h.prototype.initParameters=function(){n.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=r.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},h.prototype.calcIdealEdgeLengths=function(){for(var t,e,i,n,s,a,h,l=this.getGraphManager().getAllEdges(),c=0;c<l.length;c++)e=(t=l[c]).idealLength,t.isInterGraph&&(n=t.getSource(),s=t.getTarget(),a=t.getSourceInLca().getEstimatedSize(),h=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=a+h-2*o.SIMPLE_NODE_SIZE),i=t.getLca().getInclusionTreeDepth(),t.idealLength+=e*r.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(n.getInclusionTreeDepth()+s.getInclusionTreeDepth()-2*i))},h.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>r.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*r.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-r.ADAPTATION_LOWER_NODE_LIMIT)/(r.ADAPTATION_UPPER_NODE_LIMIT-r.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-r.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=r.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>r.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(r.COOLING_ADAPTATION_FACTOR,1-(t-r.ADAPTATION_LOWER_NODE_LIMIT)/(r.ADAPTATION_UPPER_NODE_LIMIT-r.ADAPTATION_LOWER_NODE_LIMIT)*(1-r.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=r.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.displacementThresholdPerNode=3*r.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},h.prototype.calcSpringForces=function(){for(var t,e=this.getAllEdges(),i=0;i<e.length;i++)t=e[i],this.calcSpringForce(t,t.idealLength)},h.prototype.calcRepulsionForces=function(){var t,e,i,n,o,s=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],h=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%r.GRID_CALCULATION_CHECK_PERIOD==1&&s&&this.updateGrid(),o=new Set,t=0;t<h.length;t++)i=h[t],this.calculateRepulsionForceOfANode(i,o,s,a),o.add(i);else for(t=0;t<h.length;t++)for(i=h[t],e=t+1;e<h.length;e++)n=h[e],i.getOwner()==n.getOwner()&&this.calcRepulsionForce(i,n)},h.prototype.calcGravitationalForces=function(){for(var t,e=this.getAllNodesToApplyGravitation(),i=0;i<e.length;i++)t=e[i],this.calcGravitationalForce(t)},h.prototype.moveNodes=function(){for(var t=this.getAllNodes(),e=0;e<t.length;e++)t[e].move()},h.prototype.calcSpringForce=function(t,e){var i,n,r,o,s=t.getSource(),a=t.getTarget();if(this.uniformLeafNodeSizes&&null==s.getChild()&&null==a.getChild())t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;0!=(i=t.getLength())&&(r=(n=t.edgeElasticity*(i-e))*(t.lengthX/i),o=n*(t.lengthY/i),s.springForceX+=r,s.springForceY+=o,a.springForceX-=r,a.springForceY-=o)},h.prototype.calcRepulsionForce=function(t,e){var i,n,o,h,l,c,d,g=t.getRect(),u=e.getRect(),f=new Array(2),p=new Array(4);if(g.intersects(u)){s.calcSeparationAmount(g,u,f,r.DEFAULT_EDGE_LENGTH/2),c=2*f[0],d=2*f[1];var v=t.noOfChildren*e.noOfChildren/(t.noOfChildren+e.noOfChildren);t.repulsionForceX-=v*c,t.repulsionForceY-=v*d,e.repulsionForceX+=v*c,e.repulsionForceY+=v*d}else this.uniformLeafNodeSizes&&null==t.getChild()&&null==e.getChild()?(i=u.getCenterX()-g.getCenterX(),n=u.getCenterY()-g.getCenterY()):(s.getIntersection(g,u,p),i=p[2]-p[0],n=p[3]-p[1]),Math.abs(i)<r.MIN_REPULSION_DIST&&(i=a.sign(i)*r.MIN_REPULSION_DIST),Math.abs(n)<r.MIN_REPULSION_DIST&&(n=a.sign(n)*r.MIN_REPULSION_DIST),o=i*i+n*n,h=Math.sqrt(o),c=(l=(t.nodeRepulsion/2+e.nodeRepulsion/2)*t.noOfChildren*e.noOfChildren/o)*i/h,d=l*n/h,t.repulsionForceX-=c,t.repulsionForceY-=d,e.repulsionForceX+=c,e.repulsionForceY+=d},h.prototype.calcGravitationalForce=function(t){var e,i,n,r,o,s,a,h;i=((e=t.getOwner()).getRight()+e.getLeft())/2,n=(e.getTop()+e.getBottom())/2,r=t.getCenterX()-i,o=t.getCenterY()-n,s=Math.abs(r)+t.getWidth()/2,a=Math.abs(o)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(s>(h=e.getEstimatedSize()*this.gravityRangeFactor)||a>h)&&(t.gravitationForceX=-this.gravityConstant*r,t.gravitationForceY=-this.gravityConstant*o):(s>(h=e.getEstimatedSize()*this.compoundGravityRangeFactor)||a>h)&&(t.gravitationForceX=-this.gravityConstant*r*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*o*this.compoundGravityConstant)},h.prototype.isConverged=function(){var t,e=!1;return this.totalIterations>this.maxIterations/3&&(e=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||e},h.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},h.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,e=this.graphManager.getAllNodes(),i=0;i<e.length;i++)(t=e[i]).noOfChildren=t.getNoOfChildren()},h.prototype.calcGrid=function(t){var e,i;e=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),i=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var n=new Array(e),r=0;r<e;r++)n[r]=new Array(i);for(r=0;r<e;r++)for(var o=0;o<i;o++)n[r][o]=new Array;return n},h.prototype.addNodeToGrid=function(t,e,i){var n,r,o,s;n=parseInt(Math.floor((t.getRect().x-e)/this.repulsionRange)),r=parseInt(Math.floor((t.getRect().width+t.getRect().x-e)/this.repulsionRange)),o=parseInt(Math.floor((t.getRect().y-i)/this.repulsionRange)),s=parseInt(Math.floor((t.getRect().height+t.getRect().y-i)/this.repulsionRange));for(var a=n;a<=r;a++)for(var h=o;h<=s;h++)this.grid[a][h].push(t),t.setGridCoordinates(n,r,o,s)},h.prototype.updateGrid=function(){var t,e,i=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<i.length;t++)e=i[t],this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},h.prototype.calculateRepulsionForceOfANode=function(t,e,i,n){if(this.totalIterations%r.GRID_CALCULATION_CHECK_PERIOD==1&&i||n){var o,s=new Set;t.surrounding=new Array;for(var a=this.grid,h=t.startX-1;h<t.finishX+2;h++)for(var l=t.startY-1;l<t.finishY+2;l++)if(!(h<0||l<0||h>=a.length||l>=a[0].length))for(var c=0;c<a[h][l].length;c++)if(o=a[h][l][c],t.getOwner()==o.getOwner()&&t!=o&&!e.has(o)&&!s.has(o)){var d=Math.abs(t.getCenterX()-o.getCenterX())-(t.getWidth()/2+o.getWidth()/2),g=Math.abs(t.getCenterY()-o.getCenterY())-(t.getHeight()/2+o.getHeight()/2);d<=this.repulsionRange&&g<=this.repulsionRange&&s.add(o)}t.surrounding=[].concat(function(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}(s))}for(h=0;h<t.surrounding.length;h++)this.calcRepulsionForce(t,t.surrounding[h])},h.prototype.calcRepulsionRange=function(){return 0},t.exports=h},function(t,e,i){"use strict";var n=i(1),r=i(4);function o(t,e,i){n.call(this,t,e,i),this.idealLength=r.DEFAULT_EDGE_LENGTH,this.edgeElasticity=r.DEFAULT_SPRING_STRENGTH}for(var s in o.prototype=Object.create(n.prototype),n)o[s]=n[s];t.exports=o},function(t,e,i){"use strict";var n=i(3),r=i(4);function o(t,e,i,o){n.call(this,t,e,i,o),this.nodeRepulsion=r.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var s in o.prototype=Object.create(n.prototype),n)o[s]=n[s];o.prototype.setGridCoordinates=function(t,e,i,n){this.startX=t,this.finishX=e,this.startY=i,this.finishY=n},t.exports=o},function(t,e,i){"use strict";function n(t,e){this.width=0,this.height=0,null!==t&&null!==e&&(this.height=e,this.width=t)}n.prototype.getWidth=function(){return this.width},n.prototype.setWidth=function(t){this.width=t},n.prototype.getHeight=function(){return this.height},n.prototype.setHeight=function(t){this.height=t},t.exports=n},function(t,e,i){"use strict";var n=i(14);function r(){this.map={},this.keys=[]}r.prototype.put=function(t,e){var i=n.createID(t);this.contains(i)||(this.map[i]=e,this.keys.push(t))},r.prototype.contains=function(t){return n.createID(t),null!=this.map[t]},r.prototype.get=function(t){var e=n.createID(t);return this.map[e]},r.prototype.keySet=function(){return this.keys},t.exports=r},function(t,e,i){"use strict";var n=i(14);function r(){this.set={}}r.prototype.add=function(t){var e=n.createID(t);this.contains(e)||(this.set[e]=t)},r.prototype.remove=function(t){delete this.set[n.createID(t)]},r.prototype.clear=function(){this.set={}},r.prototype.contains=function(t){return this.set[n.createID(t)]==t},r.prototype.isEmpty=function(){return 0===this.size()},r.prototype.size=function(){return Object.keys(this.set).length},r.prototype.addAllTo=function(t){for(var e=Object.keys(this.set),i=e.length,n=0;n<i;n++)t.push(this.set[e[n]])},r.prototype.size=function(){return Object.keys(this.set).length},r.prototype.addAll=function(t){for(var e=t.length,i=0;i<e;i++){var n=t[i];this.add(n)}},t.exports=r},function(t,e,i){"use strict";function n(){}n.multMat=function(t,e){for(var i=[],n=0;n<t.length;n++){i[n]=[];for(var r=0;r<e[0].length;r++){i[n][r]=0;for(var o=0;o<t[0].length;o++)i[n][r]+=t[n][o]*e[o][r]}}return i},n.transpose=function(t){for(var e=[],i=0;i<t[0].length;i++){e[i]=[];for(var n=0;n<t.length;n++)e[i][n]=t[n][i]}return e},n.multCons=function(t,e){for(var i=[],n=0;n<t.length;n++)i[n]=t[n]*e;return i},n.minusOp=function(t,e){for(var i=[],n=0;n<t.length;n++)i[n]=t[n]-e[n];return i},n.dotProduct=function(t,e){for(var i=0,n=0;n<t.length;n++)i+=t[n]*e[n];return i},n.mag=function(t){return Math.sqrt(this.dotProduct(t,t))},n.normalize=function(t){for(var e=[],i=this.mag(t),n=0;n<t.length;n++)e[n]=t[n]/i;return e},n.multGamma=function(t){for(var e=[],i=0,n=0;n<t.length;n++)i+=t[n];i*=-1/t.length;for(var r=0;r<t.length;r++)e[r]=i+t[r];return e},n.multL=function(t,e,i){for(var n=[],r=[],o=[],s=0;s<e[0].length;s++){for(var a=0,h=0;h<e.length;h++)a+=-.5*e[h][s]*t[h];r[s]=a}for(var l=0;l<i.length;l++){for(var c=0,d=0;d<i.length;d++)c+=i[l][d]*r[d];o[l]=c}for(var g=0;g<e.length;g++){for(var u=0,f=0;f<e[0].length;f++)u+=e[g][f]*o[f];n[g]=u}return n},t.exports=n},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=i(11),o=function(){function t(e,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),null===i&&void 0===i||(this.compareFunction=this._defaultCompareFunction);var n=void 0;n=e instanceof r?e.size():e.length,this._quicksort(e,0,n-1)}return n(t,[{key:"_quicksort",value:function(t,e,i){if(e<i){var n=this._partition(t,e,i);this._quicksort(t,e,n),this._quicksort(t,n+1,i)}}},{key:"_partition",value:function(t,e,i){for(var n=this._get(t,e),r=e,o=i;;){for(;this.compareFunction(n,this._get(t,o));)o--;for(;this.compareFunction(this._get(t,r),n);)r++;if(!(r<o))return o;this._swap(t,r,o),r++,o--}}},{key:"_get",value:function(t,e){return t instanceof r?t.get_object_at(e):t[e]}},{key:"_set",value:function(t,e,i){t instanceof r?t.set_object_at(e,i):t[e]=i}},{key:"_swap",value:function(t,e,i){var n=this._get(t,e);this._set(t,e,this._get(t,i)),this._set(t,i,n)}},{key:"_defaultCompareFunction",value:function(t,e){return e>t}}]),t}();t.exports=o},function(t,e,i){"use strict";function n(){}n.svd=function(t){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=t.length,this.n=t[0].length;var e=Math.min(this.m,this.n);this.s=function(t){for(var e=[];t-- >0;)e.push(0);return e}(Math.min(this.m+1,this.n)),this.U=function t(e){if(0==e.length)return 0;for(var i=[],n=0;n<e[0];n++)i.push(t(e.slice(1)));return i}([this.m,e]),this.V=function(t){return function t(e){if(0==e.length)return 0;for(var i=[],n=0;n<e[0];n++)i.push(t(e.slice(1)));return i}(t)}([this.n,this.n]);for(var i,r,o=function(t){for(var e=[];t-- >0;)e.push(0);return e}(this.n),s=function(t){for(var e=[];t-- >0;)e.push(0);return e}(this.m),a=Math.min(this.m-1,this.n),h=Math.max(0,Math.min(this.n-2,this.m)),l=0;l<Math.max(a,h);l++){if(l<a){this.s[l]=0;for(var c=l;c<this.m;c++)this.s[l]=n.hypot(this.s[l],t[c][l]);if(0!==this.s[l]){t[l][l]<0&&(this.s[l]=-this.s[l]);for(var d=l;d<this.m;d++)t[d][l]/=this.s[l];t[l][l]+=1}this.s[l]=-this.s[l]}for(var g=l+1;g<this.n;g++){if(i=l<a,r=0!==this.s[l],i&&r){for(var u=0,f=l;f<this.m;f++)u+=t[f][l]*t[f][g];u=-u/t[l][l];for(var p=l;p<this.m;p++)t[p][g]+=u*t[p][l]}o[g]=t[l][g]}if(function(t,e){return e}(0,l<a))for(var v=l;v<this.m;v++)this.U[v][l]=t[v][l];if(l<h){o[l]=0;for(var y=l+1;y<this.n;y++)o[l]=n.hypot(o[l],o[y]);if(0!==o[l]){o[l+1]<0&&(o[l]=-o[l]);for(var m=l+1;m<this.n;m++)o[m]/=o[l];o[l+1]+=1}if(o[l]=-o[l],function(t,e){return t&&e}(l+1<this.m,0!==o[l])){for(var E=l+1;E<this.m;E++)s[E]=0;for(var N=l+1;N<this.n;N++)for(var T=l+1;T<this.m;T++)s[T]+=o[N]*t[T][N];for(var A=l+1;A<this.n;A++)for(var w=-o[A]/o[l+1],L=l+1;L<this.m;L++)t[L][A]+=w*s[L]}for(var I=l+1;I<this.n;I++)this.V[I][l]=o[I]}}var _=Math.min(this.n,this.m+1);a<this.n&&(this.s[a]=t[a][a]),this.m<_&&(this.s[_-1]=0),h+1<_&&(o[h]=t[h][_-1]),o[_-1]=0;for(var C=a;C<e;C++){for(var M=0;M<this.m;M++)this.U[M][C]=0;this.U[C][C]=1}for(var x=a-1;x>=0;x--)if(0!==this.s[x]){for(var O=x+1;O<e;O++){for(var D=0,R=x;R<this.m;R++)D+=this.U[R][x]*this.U[R][O];D=-D/this.U[x][x];for(var b=x;b<this.m;b++)this.U[b][O]+=D*this.U[b][x]}for(var G=x;G<this.m;G++)this.U[G][x]=-this.U[G][x];this.U[x][x]=1+this.U[x][x];for(var F=0;F<x-1;F++)this.U[F][x]=0}else{for(var S=0;S<this.m;S++)this.U[S][x]=0;this.U[x][x]=1}for(var P=this.n-1;P>=0;P--){if(function(t,e){return t&&e}(P<h,0!==o[P]))for(var U=P+1;U<e;U++){for(var Y=0,k=P+1;k<this.n;k++)Y+=this.V[k][P]*this.V[k][U];Y=-Y/this.V[P+1][P];for(var H=P+1;H<this.n;H++)this.V[H][U]+=Y*this.V[H][P]}for(var X=0;X<this.n;X++)this.V[X][P]=0;this.V[P][P]=1}for(var z=_-1,V=Math.pow(2,-52),B=Math.pow(2,-966);_>0;){var W=void 0,j=void 0;for(W=_-2;W>=-1&&-1!==W;W--)if(Math.abs(o[W])<=B+V*(Math.abs(this.s[W])+Math.abs(this.s[W+1]))){o[W]=0;break}if(W===_-2)j=4;else{var q=void 0;for(q=_-1;q>=W&&q!==W;q--){var $=(q!==_?Math.abs(o[q]):0)+(q!==W+1?Math.abs(o[q-1]):0);if(Math.abs(this.s[q])<=B+V*$){this.s[q]=0;break}}q===W?j=3:q===_-1?j=1:(j=2,W=q)}switch(W++,j){case 1:var K=o[_-2];o[_-2]=0;for(var Z=_-2;Z>=W;Z--){var Q=n.hypot(this.s[Z],K),J=this.s[Z]/Q,tt=K/Q;this.s[Z]=Q,Z!==W&&(K=-tt*o[Z-1],o[Z-1]=J*o[Z-1]);for(var et=0;et<this.n;et++)Q=J*this.V[et][Z]+tt*this.V[et][_-1],this.V[et][_-1]=-tt*this.V[et][Z]+J*this.V[et][_-1],this.V[et][Z]=Q}break;case 2:var it=o[W-1];o[W-1]=0;for(var nt=W;nt<_;nt++){var rt=n.hypot(this.s[nt],it),ot=this.s[nt]/rt,st=it/rt;this.s[nt]=rt,it=-st*o[nt],o[nt]=ot*o[nt];for(var at=0;at<this.m;at++)rt=ot*this.U[at][nt]+st*this.U[at][W-1],this.U[at][W-1]=-st*this.U[at][nt]+ot*this.U[at][W-1],this.U[at][nt]=rt}break;case 3:var ht=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[_-1]),Math.abs(this.s[_-2])),Math.abs(o[_-2])),Math.abs(this.s[W])),Math.abs(o[W])),lt=this.s[_-1]/ht,ct=this.s[_-2]/ht,dt=o[_-2]/ht,gt=this.s[W]/ht,ut=o[W]/ht,ft=((ct+lt)*(ct-lt)+dt*dt)/2,pt=lt*dt*(lt*dt),vt=0;(function(t,e){return t||e})(0!==ft,0!==pt)&&(vt=Math.sqrt(ft*ft+pt),ft<0&&(vt=-vt),vt=pt/(ft+vt));for(var yt=(gt+lt)*(gt-lt)+vt,mt=gt*ut,Et=W;Et<_-1;Et++){var Nt=n.hypot(yt,mt),Tt=yt/Nt,At=mt/Nt;Et!==W&&(o[Et-1]=Nt),yt=Tt*this.s[Et]+At*o[Et],o[Et]=Tt*o[Et]-At*this.s[Et],mt=At*this.s[Et+1],this.s[Et+1]=Tt*this.s[Et+1];for(var wt=0;wt<this.n;wt++)Nt=Tt*this.V[wt][Et]+At*this.V[wt][Et+1],this.V[wt][Et+1]=-At*this.V[wt][Et]+Tt*this.V[wt][Et+1],this.V[wt][Et]=Nt;if(Tt=yt/(Nt=n.hypot(yt,mt)),At=mt/Nt,this.s[Et]=Nt,yt=Tt*o[Et]+At*this.s[Et+1],this.s[Et+1]=-At*o[Et]+Tt*this.s[Et+1],mt=At*o[Et+1],o[Et+1]=Tt*o[Et+1],Et<this.m-1)for(var Lt=0;Lt<this.m;Lt++)Nt=Tt*this.U[Lt][Et]+At*this.U[Lt][Et+1],this.U[Lt][Et+1]=-At*this.U[Lt][Et]+Tt*this.U[Lt][Et+1],this.U[Lt][Et]=Nt}o[_-2]=yt;break;case 4:if(this.s[W]<=0){this.s[W]=this.s[W]<0?-this.s[W]:0;for(var It=0;It<=z;It++)this.V[It][W]=-this.V[It][W]}for(;W<z&&!(this.s[W]>=this.s[W+1]);){var _t=this.s[W];if(this.s[W]=this.s[W+1],this.s[W+1]=_t,W<this.n-1)for(var Ct=0;Ct<this.n;Ct++)_t=this.V[Ct][W+1],this.V[Ct][W+1]=this.V[Ct][W],this.V[Ct][W]=_t;if(W<this.m-1)for(var Mt=0;Mt<this.m;Mt++)_t=this.U[Mt][W+1],this.U[Mt][W+1]=this.U[Mt][W],this.U[Mt][W]=_t;W++}_--}}return{U:this.U,V:this.V,S:this.s}},n.hypot=function(t,e){var i=void 0;return Math.abs(t)>Math.abs(e)?(i=e/t,i=Math.abs(t)*Math.sqrt(1+i*i)):0!=e?(i=t/e,i=Math.abs(e)*Math.sqrt(1+i*i)):i=0,i},t.exports=n},function(t,e,i){"use strict";var n=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),r=function(){function t(e,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.sequence1=e,this.sequence2=i,this.match_score=n,this.mismatch_penalty=r,this.gap_penalty=o,this.iMax=e.length+1,this.jMax=i.length+1,this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var a=0;a<this.jMax;a++)this.grid[s][a]=0}this.tracebackGrid=new Array(this.iMax);for(var h=0;h<this.iMax;h++){this.tracebackGrid[h]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[h][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return n(t,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var t=1;t<this.jMax;t++)this.grid[0][t]=this.grid[0][t-1]+this.gap_penalty,this.tracebackGrid[0][t]=[!1,!1,!0];for(var e=1;e<this.iMax;e++)this.grid[e][0]=this.grid[e-1][0]+this.gap_penalty,this.tracebackGrid[e][0]=[!1,!0,!1];for(var i=1;i<this.iMax;i++)for(var n=1;n<this.jMax;n++){var r=[this.sequence1[i-1]===this.sequence2[n-1]?this.grid[i-1][n-1]+this.match_score:this.grid[i-1][n-1]+this.mismatch_penalty,this.grid[i-1][n]+this.gap_penalty,this.grid[i][n-1]+this.gap_penalty],o=this.arrayAllMaxIndexes(r);this.grid[i][n]=r[o[0]],this.tracebackGrid[i][n]=[o.includes(0),o.includes(1),o.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var t=[];for(t.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});t[0];){var e=t[0],i=this.tracebackGrid[e.pos[0]][e.pos[1]];i[0]&&t.push({pos:[e.pos[0]-1,e.pos[1]-1],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),i[1]&&t.push({pos:[e.pos[0]-1,e.pos[1]],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:"-"+e.seq2}),i[2]&&t.push({pos:[e.pos[0],e.pos[1]-1],seq1:"-"+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),0===e.pos[0]&&0===e.pos[1]&&this.alignments.push({sequence1:e.seq1,sequence2:e.seq2}),t.shift()}return this.alignments}},{key:"getAllIndexes",value:function(t,e){for(var i=[],n=-1;-1!==(n=t.indexOf(e,n+1));)i.push(n);return i}},{key:"arrayAllMaxIndexes",value:function(t){return this.getAllIndexes(t,Math.max.apply(null,t))}}]),t}();t.exports=r},function(t,e,i){"use strict";var n=function(){};n.FDLayout=i(18),n.FDLayoutConstants=i(4),n.FDLayoutEdge=i(19),n.FDLayoutNode=i(20),n.DimensionD=i(21),n.HashMap=i(22),n.HashSet=i(23),n.IGeometry=i(8),n.IMath=i(9),n.Integer=i(10),n.Point=i(12),n.PointD=i(5),n.RandomSeed=i(16),n.RectangleD=i(13),n.Transform=i(17),n.UniqueIDGeneretor=i(14),n.Quicksort=i(25),n.LinkedList=i(11),n.LGraphObject=i(2),n.LGraph=i(6),n.LEdge=i(1),n.LGraphManager=i(7),n.LNode=i(3),n.Layout=i(15),n.LayoutConstants=i(0),n.NeedlemanWunsch=i(27),n.Matrix=i(24),n.SVD=i(26),t.exports=n},function(t,e,i){"use strict";function n(){this.listeners=[]}var r=n.prototype;r.addListener=function(t,e){this.listeners.push({event:t,callback:e})},r.removeListener=function(t,e){for(var i=this.listeners.length;i>=0;i--){var n=this.listeners[i];n.event===t&&n.callback===e&&this.listeners.splice(i,1)}},r.emit=function(t,e){for(var i=0;i<this.listeners.length;i++){var n=this.listeners[i];t===n.event&&n.callback(e)}},t.exports=n}])},t.exports=e()},7354:(t,e,i)=>{"use strict";function n(t,e){t.accDescr&&e.setAccDescription?.(t.accDescr),t.accTitle&&e.setAccTitle?.(t.accTitle),t.title&&e.setDiagramTitle?.(t.title)}i.d(e,{S:()=>n}),(0,i(2107).K2)(n,"populateCommonDb")},8938:(t,e,i)=>{"use strict";i.d(e,{diagram:()=>gt});var n=i(6503),r=i(4431),o=i(7354),s=i(1400),a=(i(5395),i(6627)),h=i(2107),l=i(8731),c=i(165),d=i(6527),g=i(7),u={L:"left",R:"right",T:"top",B:"bottom"},f={L:(0,h.K2)((t=>`${t},${t/2} 0,${t} 0,0`),"L"),R:(0,h.K2)((t=>`0,${t/2} ${t},0 ${t},${t}`),"R"),T:(0,h.K2)((t=>`0,0 ${t},0 ${t/2},${t}`),"T"),B:(0,h.K2)((t=>`${t/2},0 ${t},${t} 0,${t}`),"B")},p={L:(0,h.K2)(((t,e)=>t-e+2),"L"),R:(0,h.K2)(((t,e)=>t-2),"R"),T:(0,h.K2)(((t,e)=>t-e+2),"T"),B:(0,h.K2)(((t,e)=>t-2),"B")},v=(0,h.K2)((function(t){return m(t)?"L"===t?"R":"L":"T"===t?"B":"T"}),"getOppositeArchitectureDirection"),y=(0,h.K2)((function(t){return"L"===t||"R"===t||"T"===t||"B"===t}),"isArchitectureDirection"),m=(0,h.K2)((function(t){return"L"===t||"R"===t}),"isArchitectureDirectionX"),E=(0,h.K2)((function(t){return"T"===t||"B"===t}),"isArchitectureDirectionY"),N=(0,h.K2)((function(t,e){const i=m(t)&&E(e),n=E(t)&&m(e);return i||n}),"isArchitectureDirectionXY"),T=(0,h.K2)((function(t){const e=t[0],i=t[1],n=m(e)&&E(i),r=E(e)&&m(i);return n||r}),"isArchitecturePairXY"),A=(0,h.K2)((function(t){return"LL"!==t&&"RR"!==t&&"TT"!==t&&"BB"!==t}),"isValidArchitectureDirectionPair"),w=(0,h.K2)((function(t,e){const i=`${t}${e}`;return A(i)?i:void 0}),"getArchitectureDirectionPair"),L=(0,h.K2)((function([t,e],i){const n=i[0],r=i[1];return m(n)?E(r)?[t+("L"===n?-1:1),e+("T"===r?1:-1)]:[t+("L"===n?-1:1),e]:m(r)?[t+("L"===r?1:-1),e+("T"===n?1:-1)]:[t,e+("T"===n?1:-1)]}),"shiftPositionByArchitectureDirectionPair"),I=(0,h.K2)((function(t){return"LT"===t||"TL"===t?[1,1]:"BL"===t||"LB"===t?[1,-1]:"BR"===t||"RB"===t?[-1,-1]:[-1,1]}),"getArchitectureDirectionXYFactors"),_=(0,h.K2)((function(t,e){return N(t,e)?"bend":m(t)?"horizontal":"vertical"}),"getArchitectureDirectionAlignment"),C=(0,h.K2)((function(t){return"service"===t.type}),"isArchitectureService"),M=(0,h.K2)((function(t){return"junction"===t.type}),"isArchitectureJunction"),x=(0,h.K2)((t=>t.data()),"edgeData"),O=(0,h.K2)((t=>t.data()),"nodeData"),D=h.UI.architecture,R=new s.m((()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:D,dataStructures:void 0,elements:{}}))),b=(0,h.K2)((()=>{R.reset(),(0,h.IU)()}),"clear"),G=(0,h.K2)((function({id:t,icon:e,in:i,title:n,iconText:r}){if(void 0!==R.records.registeredIds[t])throw new Error(`The service id [${t}] is already in use by another ${R.records.registeredIds[t]}`);if(void 0!==i){if(t===i)throw new Error(`The service [${t}] cannot be placed within itself`);if(void 0===R.records.registeredIds[i])throw new Error(`The service [${t}]'s parent does not exist. Please make sure the parent is created before this service`);if("node"===R.records.registeredIds[i])throw new Error(`The service [${t}]'s parent is not a group`)}R.records.registeredIds[t]="node",R.records.nodes[t]={id:t,type:"service",icon:e,iconText:r,title:n,edges:[],in:i}}),"addService"),F=(0,h.K2)((()=>Object.values(R.records.nodes).filter(C)),"getServices"),S=(0,h.K2)((function({id:t,in:e}){R.records.registeredIds[t]="node",R.records.nodes[t]={id:t,type:"junction",edges:[],in:e}}),"addJunction"),P=(0,h.K2)((()=>Object.values(R.records.nodes).filter(M)),"getJunctions"),U=(0,h.K2)((()=>Object.values(R.records.nodes)),"getNodes"),Y=(0,h.K2)((t=>R.records.nodes[t]),"getNode"),k=(0,h.K2)((function({id:t,icon:e,in:i,title:n}){if(void 0!==R.records.registeredIds[t])throw new Error(`The group id [${t}] is already in use by another ${R.records.registeredIds[t]}`);if(void 0!==i){if(t===i)throw new Error(`The group [${t}] cannot be placed within itself`);if(void 0===R.records.registeredIds[i])throw new Error(`The group [${t}]'s parent does not exist. Please make sure the parent is created before this group`);if("node"===R.records.registeredIds[i])throw new Error(`The group [${t}]'s parent is not a group`)}R.records.registeredIds[t]="group",R.records.groups[t]={id:t,icon:e,title:n,in:i}}),"addGroup"),H=(0,h.K2)((()=>Object.values(R.records.groups)),"getGroups"),X=(0,h.K2)((function({lhsId:t,rhsId:e,lhsDir:i,rhsDir:n,lhsInto:r,rhsInto:o,lhsGroup:s,rhsGroup:a,title:h}){if(!y(i))throw new Error(`Invalid direction given for left hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${i}`);if(!y(n))throw new Error(`Invalid direction given for right hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${n}`);if(void 0===R.records.nodes[t]&&void 0===R.records.groups[t])throw new Error(`The left-hand id [${t}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(void 0===R.records.nodes[e]&&void 0===R.records.groups[t])throw new Error(`The right-hand id [${e}] does not yet exist. Please create the service/group before declaring an edge to it.`);const l=R.records.nodes[t].in,c=R.records.nodes[e].in;if(s&&l&&c&&l==c)throw new Error(`The left-hand id [${t}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(a&&l&&c&&l==c)throw new Error(`The right-hand id [${e}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const d={lhsId:t,lhsDir:i,lhsInto:r,lhsGroup:s,rhsId:e,rhsDir:n,rhsInto:o,rhsGroup:a,title:h};R.records.edges.push(d),R.records.nodes[t]&&R.records.nodes[e]&&(R.records.nodes[t].edges.push(R.records.edges[R.records.edges.length-1]),R.records.nodes[e].edges.push(R.records.edges[R.records.edges.length-1]))}),"addEdge"),z=(0,h.K2)((()=>R.records.edges),"getEdges"),V=(0,h.K2)((()=>{if(void 0===R.records.dataStructures){const t={},e=Object.entries(R.records.nodes).reduce(((e,[i,n])=>(e[i]=n.edges.reduce(((e,n)=>{const r=Y(n.lhsId)?.in,o=Y(n.rhsId)?.in;if(r&&o&&r!==o){const e=_(n.lhsDir,n.rhsDir);"bend"!==e&&(t[r]??={},t[r][o]=e,t[o]??={},t[o][r]=e)}if(n.lhsId===i){const t=w(n.lhsDir,n.rhsDir);t&&(e[t]=n.rhsId)}else{const t=w(n.rhsDir,n.lhsDir);t&&(e[t]=n.lhsId)}return e}),{}),e)),{}),i=Object.keys(e)[0],n={[i]:1},r=Object.keys(e).reduce(((t,e)=>e===i?t:{...t,[e]:1}),{}),o=(0,h.K2)((t=>{const i={[t]:[0,0]},o=[t];for(;o.length>0;){const t=o.shift();if(t){n[t]=1,delete r[t];const s=e[t],[a,h]=i[t];Object.entries(s).forEach((([t,e])=>{n[e]||(i[e]=L([a,h],t),o.push(e))}))}}return i}),"BFS"),s=[o(i)];for(;Object.keys(r).length>0;)s.push(o(Object.keys(r)[0]));R.records.dataStructures={adjList:e,spatialMaps:s,groupAlignments:t}}return R.records.dataStructures}),"getDataStructures"),B=(0,h.K2)(((t,e)=>{R.records.elements[t]=e}),"setElementForId"),W=(0,h.K2)((t=>R.records.elements[t]),"getElementById"),j={clear:b,setDiagramTitle:h.ke,getDiagramTitle:h.ab,setAccTitle:h.SV,getAccTitle:h.iN,setAccDescription:h.EI,getAccDescription:h.m7,addService:G,getServices:F,addJunction:S,getJunctions:P,getNodes:U,getNode:Y,addGroup:k,getGroups:H,addEdge:X,getEdges:z,setElementForId:B,getElementById:W,getDataStructures:V};function q(t){const e=(0,h.D7)().architecture;return e?.[t]?e[t]:D[t]}(0,h.K2)(q,"getConfigField");var $=(0,h.K2)(((t,e)=>{(0,o.S)(t,e),t.groups.map(e.addGroup),t.services.map((t=>e.addService({...t,type:"service"}))),t.junctions.map((t=>e.addJunction({...t,type:"junction"}))),t.edges.map(e.addEdge)}),"populateDb"),K={parse:(0,h.K2)((async t=>{const e=await(0,l.qg)("architecture",t);h.Rm.debug(e),$(e,j)}),"parse")},Z=(0,h.K2)((t=>`\n  .edge {\n    stroke-width: ${t.archEdgeWidth};\n    stroke: ${t.archEdgeColor};\n    fill: none;\n  }\n\n  .arrow {\n    fill: ${t.archEdgeArrowColor};\n  }\n\n  .node-bkg {\n    fill: none;\n    stroke: ${t.archGroupBorderColor};\n    stroke-width: ${t.archGroupBorderWidth};\n    stroke-dasharray: 8;\n  }\n  .node-icon-text {\n    display: flex; \n    align-items: center;\n  }\n  \n  .node-icon-text > div {\n    color: #fff;\n    margin: 1px;\n    height: fit-content;\n    text-align: center;\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n  }\n`),"getStyles"),Q=(0,h.K2)((t=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${t}</g>`),"wrapIcon"),J={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:Q('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:Q('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:Q('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:Q('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:Q('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:n.Gc,blank:{body:Q("")}}},tt=(0,h.K2)((async function(t,e){const i=q("padding"),n=q("iconSize"),o=n/2,s=n/6,a=s/2;await Promise.all(e.edges().map((async e=>{const{source:n,sourceDir:l,sourceArrow:c,sourceGroup:d,target:g,targetDir:u,targetArrow:v,targetGroup:y,label:A}=x(e);let{x:L,y:_}=e[0].sourceEndpoint();const{x:C,y:M}=e[0].midpoint();let{x:O,y:D}=e[0].targetEndpoint();const R=i+4;if(d&&(m(l)?L+="L"===l?-R:R:_+="T"===l?-R:R+18),y&&(m(u)?O+="L"===u?-R:R:D+="T"===u?-R:R+18),d||"junction"!==j.getNode(n)?.type||(m(l)?L+="L"===l?o:-o:_+="T"===l?o:-o),y||"junction"!==j.getNode(g)?.type||(m(u)?O+="L"===u?o:-o:D+="T"===u?o:-o),e[0]._private.rscratch){const e=t.insert("g");if(e.insert("path").attr("d",`M ${L},${_} L ${C},${M} L${O},${D} `).attr("class","edge"),c){const t=m(l)?p[l](L,s):L-a,i=E(l)?p[l](_,s):_-a;e.insert("polygon").attr("points",f[l](s)).attr("transform",`translate(${t},${i})`).attr("class","arrow")}if(v){const t=m(u)?p[u](O,s):O-a,i=E(u)?p[u](D,s):D-a;e.insert("polygon").attr("points",f[u](s)).attr("transform",`translate(${t},${i})`).attr("class","arrow")}if(A){const t=N(l,u)?"XY":m(l)?"X":"Y";let i=0;i="X"===t?Math.abs(L-O):"Y"===t?Math.abs(_-D)/1.5:Math.abs(L-O)/2;const n=e.append("g");if(await(0,r.GZ)(n,A,{useHtmlLabels:!1,width:i,classes:"architecture-service-label"},(0,h.D7)()),n.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),"X"===t)n.attr("transform","translate("+C+", "+M+")");else if("Y"===t)n.attr("transform","translate("+C+", "+M+") rotate(-90)");else if("XY"===t){const t=w(l,u);if(t&&T(t)){const e=n.node().getBoundingClientRect(),[i,r]=I(t);n.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*i*r*45})`);const o=n.node().getBoundingClientRect();n.attr("transform",`\n                translate(${C}, ${M-e.height/2})\n                translate(${i*o.width/2}, ${r*o.height/2})\n                rotate(${-1*i*r*45}, 0, ${e.height/2})\n              `)}}}}})))}),"drawEdges"),et=(0,h.K2)((async function(t,e){const i=.75*q("padding"),o=q("fontSize"),s=q("iconSize")/2;await Promise.all(e.nodes().map((async e=>{const a=O(e);if("group"===a.type){const{h:l,w:c,x1:d,y1:g}=e.boundingBox();t.append("rect").attr("x",d+s).attr("y",g+s).attr("width",c).attr("height",l).attr("class","node-bkg");const u=t.append("g");let f=d,p=g;if(a.icon){const t=u.append("g");t.html(`<g>${await(0,n.WY)(a.icon,{height:i,width:i,fallbackPrefix:J.prefix})}</g>`),t.attr("transform","translate("+(f+s+1)+", "+(p+s+1)+")"),f+=i,p+=o/2-1-2}if(a.label){const t=u.append("g");await(0,r.GZ)(t,a.label,{useHtmlLabels:!1,width:c,classes:"architecture-service-label"},(0,h.D7)()),t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),t.attr("transform","translate("+(f+s+4)+", "+(p+s+2)+")")}}})))}),"drawGroups"),it=(0,h.K2)((async function(t,e,i){for(const o of i){const i=e.append("g"),s=q("iconSize");if(o.title){const t=i.append("g");await(0,r.GZ)(t,o.title,{useHtmlLabels:!1,width:1.5*s,classes:"architecture-service-label"},(0,h.D7)()),t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),t.attr("transform","translate("+s/2+", "+s+")")}const a=i.append("g");if(o.icon)a.html(`<g>${await(0,n.WY)(o.icon,{height:s,width:s,fallbackPrefix:J.prefix})}</g>`);else if(o.iconText){a.html(`<g>${await(0,n.WY)("blank",{height:s,width:s,fallbackPrefix:J.prefix})}</g>`);const t=a.append("g").append("foreignObject").attr("width",s).attr("height",s).append("div").attr("class","node-icon-text").attr("style",`height: ${s}px;`).append("div").html(o.iconText),e=parseInt(window.getComputedStyle(t.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;t.attr("style",`-webkit-line-clamp: ${Math.floor((s-2)/e)};`)}else a.append("path").attr("class","node-bkg").attr("id","node-"+o.id).attr("d",`M0 ${s} v${-s} q0,-5 5,-5 h${s} q5,0 5,5 v${s} H0 Z`);i.attr("class","architecture-service");const{width:l,height:c}=i._groups[0][0].getBBox();o.width=l,o.height=c,t.setElementForId(o.id,i)}return 0}),"drawServices"),nt=(0,h.K2)((function(t,e,i){i.forEach((i=>{const n=e.append("g"),r=q("iconSize");n.append("g").append("rect").attr("id","node-"+i.id).attr("fill-opacity","0").attr("width",r).attr("height",r),n.attr("class","architecture-junction");const{width:o,height:s}=n._groups[0][0].getBBox();n.width=o,n.height=s,t.setElementForId(i.id,n)}))}),"drawJunctions");function rt(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"service",id:t.id,icon:t.icon,label:t.title,parent:t.in,width:q("iconSize"),height:q("iconSize")},classes:"node-service"})}))}function ot(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"junction",id:t.id,parent:t.in,width:q("iconSize"),height:q("iconSize")},classes:"node-junction"})}))}function st(t,e){e.nodes().map((e=>{const i=O(e);if("group"===i.type)return;i.x=e.position().x,i.y=e.position().y;t.getElementById(i.id).attr("transform","translate("+(i.x||0)+","+(i.y||0)+")")}))}function at(t,e){t.forEach((t=>{e.add({group:"nodes",data:{type:"group",id:t.id,icon:t.icon,label:t.title,parent:t.in},classes:"node-group"})}))}function ht(t,e){t.forEach((t=>{const{lhsId:i,rhsId:n,lhsInto:r,lhsGroup:o,rhsInto:s,lhsDir:a,rhsDir:h,rhsGroup:l,title:c}=t,d=N(t.lhsDir,t.rhsDir)?"segments":"straight",g={id:`${i}-${n}`,label:c,source:i,sourceDir:a,sourceArrow:r,sourceGroup:o,sourceEndpoint:"L"===a?"0 50%":"R"===a?"100% 50%":"T"===a?"50% 0":"50% 100%",target:n,targetDir:h,targetArrow:s,targetGroup:l,targetEndpoint:"L"===h?"0 50%":"R"===h?"100% 50%":"T"===h?"50% 0":"50% 100%"};e.add({group:"edges",data:g,classes:d})}))}function lt(t,e,i){const n=(0,h.K2)(((t,e)=>Object.entries(t).reduce(((t,[n,r])=>{let o=0;const s=Object.entries(r);if(1===s.length)return t[n]=s[0][1],t;for(let a=0;a<s.length-1;a++)for(let r=a+1;r<s.length;r++){const[h,l]=s[a],[c,d]=s[r],g=i[h]?.[c];if(g===e)t[n]??=[],t[n]=[...t[n],...l,...d];else if("default"===h||"default"===c)t[n]??=[],t[n]=[...t[n],...l,...d];else{t[`${n}-${o++}`]=l;t[`${n}-${o++}`]=d}}return t}),{})),"flattenAlignments"),r=e.map((e=>{const i={},r={};return Object.entries(e).forEach((([e,[n,o]])=>{const s=t.getNode(e)?.in??"default";i[o]??={},i[o][s]??=[],i[o][s].push(e),r[n]??={},r[n][s]??=[],r[n][s].push(e)})),{horiz:Object.values(n(i,"horizontal")).filter((t=>t.length>1)),vert:Object.values(n(r,"vertical")).filter((t=>t.length>1))}})),[o,s]=r.reduce((([t,e],{horiz:i,vert:n})=>[[...t,...i],[...e,...n]]),[[],[]]);return{horizontal:o,vertical:s}}function ct(t){const e=[],i=(0,h.K2)((t=>`${t[0]},${t[1]}`),"posToStr"),n=(0,h.K2)((t=>t.split(",").map((t=>parseInt(t)))),"strToPos");return t.forEach((t=>{const r=Object.fromEntries(Object.entries(t).map((([t,e])=>[i(e),t]))),o=[i([0,0])],s={},a={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;o.length>0;){const t=o.shift();if(t){s[t]=1;const h=r[t];if(h){const l=n(t);Object.entries(a).forEach((([t,n])=>{const a=i([l[0]+n[0],l[1]+n[1]]),c=r[a];c&&!s[a]&&(o.push(a),e.push({[u[t]]:c,[u[v(t)]]:h,gap:1.5*q("iconSize")}))}))}}}})),e}function dt(t,e,i,n,r,{spatialMaps:o,groupAlignments:s}){return new Promise((a=>{const l=(0,g.Ltv)("body").append("div").attr("id","cy").attr("style","display:none"),d=(0,c.A)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${q("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${q("padding")}px`}}]});l.remove(),at(i,d),rt(t,d),ot(e,d),ht(n,d);const u=lt(r,o,s),f=ct(o),p=d.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){const[e,i]=t.connectedNodes(),{parent:n}=O(e),{parent:r}=O(i);return n===r?1.5*q("iconSize"):.5*q("iconSize")},edgeElasticity(t){const[e,i]=t.connectedNodes(),{parent:n}=O(e),{parent:r}=O(i);return n===r?.45:.001},alignmentConstraint:u,relativePlacementConstraint:f});p.one("layoutstop",(()=>{function t(t,e,i,n){let r,o;const{x:s,y:a}=t,{x:h,y:l}=e;o=(n-a+(s-i)*(a-l)/(s-h))/Math.sqrt(1+Math.pow((a-l)/(s-h),2)),r=Math.sqrt(Math.pow(n-a,2)+Math.pow(i-s,2)-Math.pow(o,2));r/=Math.sqrt(Math.pow(h-s,2)+Math.pow(l-a,2));let c=(h-s)*(n-a)-(l-a)*(i-s);switch(!0){case c>=0:c=1;break;case c<0:c=-1}let d=(h-s)*(i-s)+(l-a)*(n-a);switch(!0){case d>=0:d=1;break;case d<0:d=-1}return o=Math.abs(o)*c,r*=d,{distances:o,weights:r}}(0,h.K2)(t,"getSegmentWeights"),d.startBatch();for(const e of Object.values(d.edges()))if(e.data?.()){const{x:i,y:n}=e.source().position(),{x:r,y:o}=e.target().position();if(i!==r&&n!==o){const i=e.sourceEndpoint(),n=e.targetEndpoint(),{sourceDir:r}=x(e),[o,s]=E(r)?[i.x,n.y]:[n.x,i.y],{weights:a,distances:h}=t(i,n,o,s);e.style("segment-distances",h),e.style("segment-weights",a)}}d.endBatch(),p.run()})),p.run(),d.ready((t=>{h.Rm.info("Ready",t),a(d)}))}))}(0,n.pC)([{name:J.prefix,icons:J}]),c.A.use(d),(0,h.K2)(rt,"addServices"),(0,h.K2)(ot,"addJunctions"),(0,h.K2)(st,"positionNodes"),(0,h.K2)(at,"addGroups"),(0,h.K2)(ht,"addEdges"),(0,h.K2)(lt,"getAlignments"),(0,h.K2)(ct,"getRelativeConstraints"),(0,h.K2)(dt,"layoutArchitecture");var gt={parser:K,db:j,renderer:{draw:(0,h.K2)((async(t,e,i,n)=>{const r=n.db,o=r.getServices(),s=r.getJunctions(),l=r.getGroups(),c=r.getEdges(),d=r.getDataStructures(),g=(0,a.D)(e),u=g.append("g");u.attr("class","architecture-edges");const f=g.append("g");f.attr("class","architecture-services");const p=g.append("g");p.attr("class","architecture-groups"),await it(r,f,o),nt(r,f,s);const v=await dt(o,s,l,c,r,d);await tt(u,v),await et(p,v),st(r,v),(0,h.ot)(void 0,g,q("padding"),q("useMaxWidth"))}),"draw")},styles:Z}}}]);