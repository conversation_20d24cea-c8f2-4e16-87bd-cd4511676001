(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[4251],{445:function(t){t.exports=function(){"use strict";var t={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},e=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\d/,i=/\d\d/,s=/\d\d?/,r=/\d*[^-_:/,()\s\d]+/,a={},o=function(t){return(t=+t)+(t>68?1900:2e3)},c=function(t){return function(e){this[t]=+e}},l=[/[+-]\d\d:?(\d\d)?|Z/,function(t){(this.zone||(this.zone={})).offset=function(t){if(!t)return 0;if("Z"===t)return 0;var e=t.match(/([+-]|\d\d)/g),n=60*e[1]+(+e[2]||0);return 0===n?0:"+"===e[0]?-n:n}(t)}],d=function(t){var e=a[t];return e&&(e.indexOf?e:e.s.concat(e.f))},u=function(t,e){var n,i=a.meridiem;if(i){for(var s=1;s<=24;s+=1)if(t.indexOf(i(s,0,e))>-1){n=s>12;break}}else n=t===(e?"pm":"PM");return n},h={A:[r,function(t){this.afternoon=u(t,!1)}],a:[r,function(t){this.afternoon=u(t,!0)}],Q:[n,function(t){this.month=3*(t-1)+1}],S:[n,function(t){this.milliseconds=100*+t}],SS:[i,function(t){this.milliseconds=10*+t}],SSS:[/\d{3}/,function(t){this.milliseconds=+t}],s:[s,c("seconds")],ss:[s,c("seconds")],m:[s,c("minutes")],mm:[s,c("minutes")],H:[s,c("hours")],h:[s,c("hours")],HH:[s,c("hours")],hh:[s,c("hours")],D:[s,c("day")],DD:[i,c("day")],Do:[r,function(t){var e=a.ordinal,n=t.match(/\d+/);if(this.day=n[0],e)for(var i=1;i<=31;i+=1)e(i).replace(/\[|\]/g,"")===t&&(this.day=i)}],w:[s,c("week")],ww:[i,c("week")],M:[s,c("month")],MM:[i,c("month")],MMM:[r,function(t){var e=d("months"),n=(d("monthsShort")||e.map((function(t){return t.slice(0,3)}))).indexOf(t)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[r,function(t){var e=d("months").indexOf(t)+1;if(e<1)throw new Error;this.month=e%12||e}],Y:[/[+-]?\d+/,c("year")],YY:[i,function(t){this.year=o(t)}],YYYY:[/\d{4}/,c("year")],Z:l,ZZ:l};function f(n){var i,s;i=n,s=a&&a.formats;for(var r=(n=i.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(e,n,i){var r=i&&i.toUpperCase();return n||s[i]||t[i]||s[r].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(t,e,n){return e||n.slice(1)}))}))).match(e),o=r.length,c=0;c<o;c+=1){var l=r[c],d=h[l],u=d&&d[0],f=d&&d[1];r[c]=f?{regex:u,parser:f}:l.replace(/^\[|\]$/g,"")}return function(t){for(var e={},n=0,i=0;n<o;n+=1){var s=r[n];if("string"==typeof s)i+=s.length;else{var a=s.regex,c=s.parser,l=t.slice(i),d=a.exec(l)[0];c.call(e,d),t=t.replace(d,"")}}return function(t){var e=t.afternoon;if(void 0!==e){var n=t.hours;e?n<12&&(t.hours+=12):12===n&&(t.hours=0),delete t.afternoon}}(e),e}}return function(t,e,n){n.p.customParseFormat=!0,t&&t.parseTwoDigitYear&&(o=t.parseTwoDigitYear);var i=e.prototype,s=i.parse;i.parse=function(t){var e=t.date,i=t.utc,r=t.args;this.$u=i;var o=r[1];if("string"==typeof o){var c=!0===r[2],l=!0===r[3],d=c||l,u=r[2];l&&(u=r[2]),a=this.$locale(),!c&&u&&(a=n.Ls[u]),this.$d=function(t,e,n,i){try{if(["x","X"].indexOf(e)>-1)return new Date(("X"===e?1e3:1)*t);var s=f(e)(t),r=s.year,a=s.month,o=s.day,c=s.hours,l=s.minutes,d=s.seconds,u=s.milliseconds,h=s.zone,y=s.week,k=new Date,m=o||(r||a?1:k.getDate()),p=r||k.getFullYear(),g=0;r&&!a||(g=a>0?a-1:k.getMonth());var b,T=c||0,x=l||0,v=d||0,w=u||0;return h?new Date(Date.UTC(p,g,m,T,x,v,w+60*h.offset*1e3)):n?new Date(Date.UTC(p,g,m,T,x,v,w)):(b=new Date(p,g,m,T,x,v,w),y&&(b=i(b).week(y).toDate()),b)}catch(t){return new Date("")}}(e,o,i,n),this.init(),u&&!0!==u&&(this.$L=this.locale(u).$L),d&&e!=this.format(o)&&(this.$d=new Date("")),a={}}else if(o instanceof Array)for(var h=o.length,y=1;y<=h;y+=1){r[1]=o[y-1];var k=n.apply(this,r);if(k.isValid()){this.$d=k.$d,this.$L=k.$L,this.init();break}y===h&&(this.$d=new Date(""))}else s.call(this,t)}}}()},4251:(t,e,n)=>{"use strict";n.d(e,{diagram:()=>Yt});var i=n(5395),s=n(2107),r=n(6750),a=n(4353),o=n(8313),c=n(445),l=n(7375),d=n(7),u=function(){var t=(0,s.K2)((function(t,e,n,i){for(n=n||{},i=t.length;i--;n[t[i]]=e);return n}),"o"),e=[6,8,10,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,38,40],n=[1,26],i=[1,27],r=[1,28],a=[1,29],o=[1,30],c=[1,31],l=[1,32],d=[1,33],u=[1,34],h=[1,9],f=[1,10],y=[1,11],k=[1,12],m=[1,13],p=[1,14],g=[1,15],b=[1,16],T=[1,19],x=[1,20],v=[1,21],w=[1,22],_=[1,23],D=[1,25],$=[1,35],C={trace:(0,s.K2)((function(){}),"trace"),yy:{},symbols_:{error:2,start:3,gantt:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NL:10,weekday:11,weekday_monday:12,weekday_tuesday:13,weekday_wednesday:14,weekday_thursday:15,weekday_friday:16,weekday_saturday:17,weekday_sunday:18,weekend:19,weekend_friday:20,weekend_saturday:21,dateFormat:22,inclusiveEndDates:23,topAxis:24,axisFormat:25,tickInterval:26,excludes:27,includes:28,todayMarker:29,title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,section:36,clickStatement:37,taskTxt:38,taskData:39,click:40,callbackname:41,callbackargs:42,href:43,clickStatementDebug:44,$accept:0,$end:1},terminals_:{2:"error",4:"gantt",6:"EOF",8:"SPACE",10:"NL",12:"weekday_monday",13:"weekday_tuesday",14:"weekday_wednesday",15:"weekday_thursday",16:"weekday_friday",17:"weekday_saturday",18:"weekday_sunday",20:"weekend_friday",21:"weekend_saturday",22:"dateFormat",23:"inclusiveEndDates",24:"topAxis",25:"axisFormat",26:"tickInterval",27:"excludes",28:"includes",29:"todayMarker",30:"title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"section",38:"taskTxt",39:"taskData",40:"click",41:"callbackname",42:"callbackargs",43:"href"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[11,1],[19,1],[19,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,2],[37,2],[37,3],[37,3],[37,4],[37,3],[37,4],[37,2],[44,2],[44,3],[44,3],[44,4],[44,3],[44,4],[44,2]],performAction:(0,s.K2)((function(t,e,n,i,s,r,a){var o=r.length-1;switch(s){case 1:return r[o-1];case 2:case 6:case 7:this.$=[];break;case 3:r[o-1].push(r[o]),this.$=r[o-1];break;case 4:case 5:this.$=r[o];break;case 8:i.setWeekday("monday");break;case 9:i.setWeekday("tuesday");break;case 10:i.setWeekday("wednesday");break;case 11:i.setWeekday("thursday");break;case 12:i.setWeekday("friday");break;case 13:i.setWeekday("saturday");break;case 14:i.setWeekday("sunday");break;case 15:i.setWeekend("friday");break;case 16:i.setWeekend("saturday");break;case 17:i.setDateFormat(r[o].substr(11)),this.$=r[o].substr(11);break;case 18:i.enableInclusiveEndDates(),this.$=r[o].substr(18);break;case 19:i.TopAxis(),this.$=r[o].substr(8);break;case 20:i.setAxisFormat(r[o].substr(11)),this.$=r[o].substr(11);break;case 21:i.setTickInterval(r[o].substr(13)),this.$=r[o].substr(13);break;case 22:i.setExcludes(r[o].substr(9)),this.$=r[o].substr(9);break;case 23:i.setIncludes(r[o].substr(9)),this.$=r[o].substr(9);break;case 24:i.setTodayMarker(r[o].substr(12)),this.$=r[o].substr(12);break;case 27:i.setDiagramTitle(r[o].substr(6)),this.$=r[o].substr(6);break;case 28:this.$=r[o].trim(),i.setAccTitle(this.$);break;case 29:case 30:this.$=r[o].trim(),i.setAccDescription(this.$);break;case 31:i.addSection(r[o].substr(8)),this.$=r[o].substr(8);break;case 33:i.addTask(r[o-1],r[o]),this.$="task";break;case 34:this.$=r[o-1],i.setClickEvent(r[o-1],r[o],null);break;case 35:this.$=r[o-2],i.setClickEvent(r[o-2],r[o-1],r[o]);break;case 36:this.$=r[o-2],i.setClickEvent(r[o-2],r[o-1],null),i.setLink(r[o-2],r[o]);break;case 37:this.$=r[o-3],i.setClickEvent(r[o-3],r[o-2],r[o-1]),i.setLink(r[o-3],r[o]);break;case 38:this.$=r[o-2],i.setClickEvent(r[o-2],r[o],null),i.setLink(r[o-2],r[o-1]);break;case 39:this.$=r[o-3],i.setClickEvent(r[o-3],r[o-1],r[o]),i.setLink(r[o-3],r[o-2]);break;case 40:this.$=r[o-1],i.setLink(r[o-1],r[o]);break;case 41:case 47:this.$=r[o-1]+" "+r[o];break;case 42:case 43:case 45:this.$=r[o-2]+" "+r[o-1]+" "+r[o];break;case 44:case 46:this.$=r[o-3]+" "+r[o-2]+" "+r[o-1]+" "+r[o]}}),"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},t(e,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:17,12:n,13:i,14:r,15:a,16:o,17:c,18:l,19:18,20:d,21:u,22:h,23:f,24:y,25:k,26:m,27:p,28:g,29:b,30:T,31:x,33:v,35:w,36:_,37:24,38:D,40:$},t(e,[2,7],{1:[2,1]}),t(e,[2,3]),{9:36,11:17,12:n,13:i,14:r,15:a,16:o,17:c,18:l,19:18,20:d,21:u,22:h,23:f,24:y,25:k,26:m,27:p,28:g,29:b,30:T,31:x,33:v,35:w,36:_,37:24,38:D,40:$},t(e,[2,5]),t(e,[2,6]),t(e,[2,17]),t(e,[2,18]),t(e,[2,19]),t(e,[2,20]),t(e,[2,21]),t(e,[2,22]),t(e,[2,23]),t(e,[2,24]),t(e,[2,25]),t(e,[2,26]),t(e,[2,27]),{32:[1,37]},{34:[1,38]},t(e,[2,30]),t(e,[2,31]),t(e,[2,32]),{39:[1,39]},t(e,[2,8]),t(e,[2,9]),t(e,[2,10]),t(e,[2,11]),t(e,[2,12]),t(e,[2,13]),t(e,[2,14]),t(e,[2,15]),t(e,[2,16]),{41:[1,40],43:[1,41]},t(e,[2,4]),t(e,[2,28]),t(e,[2,29]),t(e,[2,33]),t(e,[2,34],{42:[1,42],43:[1,43]}),t(e,[2,40],{41:[1,44]}),t(e,[2,35],{43:[1,45]}),t(e,[2,36]),t(e,[2,38],{42:[1,46]}),t(e,[2,37]),t(e,[2,39])],defaultActions:{},parseError:(0,s.K2)((function(t,e){if(!e.recoverable){var n=new Error(t);throw n.hash=e,n}this.trace(t)}),"parseError"),parse:(0,s.K2)((function(t){var e=this,n=[0],i=[],r=[null],a=[],o=this.table,c="",l=0,d=0,u=0,h=a.slice.call(arguments,1),f=Object.create(this.lexer),y={yy:{}};for(var k in this.yy)Object.prototype.hasOwnProperty.call(this.yy,k)&&(y.yy[k]=this.yy[k]);f.setInput(t,y.yy),y.yy.lexer=f,y.yy.parser=this,void 0===f.yylloc&&(f.yylloc={});var m=f.yylloc;a.push(m);var p=f.options&&f.options.ranges;function g(){var t;return"number"!=typeof(t=i.pop()||f.lex()||1)&&(t instanceof Array&&(t=(i=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof y.yy.parseError?this.parseError=y.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,s.K2)((function(t){n.length=n.length-2*t,r.length=r.length-t,a.length=a.length-t}),"popStack"),(0,s.K2)(g,"lex");for(var b,T,x,v,w,_,D,$,C,S={};;){if(x=n[n.length-1],this.defaultActions[x]?v=this.defaultActions[x]:(null==b&&(b=g()),v=o[x]&&o[x][b]),void 0===v||!v.length||!v[0]){var K="";for(_ in C=[],o[x])this.terminals_[_]&&_>2&&C.push("'"+this.terminals_[_]+"'");K=f.showPosition?"Parse error on line "+(l+1)+":\n"+f.showPosition()+"\nExpecting "+C.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==b?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(K,{text:f.match,token:this.terminals_[b]||b,line:f.yylineno,loc:m,expected:C})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+x+", token: "+b);switch(v[0]){case 1:n.push(b),r.push(f.yytext),a.push(f.yylloc),n.push(v[1]),b=null,T?(b=T,T=null):(d=f.yyleng,c=f.yytext,l=f.yylineno,m=f.yylloc,u>0&&u--);break;case 2:if(D=this.productions_[v[1]][1],S.$=r[r.length-D],S._$={first_line:a[a.length-(D||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(D||1)].first_column,last_column:a[a.length-1].last_column},p&&(S._$.range=[a[a.length-(D||1)].range[0],a[a.length-1].range[1]]),void 0!==(w=this.performAction.apply(S,[c,d,l,y.yy,v[1],r,a].concat(h))))return w;D&&(n=n.slice(0,-1*D*2),r=r.slice(0,-1*D),a=a.slice(0,-1*D)),n.push(this.productions_[v[1]][0]),r.push(S.$),a.push(S._$),$=o[n[n.length-2]][n[n.length-1]],n.push($);break;case 3:return!0}}return!0}),"parse")},S=function(){return{EOF:1,parseError:(0,s.K2)((function(t,e){if(!this.yy.parser)throw new Error(t);this.yy.parser.parseError(t,e)}),"parseError"),setInput:(0,s.K2)((function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this}),"setInput"),input:(0,s.K2)((function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t}),"input"),unput:(0,s.K2)((function(t){var e=t.length,n=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),n.length-1&&(this.yylineno-=n.length-1);var s=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:n?(n.length===i.length?this.yylloc.first_column:0)+i[i.length-n.length].length-n[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[s[0],s[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this}),"unput"),more:(0,s.K2)((function(){return this._more=!0,this}),"more"),reject:(0,s.K2)((function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"reject"),less:(0,s.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,s.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,s.K2)((function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,s.K2)((function(){var t=this.pastInput(),e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,s.K2)((function(t,e){var n,i,s;if(this.options.backtrack_lexer&&(s={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(s.yylloc.range=this.yylloc.range.slice(0))),(i=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],n=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),n)return n;if(this._backtrack){for(var r in s)this[r]=s[r];return!1}return!1}),"test_match"),next:(0,s.K2)((function(){if(this.done)return this.EOF;var t,e,n,i;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var s=this._currentRules(),r=0;r<s.length;r++)if((n=this._input.match(this.rules[s[r]]))&&(!e||n[0].length>e[0].length)){if(e=n,i=r,this.options.backtrack_lexer){if(!1!==(t=this.test_match(n,s[r])))return t;if(this._backtrack){e=!1;continue}return!1}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,s[i]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}),"next"),lex:(0,s.K2)((function(){var t=this.next();return t||this.lex()}),"lex"),begin:(0,s.K2)((function(t){this.conditionStack.push(t)}),"begin"),popState:(0,s.K2)((function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]}),"popState"),_currentRules:(0,s.K2)((function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules}),"_currentRules"),topState:(0,s.K2)((function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"}),"topState"),pushState:(0,s.K2)((function(t){this.begin(t)}),"pushState"),stateStackSize:(0,s.K2)((function(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,s.K2)((function(t,e,n,i){switch(n){case 0:return this.begin("open_directive"),"open_directive";case 1:return this.begin("acc_title"),31;case 2:return this.popState(),"acc_title_value";case 3:return this.begin("acc_descr"),33;case 4:return this.popState(),"acc_descr_value";case 5:this.begin("acc_descr_multiline");break;case 6:case 15:case 18:case 21:case 24:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:case 9:case 10:case 12:case 13:break;case 11:return 10;case 14:this.begin("href");break;case 16:return 43;case 17:this.begin("callbackname");break;case 19:this.popState(),this.begin("callbackargs");break;case 20:return 41;case 22:return 42;case 23:this.begin("click");break;case 25:return 40;case 26:return 4;case 27:return 22;case 28:return 23;case 29:return 24;case 30:return 25;case 31:return 26;case 32:return 28;case 33:return 27;case 34:return 29;case 35:return 12;case 36:return 13;case 37:return 14;case 38:return 15;case 39:return 16;case 40:return 17;case 41:return 18;case 42:return 20;case 43:return 21;case 44:return"date";case 45:return 30;case 46:return"accDescription";case 47:return 36;case 48:return 38;case 49:return 39;case 50:return":";case 51:return 6;case 52:return"INVALID"}}),"anonymous"),rules:[/^(?:%%\{)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:%%(?!\{)*[^\n]*)/i,/^(?:[^\}]%%*[^\n]*)/i,/^(?:%%*[^\n]*[\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:%[^\n]*)/i,/^(?:href[\s]+["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:call[\s]+)/i,/^(?:\([\s]*\))/i,/^(?:\()/i,/^(?:[^(]*)/i,/^(?:\))/i,/^(?:[^)]*)/i,/^(?:click[\s]+)/i,/^(?:[\s\n])/i,/^(?:[^\s\n]*)/i,/^(?:gantt\b)/i,/^(?:dateFormat\s[^#\n;]+)/i,/^(?:inclusiveEndDates\b)/i,/^(?:topAxis\b)/i,/^(?:axisFormat\s[^#\n;]+)/i,/^(?:tickInterval\s[^#\n;]+)/i,/^(?:includes\s[^#\n;]+)/i,/^(?:excludes\s[^#\n;]+)/i,/^(?:todayMarker\s[^\n;]+)/i,/^(?:weekday\s+monday\b)/i,/^(?:weekday\s+tuesday\b)/i,/^(?:weekday\s+wednesday\b)/i,/^(?:weekday\s+thursday\b)/i,/^(?:weekday\s+friday\b)/i,/^(?:weekday\s+saturday\b)/i,/^(?:weekday\s+sunday\b)/i,/^(?:weekend\s+friday\b)/i,/^(?:weekend\s+saturday\b)/i,/^(?:\d\d\d\d-\d\d-\d\d\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accDescription\s[^#\n;]+)/i,/^(?:section\s[^\n]+)/i,/^(?:[^:\n]+)/i,/^(?::[^#\n;]+)/i,/^(?::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[6,7],inclusive:!1},acc_descr:{rules:[4],inclusive:!1},acc_title:{rules:[2],inclusive:!1},callbackargs:{rules:[21,22],inclusive:!1},callbackname:{rules:[18,19,20],inclusive:!1},href:{rules:[15,16],inclusive:!1},click:{rules:[24,25],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,17,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],inclusive:!0}}}}();function K(){this.yy={}}return C.lexer=S,(0,s.K2)(K,"Parser"),K.prototype=C,C.Parser=K,new K}();u.parser=u;var h=u;a.extend(o),a.extend(c),a.extend(l);var f,y,k={friday:5,saturday:6},m="",p="",g=void 0,b="",T=[],x=[],v=new Map,w=[],_=[],D="",$="",C=["active","done","crit","milestone"],S=[],K=!1,E=!1,M="sunday",A="saturday",L=0,Y=(0,s.K2)((function(){w=[],_=[],D="",S=[],ht=0,f=void 0,y=void 0,mt=[],m="",p="",$="",g=void 0,b="",T=[],x=[],K=!1,E=!1,L=0,v=new Map,(0,s.IU)(),M="sunday",A="saturday"}),"clear"),I=(0,s.K2)((function(t){p=t}),"setAxisFormat"),F=(0,s.K2)((function(){return p}),"getAxisFormat"),W=(0,s.K2)((function(t){g=t}),"setTickInterval"),O=(0,s.K2)((function(){return g}),"getTickInterval"),P=(0,s.K2)((function(t){b=t}),"setTodayMarker"),B=(0,s.K2)((function(){return b}),"getTodayMarker"),z=(0,s.K2)((function(t){m=t}),"setDateFormat"),N=(0,s.K2)((function(){K=!0}),"enableInclusiveEndDates"),G=(0,s.K2)((function(){return K}),"endDatesAreInclusive"),H=(0,s.K2)((function(){E=!0}),"enableTopAxis"),R=(0,s.K2)((function(){return E}),"topAxisEnabled"),j=(0,s.K2)((function(t){$=t}),"setDisplayMode"),U=(0,s.K2)((function(){return $}),"getDisplayMode"),V=(0,s.K2)((function(){return m}),"getDateFormat"),Z=(0,s.K2)((function(t){T=t.toLowerCase().split(/[\s,]+/)}),"setIncludes"),X=(0,s.K2)((function(){return T}),"getIncludes"),q=(0,s.K2)((function(t){x=t.toLowerCase().split(/[\s,]+/)}),"setExcludes"),Q=(0,s.K2)((function(){return x}),"getExcludes"),J=(0,s.K2)((function(){return v}),"getLinks"),tt=(0,s.K2)((function(t){D=t,w.push(t)}),"addSection"),et=(0,s.K2)((function(){return w}),"getSections"),nt=(0,s.K2)((function(){let t=xt();let e=0;for(;!t&&e<10;)t=xt(),e++;return _=mt}),"getTasks"),it=(0,s.K2)((function(t,e,n,i){return!i.includes(t.format(e.trim()))&&(!(!n.includes("weekends")||t.isoWeekday()!==k[A]&&t.isoWeekday()!==k[A]+1)||(!!n.includes(t.format("dddd").toLowerCase())||n.includes(t.format(e.trim()))))}),"isInvalidDate"),st=(0,s.K2)((function(t){M=t}),"setWeekday"),rt=(0,s.K2)((function(){return M}),"getWeekday"),at=(0,s.K2)((function(t){A=t}),"setWeekend"),ot=(0,s.K2)((function(t,e,n,i){if(!n.length||t.manualEndTime)return;let s,r;s=t.startTime instanceof Date?a(t.startTime):a(t.startTime,e,!0),s=s.add(1,"d"),r=t.endTime instanceof Date?a(t.endTime):a(t.endTime,e,!0);const[o,c]=ct(s,r,e,n,i);t.endTime=o.toDate(),t.renderEndTime=c}),"checkTaskDates"),ct=(0,s.K2)((function(t,e,n,i,s){let r=!1,a=null;for(;t<=e;)r||(a=e.toDate()),r=it(t,n,i,s),r&&(e=e.add(1,"d")),t=t.add(1,"d");return[e,a]}),"fixTaskDates"),lt=(0,s.K2)((function(t,e,n){n=n.trim();const i=/^after\s+(?<ids>[\d\w- ]+)/.exec(n);if(null!==i){let t=null;for(const n of i.groups.ids.split(" ")){let e=bt(n);void 0!==e&&(!t||e.endTime>t.endTime)&&(t=e)}if(t)return t.endTime;const e=new Date;return e.setHours(0,0,0,0),e}let r=a(n,e.trim(),!0);if(r.isValid())return r.toDate();{s.Rm.debug("Invalid date:"+n),s.Rm.debug("With date format:"+e.trim());const t=new Date(n);if(void 0===t||isNaN(t.getTime())||t.getFullYear()<-1e4||t.getFullYear()>1e4)throw new Error("Invalid date:"+n);return t}}),"getStartDate"),dt=(0,s.K2)((function(t){const e=/^(\d+(?:\.\d+)?)([Mdhmswy]|ms)$/.exec(t.trim());return null!==e?[Number.parseFloat(e[1]),e[2]]:[NaN,"ms"]}),"parseDuration"),ut=(0,s.K2)((function(t,e,n,i=!1){n=n.trim();const s=/^until\s+(?<ids>[\d\w- ]+)/.exec(n);if(null!==s){let t=null;for(const n of s.groups.ids.split(" ")){let e=bt(n);void 0!==e&&(!t||e.startTime<t.startTime)&&(t=e)}if(t)return t.startTime;const e=new Date;return e.setHours(0,0,0,0),e}let r=a(n,e.trim(),!0);if(r.isValid())return i&&(r=r.add(1,"d")),r.toDate();let o=a(t);const[c,l]=dt(n);if(!Number.isNaN(c)){const t=o.add(c,l);t.isValid()&&(o=t)}return o.toDate()}),"getEndDate"),ht=0,ft=(0,s.K2)((function(t){return void 0===t?"task"+(ht+=1):t}),"parseId"),yt=(0,s.K2)((function(t,e){let n;n=":"===e.substr(0,1)?e.substr(1,e.length):e;const i=n.split(","),s={};Kt(i,s,C);for(let a=0;a<i.length;a++)i[a]=i[a].trim();let r="";switch(i.length){case 1:s.id=ft(),s.startTime=t.endTime,r=i[0];break;case 2:s.id=ft(),s.startTime=lt(void 0,m,i[0]),r=i[1];break;case 3:s.id=ft(i[0]),s.startTime=lt(void 0,m,i[1]),r=i[2]}return r&&(s.endTime=ut(s.startTime,m,r,K),s.manualEndTime=a(r,"YYYY-MM-DD",!0).isValid(),ot(s,m,x,T)),s}),"compileData"),kt=(0,s.K2)((function(t,e){let n;n=":"===e.substr(0,1)?e.substr(1,e.length):e;const i=n.split(","),s={};Kt(i,s,C);for(let r=0;r<i.length;r++)i[r]=i[r].trim();switch(i.length){case 1:s.id=ft(),s.startTime={type:"prevTaskEnd",id:t},s.endTime={data:i[0]};break;case 2:s.id=ft(),s.startTime={type:"getStartDate",startData:i[0]},s.endTime={data:i[1]};break;case 3:s.id=ft(i[0]),s.startTime={type:"getStartDate",startData:i[1]},s.endTime={data:i[2]}}return s}),"parseData"),mt=[],pt={},gt=(0,s.K2)((function(t,e){const n={section:D,type:D,processed:!1,manualEndTime:!1,renderEndTime:null,raw:{data:e},task:t,classes:[]},i=kt(y,e);n.raw.startTime=i.startTime,n.raw.endTime=i.endTime,n.id=i.id,n.prevTaskId=y,n.active=i.active,n.done=i.done,n.crit=i.crit,n.milestone=i.milestone,n.order=L,L++;const s=mt.push(n);y=n.id,pt[n.id]=s-1}),"addTask"),bt=(0,s.K2)((function(t){const e=pt[t];return mt[e]}),"findTaskById"),Tt=(0,s.K2)((function(t,e){const n={section:D,type:D,description:t,task:t,classes:[]},i=yt(f,e);n.startTime=i.startTime,n.endTime=i.endTime,n.id=i.id,n.active=i.active,n.done=i.done,n.crit=i.crit,n.milestone=i.milestone,f=n,_.push(n)}),"addTaskOrg"),xt=(0,s.K2)((function(){const t=(0,s.K2)((function(t){const e=mt[t];let n="";switch(mt[t].raw.startTime.type){case"prevTaskEnd":{const t=bt(e.prevTaskId);e.startTime=t.endTime;break}case"getStartDate":n=lt(void 0,m,mt[t].raw.startTime.startData),n&&(mt[t].startTime=n)}return mt[t].startTime&&(mt[t].endTime=ut(mt[t].startTime,m,mt[t].raw.endTime.data,K),mt[t].endTime&&(mt[t].processed=!0,mt[t].manualEndTime=a(mt[t].raw.endTime.data,"YYYY-MM-DD",!0).isValid(),ot(mt[t],m,x,T))),mt[t].processed}),"compileTask");let e=!0;for(const[n,i]of mt.entries())t(n),e=e&&i.processed;return e}),"compileTasks"),vt=(0,s.K2)((function(t,e){let n=e;"loose"!==(0,s.D7)().securityLevel&&(n=(0,r.J)(e)),t.split(",").forEach((function(t){void 0!==bt(t)&&(Dt(t,(()=>{window.open(n,"_self")})),v.set(t,n))})),wt(t,"clickable")}),"setLink"),wt=(0,s.K2)((function(t,e){t.split(",").forEach((function(t){let n=bt(t);void 0!==n&&n.classes.push(e)}))}),"setClass"),_t=(0,s.K2)((function(t,e,n){if("loose"!==(0,s.D7)().securityLevel)return;if(void 0===e)return;let r=[];if("string"==typeof n){r=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let t=0;t<r.length;t++){let e=r[t].trim();e.startsWith('"')&&e.endsWith('"')&&(e=e.substr(1,e.length-2)),r[t]=e}}0===r.length&&r.push(t),void 0!==bt(t)&&Dt(t,(()=>{i._K.runFunc(e,...r)}))}),"setClickFun"),Dt=(0,s.K2)((function(t,e){S.push((function(){const n=document.querySelector(`[id="${t}"]`);null!==n&&n.addEventListener("click",(function(){e()}))}),(function(){const n=document.querySelector(`[id="${t}-text"]`);null!==n&&n.addEventListener("click",(function(){e()}))}))}),"pushFun"),$t=(0,s.K2)((function(t,e,n){t.split(",").forEach((function(t){_t(t,e,n)})),wt(t,"clickable")}),"setClickEvent"),Ct=(0,s.K2)((function(t){S.forEach((function(e){e(t)}))}),"bindFunctions"),St={getConfig:(0,s.K2)((()=>(0,s.D7)().gantt),"getConfig"),clear:Y,setDateFormat:z,getDateFormat:V,enableInclusiveEndDates:N,endDatesAreInclusive:G,enableTopAxis:H,topAxisEnabled:R,setAxisFormat:I,getAxisFormat:F,setTickInterval:W,getTickInterval:O,setTodayMarker:P,getTodayMarker:B,setAccTitle:s.SV,getAccTitle:s.iN,setDiagramTitle:s.ke,getDiagramTitle:s.ab,setDisplayMode:j,getDisplayMode:U,setAccDescription:s.EI,getAccDescription:s.m7,addSection:tt,getSections:et,getTasks:nt,addTask:gt,findTaskById:bt,addTaskOrg:Tt,setIncludes:Z,getIncludes:X,setExcludes:q,getExcludes:Q,setClickEvent:$t,setLink:vt,getLinks:J,bindFunctions:Ct,parseDuration:dt,isInvalidDate:it,setWeekday:st,getWeekday:rt,setWeekend:at};function Kt(t,e,n){let i=!0;for(;i;)i=!1,n.forEach((function(n){const s=new RegExp("^\\s*"+n+"\\s*$");t[0].match(s)&&(e[n]=!0,t.shift(1),i=!0)}))}(0,s.K2)(Kt,"getTaskTags");var Et,Mt=(0,s.K2)((function(){s.Rm.debug("Something is calling, setConf, remove the call")}),"setConf"),At={monday:d.ABi,tuesday:d.PGu,wednesday:d.GuW,thursday:d.Mol,friday:d.TUC,saturday:d.rGn,sunday:d.YPH},Lt=(0,s.K2)(((t,e)=>{let n=[...t].map((()=>-1/0)),i=[...t].sort(((t,e)=>t.startTime-e.startTime||t.order-e.order)),s=0;for(const r of i)for(let t=0;t<n.length;t++)if(r.startTime>=n[t]){n[t]=r.endTime,r.order=t+e,t>s&&(s=t);break}return s}),"getMaxIntersections"),Yt={parser:h,db:St,renderer:{setConf:Mt,draw:(0,s.K2)((function(t,e,n,i){const r=(0,s.D7)().gantt,o=(0,s.D7)().securityLevel;let c;"sandbox"===o&&(c=(0,d.Ltv)("#i"+e));const l="sandbox"===o?(0,d.Ltv)(c.nodes()[0].contentDocument.body):(0,d.Ltv)("body"),u="sandbox"===o?c.nodes()[0].contentDocument:document,h=u.getElementById(e);void 0===(Et=h.parentElement.offsetWidth)&&(Et=1200),void 0!==r.useWidth&&(Et=r.useWidth);const f=i.db.getTasks();let y=[];for(const s of f)y.push(s.type);y=$(y);const k={};let m=2*r.topPadding;if("compact"===i.db.getDisplayMode()||"compact"===r.displayMode){const t={};for(const n of f)void 0===t[n.section]?t[n.section]=[n]:t[n.section].push(n);let e=0;for(const n of Object.keys(t)){const i=Lt(t[n],e)+1;e+=i,m+=i*(r.barHeight+r.barGap),k[n]=i}}else{m+=f.length*(r.barHeight+r.barGap);for(const t of y)k[t]=f.filter((e=>e.type===t)).length}h.setAttribute("viewBox","0 0 "+Et+" "+m);const p=l.select(`[id="${e}"]`),g=(0,d.w7C)().domain([(0,d.jkA)(f,(function(t){return t.startTime})),(0,d.T9B)(f,(function(t){return t.endTime}))]).rangeRound([0,Et-r.leftPadding-r.rightPadding]);function b(t,e){const n=t.startTime,i=e.startTime;let s=0;return n>i?s=1:n<i&&(s=-1),s}function T(t,e,n){const s=r.barHeight,a=s+r.barGap,o=r.topPadding,c=r.leftPadding,l=(0,d.m4Y)().domain([0,y.length]).range(["#00B9FA","#F95002"]).interpolate(d.bEH);v(a,o,c,e,n,t,i.db.getExcludes(),i.db.getIncludes()),w(c,o,e,n),x(t,a,o,c,s,l,e),_(a,o),D(c,o,e,n)}function x(t,n,a,o,c,l,u){const h=[...new Set(t.map((t=>t.order)))].map((e=>t.find((t=>t.order===e))));p.append("g").selectAll("rect").data(h).enter().append("rect").attr("x",0).attr("y",(function(t,e){return t.order*n+a-2})).attr("width",(function(){return u-r.rightPadding/2})).attr("height",n).attr("class",(function(t){for(const[e,n]of y.entries())if(t.type===n)return"section section"+e%r.numberSectionStyles;return"section section0"}));const f=p.append("g").selectAll("rect").data(t).enter(),k=i.db.getLinks();f.append("rect").attr("id",(function(t){return t.id})).attr("rx",3).attr("ry",3).attr("x",(function(t){return t.milestone?g(t.startTime)+o+.5*(g(t.endTime)-g(t.startTime))-.5*c:g(t.startTime)+o})).attr("y",(function(t,e){return t.order*n+a})).attr("width",(function(t){return t.milestone?c:g(t.renderEndTime||t.endTime)-g(t.startTime)})).attr("height",c).attr("transform-origin",(function(t,e){return e=t.order,(g(t.startTime)+o+.5*(g(t.endTime)-g(t.startTime))).toString()+"px "+(e*n+a+.5*c).toString()+"px"})).attr("class",(function(t){let e="";t.classes.length>0&&(e=t.classes.join(" "));let n=0;for(const[s,a]of y.entries())t.type===a&&(n=s%r.numberSectionStyles);let i="";return t.active?t.crit?i+=" activeCrit":i=" active":t.done?i=t.crit?" doneCrit":" done":t.crit&&(i+=" crit"),0===i.length&&(i=" task"),t.milestone&&(i=" milestone "+i),i+=n,i+=" "+e,"task"+i})),f.append("text").attr("id",(function(t){return t.id+"-text"})).text((function(t){return t.task})).attr("font-size",r.fontSize).attr("x",(function(t){let e=g(t.startTime),n=g(t.renderEndTime||t.endTime);t.milestone&&(e+=.5*(g(t.endTime)-g(t.startTime))-.5*c),t.milestone&&(n=e+c);const i=this.getBBox().width;return i>n-e?n+i+1.5*r.leftPadding>u?e+o-5:n+o+5:(n-e)/2+e+o})).attr("y",(function(t,e){return t.order*n+r.barHeight/2+(r.fontSize/2-2)+a})).attr("text-height",c).attr("class",(function(t){const e=g(t.startTime);let n=g(t.endTime);t.milestone&&(n=e+c);const i=this.getBBox().width;let s="";t.classes.length>0&&(s=t.classes.join(" "));let a=0;for(const[c,l]of y.entries())t.type===l&&(a=c%r.numberSectionStyles);let o="";return t.active&&(o=t.crit?"activeCritText"+a:"activeText"+a),t.done?o=t.crit?o+" doneCritText"+a:o+" doneText"+a:t.crit&&(o=o+" critText"+a),t.milestone&&(o+=" milestoneText"),i>n-e?n+i+1.5*r.leftPadding>u?s+" taskTextOutsideLeft taskTextOutside"+a+" "+o:s+" taskTextOutsideRight taskTextOutside"+a+" "+o+" width-"+i:s+" taskText taskText"+a+" "+o+" width-"+i}));if("sandbox"===(0,s.D7)().securityLevel){let t;t=(0,d.Ltv)("#i"+e);const n=t.nodes()[0].contentDocument;f.filter((function(t){return k.has(t.id)})).each((function(t){var e=n.querySelector("#"+t.id),i=n.querySelector("#"+t.id+"-text");const s=e.parentNode;var r=n.createElement("a");r.setAttribute("xlink:href",k.get(t.id)),r.setAttribute("target","_top"),s.appendChild(r),r.appendChild(e),r.appendChild(i)}))}}function v(t,e,n,o,c,l,d,u){if(0===d.length&&0===u.length)return;let h,f;for(const{startTime:i,endTime:s}of l)(void 0===h||i<h)&&(h=i),(void 0===f||s>f)&&(f=s);if(!h||!f)return;if(a(f).diff(a(h),"year")>5)return void s.Rm.warn("The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.");const y=i.db.getDateFormat(),k=[];let m=null,b=a(h);for(;b.valueOf()<=f;)i.db.isInvalidDate(b,y,d,u)?m?m.end=b:m={start:b,end:b}:m&&(k.push(m),m=null),b=b.add(1,"d");p.append("g").selectAll("rect").data(k).enter().append("rect").attr("id",(function(t){return"exclude-"+t.start.format("YYYY-MM-DD")})).attr("x",(function(t){return g(t.start)+n})).attr("y",r.gridLineStartPadding).attr("width",(function(t){const e=t.end.add(1,"day");return g(e)-g(t.start)})).attr("height",c-e-r.gridLineStartPadding).attr("transform-origin",(function(e,i){return(g(e.start)+n+.5*(g(e.end)-g(e.start))).toString()+"px "+(i*t+.5*c).toString()+"px"})).attr("class","exclude-range")}function w(t,e,n,s){let a=(0,d.l78)(g).tickSize(-s+e+r.gridLineStartPadding).tickFormat((0,d.DCK)(i.db.getAxisFormat()||r.axisFormat||"%Y-%m-%d"));const o=/^([1-9]\d*)(millisecond|second|minute|hour|day|week|month)$/.exec(i.db.getTickInterval()||r.tickInterval);if(null!==o){const t=o[1],e=o[2],n=i.db.getWeekday()||r.weekday;switch(e){case"millisecond":a.ticks(d.t6C.every(t));break;case"second":a.ticks(d.ucG.every(t));break;case"minute":a.ticks(d.wXd.every(t));break;case"hour":a.ticks(d.Agd.every(t));break;case"day":a.ticks(d.UAC.every(t));break;case"week":a.ticks(At[n].every(t));break;case"month":a.ticks(d.Ui6.every(t))}}if(p.append("g").attr("class","grid").attr("transform","translate("+t+", "+(s-50)+")").call(a).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10).attr("dy","1em"),i.db.topAxisEnabled()||r.topAxis){let n=(0,d.tlR)(g).tickSize(-s+e+r.gridLineStartPadding).tickFormat((0,d.DCK)(i.db.getAxisFormat()||r.axisFormat||"%Y-%m-%d"));if(null!==o){const t=o[1],e=o[2],s=i.db.getWeekday()||r.weekday;switch(e){case"millisecond":n.ticks(d.t6C.every(t));break;case"second":n.ticks(d.ucG.every(t));break;case"minute":n.ticks(d.wXd.every(t));break;case"hour":n.ticks(d.Agd.every(t));break;case"day":n.ticks(d.UAC.every(t));break;case"week":n.ticks(At[s].every(t));break;case"month":n.ticks(d.Ui6.every(t))}}p.append("g").attr("class","grid").attr("transform","translate("+t+", "+e+")").call(n).selectAll("text").style("text-anchor","middle").attr("fill","#000").attr("stroke","none").attr("font-size",10)}}function _(t,e){let n=0;const i=Object.keys(k).map((t=>[t,k[t]]));p.append("g").selectAll("text").data(i).enter().append((function(t){const e=t[0].split(s.Y2.lineBreakRegex),n=-(e.length-1)/2,i=u.createElementNS("http://www.w3.org/2000/svg","text");i.setAttribute("dy",n+"em");for(const[s,r]of e.entries()){const t=u.createElementNS("http://www.w3.org/2000/svg","tspan");t.setAttribute("alignment-baseline","central"),t.setAttribute("x","10"),s>0&&t.setAttribute("dy","1em"),t.textContent=r,i.appendChild(t)}return i})).attr("x",10).attr("y",(function(s,r){if(!(r>0))return s[1]*t/2+e;for(let a=0;a<r;a++)return n+=i[r-1][1],s[1]*t/2+n*t+e})).attr("font-size",r.sectionFontSize).attr("class",(function(t){for(const[e,n]of y.entries())if(t[0]===n)return"sectionTitle sectionTitle"+e%r.numberSectionStyles;return"sectionTitle"}))}function D(t,e,n,s){const a=i.db.getTodayMarker();if("off"===a)return;const o=p.append("g").attr("class","today"),c=new Date,l=o.append("line");l.attr("x1",g(c)+t).attr("x2",g(c)+t).attr("y1",r.titleTopMargin).attr("y2",s-r.titleTopMargin).attr("class","today"),""!==a&&l.attr("style",a.replace(/,/g,";"))}function $(t){const e={},n=[];for(let i=0,s=t.length;i<s;++i)Object.prototype.hasOwnProperty.call(e,t[i])||(e[t[i]]=!0,n.push(t[i]));return n}(0,s.K2)(b,"taskCompare"),f.sort(b),T(f,Et,m),(0,s.a$)(p,m,Et,r.useMaxWidth),p.append("text").text(i.db.getDiagramTitle()).attr("x",Et/2).attr("y",r.titleTopMargin).attr("class","titleText"),(0,s.K2)(T,"makeGantt"),(0,s.K2)(x,"drawRects"),(0,s.K2)(v,"drawExcludeDays"),(0,s.K2)(w,"makeGrid"),(0,s.K2)(_,"vertLabels"),(0,s.K2)(D,"drawToday"),(0,s.K2)($,"checkUnique")}),"draw")},styles:(0,s.K2)((t=>`\n  .mermaid-main-font {\n        font-family: ${t.fontFamily};\n  }\n\n  .exclude-range {\n    fill: ${t.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${t.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${t.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${t.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${t.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: ${t.fontFamily};\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${t.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${t.fontFamily};\n    fill: ${t.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${t.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: ${t.fontFamily};\n  }\n\n  .taskTextOutsideRight {\n    fill: ${t.taskTextDarkColor};\n    text-anchor: start;\n    font-family: ${t.fontFamily};\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${t.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${t.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${t.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${t.taskBkgColor};\n    stroke: ${t.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${t.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${t.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${t.activeTaskBkgColor};\n    stroke: ${t.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${t.doneTaskBorderColor};\n    fill: ${t.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${t.critBorderColor};\n    fill: ${t.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${t.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${t.titleColor||t.textColor};\n    font-family: ${t.fontFamily};\n  }\n`),"getStyles")}},7375:function(t){t.exports=function(){"use strict";return function(t,e){var n=e.prototype,i=n.format;n.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return i.bind(this)(t);var s=this.$utils(),r=(t||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(t){switch(t){case"Q":return Math.ceil((e.$M+1)/3);case"Do":return n.ordinal(e.$D);case"gggg":return e.weekYear();case"GGGG":return e.isoWeekYear();case"wo":return n.ordinal(e.week(),"W");case"w":case"ww":return s.s(e.week(),"w"===t?1:2,"0");case"W":case"WW":return s.s(e.isoWeek(),"W"===t?1:2,"0");case"k":case"kk":return s.s(String(0===e.$H?24:e.$H),"k"===t?1:2,"0");case"X":return Math.floor(e.$d.getTime()/1e3);case"x":return e.$d.getTime();case"z":return"["+e.offsetName()+"]";case"zzz":return"["+e.offsetName("long")+"]";default:return t}}));return i.bind(this)(r)}}}()},8313:function(t){t.exports=function(){"use strict";var t="day";return function(e,n,i){var s=function(e){return e.add(4-e.isoWeekday(),t)},r=n.prototype;r.isoWeekYear=function(){return s(this).year()},r.isoWeek=function(e){if(!this.$utils().u(e))return this.add(7*(e-this.isoWeek()),t);var n,r,a,o=s(this),c=(n=this.isoWeekYear(),a=4-(r=(this.$u?i.utc:i)().year(n).startOf("year")).isoWeekday(),r.isoWeekday()>4&&(a+=7),r.add(a,t));return o.diff(c,"week")+1},r.isoWeekday=function(t){return this.$utils().u(t)?this.day()||7:this.day(this.day()%7?t:t-7)};var a=r.startOf;r.startOf=function(t,e){var n=this.$utils(),i=!!n.u(e)||e;return"isoweek"===n.p(t)?i?this.date(this.date()-(this.isoWeekday()-1)).startOf("day"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf("day"):a.bind(this)(t,e)}}}()}}]);