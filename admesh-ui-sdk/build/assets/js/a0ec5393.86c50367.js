"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[2052],{6713:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>o,default:()=>c,frontMatter:()=>l,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"python-sdk/installation","title":"Python SDK Installation","description":"The AdMesh Python SDK provides programmatic access to the AdMesh REST API for Python 3.8+ applications. The SDK includes comprehensive type definitions for all request parameters and response fields, with support for both synchronous and asynchronous operations.","source":"@site/docs/python-sdk/installation.md","sourceDirName":"python-sdk","slug":"/python-sdk/installation","permalink":"/python-sdk/installation","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/python-sdk/installation.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"AdMesh vs Traditional Advertising","permalink":"/getting-started/admesh-vs-traditional"},"next":{"title":"TypeScript SDK Installation","permalink":"/typescript-sdk/installation"}}');var t=i(4848),r=i(8453);const l={sidebar_position:1},o="Python SDK Installation",a={},d=[{value:"Requirements",id:"requirements",level:2},{value:"Installation",id:"installation",level:2},{value:"Using pip (Recommended)",id:"using-pip-recommended",level:3},{value:"Using pip with virtual environment",id:"using-pip-with-virtual-environment",level:3},{value:"Using Poetry",id:"using-poetry",level:3},{value:"Using Conda",id:"using-conda",level:3},{value:"Verify Installation",id:"verify-installation",level:2},{value:"Dependencies",id:"dependencies",level:2},{value:"Environment Setup",id:"environment-setup",level:2},{value:"1. API Key Configuration",id:"1-api-key-configuration",level:3},{value:"2. Using python-dotenv (Recommended)",id:"2-using-python-dotenv-recommended",level:3},{value:"Development Installation",id:"development-installation",level:2},{value:"From Source",id:"from-source",level:3},{value:"Development Dependencies",id:"development-dependencies",level:3},{value:"Configuration Options",id:"configuration-options",level:2},{value:"Basic Configuration",id:"basic-configuration",level:3},{value:"Advanced Configuration",id:"advanced-configuration",level:3},{value:"Async Client Configuration",id:"async-client-configuration",level:3},{value:"Troubleshooting Installation",id:"troubleshooting-installation",level:2},{value:"Common Issues",id:"common-issues",level:3},{value:"SSL Certificate Errors",id:"ssl-certificate-errors",level:4},{value:"Permission Errors",id:"permission-errors",level:4},{value:"Python Version Issues",id:"python-version-issues",level:4},{value:"Virtual Environment Issues",id:"virtual-environment-issues",level:4},{value:"Proxy Configuration",id:"proxy-configuration",level:3},{value:"IDE Setup",id:"ide-setup",level:2},{value:"VS Code",id:"vs-code",level:3},{value:"PyCharm",id:"pycharm",level:3},{value:"Type Checking",id:"type-checking",level:3},{value:"Next Steps",id:"next-steps",level:2},{value:"Support Resources",id:"support-resources",level:2}];function h(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"python-sdk-installation",children:"Python SDK Installation"})}),"\n",(0,t.jsx)(n.p,{children:"The AdMesh Python SDK provides programmatic access to the AdMesh REST API for Python 3.8+ applications. The SDK includes comprehensive type definitions for all request parameters and response fields, with support for both synchronous and asynchronous operations."}),"\n",(0,t.jsx)(n.h2,{id:"requirements",children:"Requirements"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.strong,{children:"Python 3.8 or higher"})}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"pip"})," (Python package installer)"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"installation",children:"Installation"}),"\n",(0,t.jsx)(n.h3,{id:"using-pip-recommended",children:"Using pip (Recommended)"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"pip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-pip-with-virtual-environment",children:"Using pip with virtual environment"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Create virtual environment\npython -m venv admesh-env\n\n# Activate virtual environment\n# On Windows:\nadmesh-env\\Scripts\\activate\n# On macOS/Linux:\nsource admesh-env/bin/activate\n\n# Install AdMesh\npip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-poetry",children:"Using Poetry"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"poetry add admesh-python\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-conda",children:"Using Conda"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"conda install -c conda-forge admesh-python\n"})}),"\n",(0,t.jsx)(n.h2,{id:"verify-installation",children:"Verify Installation"}),"\n",(0,t.jsx)(n.p,{children:"Create a simple test script to verify the installation:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'# test_installation.py\ntry:\n    from admesh import Admesh\n    print("\u2705 AdMesh Python SDK installed successfully!")\n    print(f"\ud83d\udce6 Version: {Admesh.__version__}")\nexcept ImportError as e:\n    print(f"\u274c Installation failed: {e}")\n'})}),"\n",(0,t.jsx)(n.p,{children:"Run the test:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"python test_installation.py\n"})}),"\n",(0,t.jsx)(n.h2,{id:"dependencies",children:"Dependencies"}),"\n",(0,t.jsx)(n.p,{children:"The AdMesh Python SDK automatically installs these dependencies:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"httpx"})," - Modern HTTP client for making API requests"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"pydantic"})," - Data validation and type hints"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"typing-extensions"})," - Extended type hints support"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"environment-setup",children:"Environment Setup"}),"\n",(0,t.jsx)(n.h3,{id:"1-api-key-configuration",children:"1. API Key Configuration"}),"\n",(0,t.jsx)(n.p,{children:"Set up your API key using environment variables:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:'# Option 1: Export environment variable\nexport ADMESH_API_KEY="your_api_key_here"\n\n# Option 2: Create .env file\necho "ADMESH_API_KEY=your_api_key_here" > .env\n'})}),"\n",(0,t.jsx)(n.h3,{id:"2-using-python-dotenv-recommended",children:"2. Using python-dotenv (Recommended)"}),"\n",(0,t.jsx)(n.p,{children:"Install python-dotenv for easy environment management:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"pip install python-dotenv\n"})}),"\n",(0,t.jsxs)(n.p,{children:["Create a ",(0,t.jsx)(n.code,{children:".env"})," file in your project root:"]}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# .env\nADMESH_API_KEY=your_api_key_here\nADMESH_BASE_URL=https://api.useadmesh.com  # Optional: custom base URL\n"})}),"\n",(0,t.jsx)(n.p,{children:"Load environment variables in your Python code:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:"from dotenv import load_dotenv\nload_dotenv()  # Load variables from .env file\n\nfrom admesh import Admesh\nclient = Admesh()  # API key loaded automatically\n"})}),"\n",(0,t.jsx)(n.h2,{id:"development-installation",children:"Development Installation"}),"\n",(0,t.jsx)(n.p,{children:"If you want to contribute to the SDK or need the latest development version:"}),"\n",(0,t.jsx)(n.h3,{id:"from-source",children:"From Source"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:'# Clone the repository\ngit clone https://github.com/GouniManikumar12/admesh-python.git\ncd admesh-python\n\n# Install in development mode\npip install -e .\n\n# Install development dependencies\npip install -e ".[dev]"\n'})}),"\n",(0,t.jsx)(n.h3,{id:"development-dependencies",children:"Development Dependencies"}),"\n",(0,t.jsx)(n.p,{children:"For development, you'll also get:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"pytest"})," - Testing framework"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"black"})," - Code formatting"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"mypy"})," - Type checking"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"flake8"})," - Linting"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"pre-commit"})," - Git hooks"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"configuration-options",children:"Configuration Options"}),"\n",(0,t.jsx)(n.h3,{id:"basic-configuration",children:"Basic Configuration"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from admesh import Admesh\n\n# Using environment variable (recommended)\nclient = Admesh()\n\n# Or specify API key directly\nclient = Admesh(api_key="your_api_key_here")\n'})}),"\n",(0,t.jsx)(n.h3,{id:"advanced-configuration",children:"Advanced Configuration"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from admesh import Admesh\n\nclient = Admesh(\n    api_key="your_api_key_here",\n    base_url="https://api.useadmesh.com",  # Custom base URL\n    timeout=30.0,  # Request timeout in seconds\n    max_retries=3,  # Number of retry attempts\n    default_headers={  # Custom headers\n        "User-Agent": "MyApp/1.0"\n    }\n)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"async-client-configuration",children:"Async Client Configuration"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from admesh import AsyncAdmesh\n\nasync_client = AsyncAdmesh(\n    api_key="your_api_key_here",\n    timeout=30.0,\n    max_retries=3\n)\n'})}),"\n",(0,t.jsx)(n.h2,{id:"troubleshooting-installation",children:"Troubleshooting Installation"}),"\n",(0,t.jsx)(n.h3,{id:"common-issues",children:"Common Issues"}),"\n",(0,t.jsx)(n.h4,{id:"ssl-certificate-errors",children:"SSL Certificate Errors"}),"\n",(0,t.jsx)(n.p,{children:"If you encounter SSL certificate errors:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org admesh-python\n"})}),"\n",(0,t.jsx)(n.h4,{id:"permission-errors",children:"Permission Errors"}),"\n",(0,t.jsx)(n.p,{children:"On macOS/Linux, if you get permission errors:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Use --user flag\npip install --user admesh-python\n\n# Or use sudo (not recommended)\nsudo pip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h4,{id:"python-version-issues",children:"Python Version Issues"}),"\n",(0,t.jsx)(n.p,{children:"Check your Python version:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"python --version\n# Should be 3.8 or higher\n"})}),"\n",(0,t.jsx)(n.p,{children:"If you have multiple Python versions:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Use specific Python version\npython3.8 -m pip install admesh-python\npython3.9 -m pip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h4,{id:"virtual-environment-issues",children:"Virtual Environment Issues"}),"\n",(0,t.jsx)(n.p,{children:"If you're having issues with virtual environments:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Recreate virtual environment\nrm -rf admesh-env\npython -m venv admesh-env\nsource admesh-env/bin/activate  # or admesh-env\\Scripts\\activate on Windows\npip install --upgrade pip\npip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h3,{id:"proxy-configuration",children:"Proxy Configuration"}),"\n",(0,t.jsx)(n.p,{children:"If you're behind a corporate proxy:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"pip install --proxy http://user:<EMAIL>:port admesh-python\n"})}),"\n",(0,t.jsx)(n.p,{children:"Or set environment variables:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"export HTTP_PROXY=http://proxy.server:port\nexport HTTPS_PROXY=https://proxy.server:port\npip install admesh-python\n"})}),"\n",(0,t.jsx)(n.h2,{id:"ide-setup",children:"IDE Setup"}),"\n",(0,t.jsx)(n.h3,{id:"vs-code",children:"VS Code"}),"\n",(0,t.jsx)(n.p,{children:"For the best development experience with VS Code:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Install the Python extension"}),"\n",(0,t.jsx)(n.li,{children:"Set up your Python interpreter to use your virtual environment"}),"\n",(0,t.jsxs)(n.li,{children:["Install these additional extensions:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Python Docstring Generator"}),"\n",(0,t.jsx)(n.li,{children:"Python Type Hint"}),"\n",(0,t.jsx)(n.li,{children:"autoDocstring"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"pycharm",children:"PyCharm"}),"\n",(0,t.jsx)(n.p,{children:"PyCharm provides excellent support out of the box:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Create a new project with your virtual environment"}),"\n",(0,t.jsx)(n.li,{children:"Enable type checking in Settings \u2192 Editor \u2192 Inspections \u2192 Python"}),"\n",(0,t.jsx)(n.li,{children:"Configure code style to use Black formatting"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"type-checking",children:"Type Checking"}),"\n",(0,t.jsx)(n.p,{children:"The SDK includes full type annotations. Enable type checking:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# Install mypy\npip install mypy\n\n# Run type checking\nmypy your_script.py\n"})}),"\n",(0,t.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,t.jsx)(n.p,{children:"After completing Python SDK installation:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/getting-started/overview",children:"Getting Started"})})," - Core concepts and setup"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Implementation examples"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/typescript-sdk/installation",children:"TypeScript SDK"})})," - Node.js SDK alternative"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"support-resources",children:"Support Resources"}),"\n",(0,t.jsx)(n.p,{children:"For installation assistance:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"GitHub Issues"}),": ",(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-python/issues",children:"Report issues"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Support"}),": ",(0,t.jsx)(n.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Documentation"}),": Complete technical documentation"]}),"\n"]})]})}function c(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(h,{...e})}):h(e)}}}]);