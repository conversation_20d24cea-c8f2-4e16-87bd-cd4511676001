"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[864],{7969:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>d,default:()=>h,frontMatter:()=>a,metadata:()=>s,toc:()=>l});const s=JSON.parse('{"id":"api/authentication","title":"Authentication","description":"Technical documentation for AdMesh API authentication using API keys and error handling procedures.","source":"@site/docs/api/authentication.md","sourceDirName":"api","slug":"/api/authentication","permalink":"/api/authentication","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/docs/api/authentication.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"AI Agent Integration Overview","permalink":"/ai-integration/overview"},"next":{"title":"AI Assistant Integration","permalink":"/examples/ai-assistant"}}');var r=i(4848),t=i(8453);const a={sidebar_position:1},d="Authentication",o={},l=[{value:"Overview",id:"overview",level:2},{value:"API Key Format",id:"api-key-format",level:2},{value:"Authentication Methods",id:"authentication-methods",level:2},{value:"HTTP Header (Recommended)",id:"http-header-recommended",level:3},{value:"Query Parameter (Not Recommended)",id:"query-parameter-not-recommended",level:3},{value:"SDK Authentication",id:"sdk-authentication",level:2},{value:"Python SDK",id:"python-sdk",level:3},{value:"TypeScript SDK",id:"typescript-sdk",level:3},{value:"UI SDK",id:"ui-sdk",level:3},{value:"Environment-Based Keys",id:"environment-based-keys",level:2},{value:"Development",id:"development",level:3},{value:"Testing",id:"testing",level:3},{value:"Production",id:"production",level:3},{value:"API Key Permissions",id:"api-key-permissions",level:2},{value:"Read-Only Keys",id:"read-only-keys",level:3},{value:"Read-Write Keys",id:"read-write-keys",level:3},{value:"Admin Keys",id:"admin-keys",level:3},{value:"Authentication Errors",id:"authentication-errors",level:2},{value:"Invalid API Key",id:"invalid-api-key",level:3},{value:"Missing API Key",id:"missing-api-key",level:3},{value:"Insufficient Permissions",id:"insufficient-permissions",level:3},{value:"Expired API Key",id:"expired-api-key",level:3},{value:"Error Handling",id:"error-handling",level:2},{value:"Python SDK",id:"python-sdk-1",level:3},{value:"TypeScript SDK",id:"typescript-sdk-1",level:3},{value:"Security Best Practices",id:"security-best-practices",level:2},{value:"\u2705 Do&#39;s",id:"-dos",level:3},{value:"\u274c Don&#39;ts",id:"-donts",level:3},{value:"Testing Authentication",id:"testing-authentication",level:2},{value:"Verify API Key",id:"verify-api-key",level:3},{value:"Test with Invalid Key",id:"test-with-invalid-key",level:3},{value:"Next Steps",id:"next-steps",level:2}];function c(e){const n={a:"a",admonition:"admonition",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"authentication",children:"Authentication"})}),"\n",(0,r.jsx)(n.p,{children:"Technical documentation for AdMesh API authentication using API keys and error handling procedures."}),"\n",(0,r.jsx)(n.h2,{id:"overview",children:"Overview"}),"\n",(0,r.jsx)(n.p,{children:"AdMesh implements API key-based authentication for all requests. API keys are account-specific and provide access to the recommendation engine and tracking capabilities."}),"\n",(0,r.jsx)(n.h2,{id:"api-key-format",children:"API Key Format"}),"\n",(0,r.jsx)(n.p,{children:"AdMesh API keys follow this format:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"admesh_[environment]_[random_string]\n"})}),"\n",(0,r.jsx)(n.p,{children:"Examples:"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"admesh_prod_abc123xyz789"})," - Production key"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"admesh_test_def456uvw012"})," - Test key"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"admesh_dev_ghi789rst345"})," - Development key"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"authentication-methods",children:"Authentication Methods"}),"\n",(0,r.jsx)(n.h3,{id:"http-header-recommended",children:"HTTP Header (Recommended)"}),"\n",(0,r.jsxs)(n.p,{children:["Include your API key in the ",(0,r.jsx)(n.code,{children:"Authorization"})," header:"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /api/v1/recommendations\nHost: api.useadmesh.com\nAuthorization: Bearer admesh_prod_abc123xyz789\nContent-Type: application/json\n"})}),"\n",(0,r.jsx)(n.h3,{id:"query-parameter-not-recommended",children:"Query Parameter (Not Recommended)"}),"\n",(0,r.jsx)(n.p,{children:"For testing only, you can include the API key as a query parameter:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /api/v1/recommendations?api_key=admesh_prod_abc123xyz789\nHost: api.useadmesh.com\n"})}),"\n",(0,r.jsx)(n.admonition,{title:"Security Warning",type:"warning",children:(0,r.jsx)(n.p,{children:"Never use query parameters for API keys in production. They can be logged in server logs and browser history."})}),"\n",(0,r.jsx)(n.h2,{id:"sdk-authentication",children:"SDK Authentication"}),"\n",(0,r.jsx)(n.h3,{id:"python-sdk",children:"Python SDK"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'from admesh import Admesh\n\n# Method 1: Environment variable (recommended)\nimport os\nclient = Admesh(api_key=os.environ.get("ADMESH_API_KEY"))\n\n# Method 2: Direct parameter\nclient = Admesh(api_key="admesh_prod_abc123xyz789")\n\n# Method 3: Using dotenv\nfrom dotenv import load_dotenv\nload_dotenv()\nclient = Admesh()  # Automatically uses ADMESH_API_KEY from .env\n'})}),"\n",(0,r.jsx)(n.h3,{id:"typescript-sdk",children:"TypeScript SDK"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\n// Method 1: Environment variable (recommended)\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY\n});\n\n// Method 2: Direct parameter\nconst client = new Admesh({\n  apiKey: 'admesh_prod_abc123xyz789'\n});\n"})}),"\n",(0,r.jsx)(n.h3,{id:"ui-sdk",children:"UI SDK"}),"\n",(0,r.jsx)(n.p,{children:"The UI SDK doesn't require direct authentication - it works with recommendations fetched from your backend:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-tsx",children:"// Backend fetches recommendations with authentication\nconst recommendations = await fetch('/api/recommendations', {\n  headers: {\n    'Authorization': `Bearer ${process.env.ADMESH_API_KEY}`\n  }\n});\n\n// Frontend displays recommendations without API key\n<AdMeshLayout recommendations={recommendations} />\n"})}),"\n",(0,r.jsx)(n.h2,{id:"environment-based-keys",children:"Environment-Based Keys"}),"\n",(0,r.jsx)(n.p,{children:"Use different API keys for different environments:"}),"\n",(0,r.jsx)(n.h3,{id:"development",children:"Development"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"ADMESH_API_KEY=admesh_dev_abc123xyz789\nADMESH_BASE_URL=http://localhost:8000  # Local development\n"})}),"\n",(0,r.jsx)(n.h3,{id:"testing",children:"Testing"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"ADMESH_API_KEY=admesh_test_def456uvw012\nADMESH_BASE_URL=https://api-test.useadmesh.com\n"})}),"\n",(0,r.jsx)(n.h3,{id:"production",children:"Production"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"ADMESH_API_KEY=admesh_prod_ghi789rst345\nADMESH_BASE_URL=https://api.useadmesh.com\n"})}),"\n",(0,r.jsx)(n.h2,{id:"api-key-permissions",children:"API Key Permissions"}),"\n",(0,r.jsx)(n.p,{children:"Different API keys can have different permission levels:"}),"\n",(0,r.jsx)(n.h3,{id:"read-only-keys",children:"Read-Only Keys"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Get recommendations"}),"\n",(0,r.jsx)(n.li,{children:"View analytics (read-only)"}),"\n",(0,r.jsx)(n.li,{children:"Access public product data"}),"\n"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'# Read-only operations\nrecommendations = client.recommend.get_recommendations(query="CRM software")\nanalytics = client.analytics.get_stats()  # Read-only\n'})}),"\n",(0,r.jsx)(n.h3,{id:"read-write-keys",children:"Read-Write Keys"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"All read-only permissions"}),"\n",(0,r.jsx)(n.li,{children:"Submit tracking data"}),"\n",(0,r.jsx)(n.li,{children:"Update user preferences"}),"\n"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'# Read-write operations\nrecommendations = client.recommend.get_recommendations(query="CRM software")\nclient.tracking.record_click(ad_id="abc123", user_id="user456")\n'})}),"\n",(0,r.jsx)(n.h3,{id:"admin-keys",children:"Admin Keys"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"All read-write permissions"}),"\n",(0,r.jsx)(n.li,{children:"Manage offers and products"}),"\n",(0,r.jsx)(n.li,{children:"Access sensitive analytics"}),"\n"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:"# Admin operations (requires admin key)\nclient.offers.create_offer(product_data)\nclient.analytics.get_revenue_data()\n"})}),"\n",(0,r.jsx)(n.h2,{id:"authentication-errors",children:"Authentication Errors"}),"\n",(0,r.jsx)(n.h3,{id:"invalid-api-key",children:"Invalid API Key"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"HTTP Status:"})," ",(0,r.jsx)(n.code,{children:"401 Unauthorized"})]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "error": {\n    "code": "INVALID_API_KEY",\n    "message": "The provided API key is invalid or has been revoked",\n    "type": "authentication_error"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Common causes:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Typo in API key"}),"\n",(0,r.jsx)(n.li,{children:"API key has been revoked"}),"\n",(0,r.jsx)(n.li,{children:"Using wrong environment key"}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"missing-api-key",children:"Missing API Key"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"HTTP Status:"})," ",(0,r.jsx)(n.code,{children:"401 Unauthorized"})]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "error": {\n    "code": "MISSING_API_KEY",\n    "message": "API key is required for this endpoint",\n    "type": "authentication_error"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"insufficient-permissions",children:"Insufficient Permissions"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"HTTP Status:"})," ",(0,r.jsx)(n.code,{children:"403 Forbidden"})]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "error": {\n    "code": "INSUFFICIENT_PERMISSIONS",\n    "message": "Your API key does not have permission to access this resource",\n    "type": "authorization_error"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"expired-api-key",children:"Expired API Key"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"HTTP Status:"})," ",(0,r.jsx)(n.code,{children:"401 Unauthorized"})]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "error": {\n    "code": "EXPIRED_API_KEY",\n    "message": "Your API key has expired. Please generate a new one",\n    "type": "authentication_error"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"error-handling",children:"Error Handling"}),"\n",(0,r.jsx)(n.h3,{id:"python-sdk-1",children:"Python SDK"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'import admesh\n\ntry:\n    client = Admesh(api_key="invalid_key")\n    response = client.recommend.get_recommendations(query="CRM software")\nexcept admesh.AuthenticationError as e:\n    print(f"Authentication failed: {e.message}")\n    # Handle invalid API key\nexcept admesh.PermissionDeniedError as e:\n    print(f"Permission denied: {e.message}")\n    # Handle insufficient permissions\nexcept admesh.APIError as e:\n    print(f"API error: {e.status_code} - {e.message}")\n'})}),"\n",(0,r.jsx)(n.h3,{id:"typescript-sdk-1",children:"TypeScript SDK"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\ntry {\n  const client = new Admesh({ apiKey: 'invalid_key' });\n  const response = await client.recommend.getRecommendations({\n    query: 'CRM software'\n  });\n} catch (error) {\n  if (error instanceof Admesh.AuthenticationError) {\n    console.log('Authentication failed:', error.message);\n    // Handle invalid API key\n  } else if (error instanceof Admesh.PermissionDeniedError) {\n    console.log('Permission denied:', error.message);\n    // Handle insufficient permissions\n  } else {\n    console.log('API error:', error);\n  }\n}\n"})}),"\n",(0,r.jsx)(n.h2,{id:"security-best-practices",children:"Security Best Practices"}),"\n",(0,r.jsx)(n.h3,{id:"-dos",children:"\u2705 Do's"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Store API keys securely"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Use environment variables\nexport ADMESH_API_KEY="your_api_key"\n\n# Or use a .env file\necho "ADMESH_API_KEY=your_api_key" > .env\n'})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Use different keys for different environments"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:"# Different keys for dev/test/prod\nif os.environ.get('NODE_ENV') == 'production':\n    api_key = os.environ.get('ADMESH_PROD_API_KEY')\nelse:\n    api_key = os.environ.get('ADMESH_DEV_API_KEY')\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Rotate keys regularly"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:"# Set up key rotation schedule\n# Generate new keys monthly\n# Update applications with new keys\n# Revoke old keys after transition\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Monitor API key usage"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:"# Check dashboard for unusual activity\n# Set up alerts for high usage\n# Monitor error rates\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"-donts",children:"\u274c Don'ts"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Never commit API keys to version control"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Add to .gitignore\necho ".env" >> .gitignore\necho "*.key" >> .gitignore\n'})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Don't use production keys in development"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'# \u274c Wrong\nclient = Admesh(api_key="admesh_prod_abc123xyz789")  # In dev environment\n\n# \u2705 Correct\nclient = Admesh(api_key=os.environ.get("ADMESH_API_KEY"))\n'})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Don't expose keys in client-side code"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-javascript",children:"// \u274c Wrong - API key exposed in browser\nconst client = new Admesh({ apiKey: 'admesh_prod_abc123xyz789' });\n\n// \u2705 Correct - API key stays on server\nconst response = await fetch('/api/recommendations');\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Don't share API keys"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"# \u274c Wrong\n# Sharing keys in chat, email, or documentation\n\n# \u2705 Correct\n# Each developer/environment gets their own key\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"testing-authentication",children:"Testing Authentication"}),"\n",(0,r.jsx)(n.h3,{id:"verify-api-key",children:"Verify API Key"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'curl -H "Authorization: Bearer your_api_key" \\\n     https://api.useadmesh.com/api/v1/auth/verify\n'})}),"\n",(0,r.jsx)(n.p,{children:"Expected response:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "valid": true,\n  "key_id": "key_abc123",\n  "permissions": ["read", "write"],\n  "rate_limit": {\n    "requests_per_minute": 100,\n    "requests_remaining": 99\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"test-with-invalid-key",children:"Test with Invalid Key"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'curl -H "Authorization: Bearer invalid_key" \\\n     https://api.useadmesh.com/api/v1/recommendations\n'})}),"\n",(0,r.jsx)(n.p,{children:"Expected response:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "error": {\n    "code": "INVALID_API_KEY",\n    "message": "The provided API key is invalid or has been revoked"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:(0,r.jsx)(n.a,{href:"/getting-started/api-keys",children:"API Keys Setup"})})," - Obtain API credentials from dashboard"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:(0,r.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})})," - Backend SDK implementation"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:(0,r.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK"})})," - Frontend component integration"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:(0,r.jsx)(n.a,{href:"/getting-started/quick-start",children:"Quick Start"})})," - Execute first API call"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}}}]);