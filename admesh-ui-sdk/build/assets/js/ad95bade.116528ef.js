"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[9200],{642:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>s,toc:()=>a});const s=JSON.parse('{"id":"getting-started/api-keys","title":"Getting Your API Key","description":"To use AdMesh APIs and SDKs, you\'ll need an API key. This guide walks you through the process of creating an account and obtaining your API key.","source":"@site/docs/getting-started/api-keys.md","sourceDirName":"getting-started","slug":"/getting-started/api-keys","permalink":"/getting-started/api-keys","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/getting-started/api-keys.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"Overview","permalink":"/getting-started/overview"},"next":{"title":"Quick Start Guide","permalink":"/getting-started/quick-start"}}');var t=i(4848),r=i(8453);const l={sidebar_position:2},d="Getting Your API Key",o={},a=[{value:"Creating an Account",id:"creating-an-account",level:2},{value:"Step 1: Sign Up",id:"step-1-sign-up",level:3},{value:"Step 2: Email Verification",id:"step-2-email-verification",level:3},{value:"Obtaining Your API Key",id:"obtaining-your-api-key",level:2},{value:"Step 1: Access the Dashboard",id:"step-1-access-the-dashboard",level:3},{value:"Step 2: Generate API Key",id:"step-2-generate-api-key",level:3},{value:"Step 3: Copy and Secure Your Key",id:"step-3-copy-and-secure-your-key",level:3},{value:"API Key Types",id:"api-key-types",level:2},{value:"Production Keys",id:"production-keys",level:3},{value:"Test Keys",id:"test-keys",level:3},{value:"Development Keys",id:"development-keys",level:3},{value:"Using Your API Key",id:"using-your-api-key",level:2},{value:"Environment Variables (Recommended)",id:"environment-variables-recommended",level:3},{value:"Python SDK",id:"python-sdk",level:3},{value:"TypeScript SDK",id:"typescript-sdk",level:3},{value:"Using dotenv",id:"using-dotenv",level:3},{value:"Python",id:"python",level:4},{value:"Node.js",id:"nodejs",level:4},{value:"Security Best Practices",id:"security-best-practices",level:2},{value:"\u2705 Do&#39;s",id:"-dos",level:3},{value:"\u274c Don&#39;ts",id:"-donts",level:3},{value:"Rate Limits",id:"rate-limits",level:2},{value:"Managing API Keys",id:"managing-api-keys",level:2},{value:"Viewing Usage",id:"viewing-usage",level:3},{value:"Rotating Keys",id:"rotating-keys",level:3},{value:"Revoking Keys",id:"revoking-keys",level:3},{value:"Troubleshooting",id:"troubleshooting",level:2},{value:"Common Issues",id:"common-issues",level:3},{value:"Invalid API Key",id:"invalid-api-key",level:4},{value:"Rate Limit Exceeded",id:"rate-limit-exceeded",level:4},{value:"Insufficient Permissions",id:"insufficient-permissions",level:4},{value:"Getting Help",id:"getting-help",level:3}];function c(e){const n={a:"a",admonition:"admonition",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",hr:"hr",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"getting-your-api-key",children:"Getting Your API Key"})}),"\n",(0,t.jsx)(n.p,{children:"To use AdMesh APIs and SDKs, you'll need an API key. This guide walks you through the process of creating an account and obtaining your API key."}),"\n",(0,t.jsx)(n.h2,{id:"creating-an-account",children:"Creating an Account"}),"\n",(0,t.jsx)(n.h3,{id:"step-1-sign-up",children:"Step 1: Sign Up"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["Visit ",(0,t.jsx)(n.a,{href:"https://useadmesh.com/agent",children:"useadmesh.com/agent"})]}),"\n",(0,t.jsxs)(n.li,{children:["Click ",(0,t.jsx)(n.strong,{children:'"Sign Up"'})," to create a new account"]}),"\n",(0,t.jsxs)(n.li,{children:["Fill in your details:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Email address"}),"\n",(0,t.jsx)(n.li,{children:"Password"}),"\n",(0,t.jsx)(n.li,{children:"Company/Project name (optional)"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"step-2-email-verification",children:"Step 2: Email Verification"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Check your email for a verification message from AdMesh"}),"\n",(0,t.jsx)(n.li,{children:"Click the verification link to activate your account"}),"\n",(0,t.jsx)(n.li,{children:"You'll be redirected to the AdMesh dashboard"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"obtaining-your-api-key",children:"Obtaining Your API Key"}),"\n",(0,t.jsx)(n.h3,{id:"step-1-access-the-dashboard",children:"Step 1: Access the Dashboard"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["Log in to your AdMesh account at ",(0,t.jsx)(n.a,{href:"https://useadmesh.com/agent",children:"useadmesh.com/agent"})]}),"\n",(0,t.jsxs)(n.li,{children:["Navigate to the ",(0,t.jsx)(n.strong,{children:"API Keys"})," section in the dashboard"]}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"step-2-generate-api-key",children:"Step 2: Generate API Key"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["Click ",(0,t.jsx)(n.strong,{children:'"Generate New API Key"'})]}),"\n",(0,t.jsx)(n.li,{children:'Give your API key a descriptive name (e.g., "Production App", "Development")'}),"\n",(0,t.jsxs)(n.li,{children:["Select the appropriate permissions:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Read"})," - Get recommendations"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Write"})," - Submit tracking data"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Admin"})," - Manage offers and settings"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"step-3-copy-and-secure-your-key",children:"Step 3: Copy and Secure Your Key"}),"\n",(0,t.jsx)(n.admonition,{title:"Important",type:"warning",children:(0,t.jsx)(n.p,{children:"Your API key will only be shown once. Make sure to copy and store it securely."})}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Copy the generated API key"}),"\n",(0,t.jsx)(n.li,{children:"Store it in a secure location (password manager, environment variables)"}),"\n",(0,t.jsx)(n.li,{children:"Never commit API keys to version control"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"api-key-types",children:"API Key Types"}),"\n",(0,t.jsx)(n.p,{children:"AdMesh provides different types of API keys for different environments:"}),"\n",(0,t.jsx)(n.h3,{id:"production-keys",children:"Production Keys"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Used for live applications"}),"\n",(0,t.jsx)(n.li,{children:"Full rate limits apply"}),"\n",(0,t.jsx)(n.li,{children:"Real analytics and tracking"}),"\n",(0,t.jsx)(n.li,{children:"Revenue attribution enabled"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"test-keys",children:"Test Keys"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Used for development and testing"}),"\n",(0,t.jsx)(n.li,{children:"Reduced rate limits"}),"\n",(0,t.jsx)(n.li,{children:"Sandbox environment"}),"\n",(0,t.jsx)(n.li,{children:"No revenue attribution"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"development-keys",children:"Development Keys"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Local development only"}),"\n",(0,t.jsx)(n.li,{children:"Unlimited requests for testing"}),"\n",(0,t.jsx)(n.li,{children:"Mock data responses available"}),"\n",(0,t.jsx)(n.li,{children:"No analytics tracking"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"using-your-api-key",children:"Using Your API Key"}),"\n",(0,t.jsx)(n.h3,{id:"environment-variables-recommended",children:"Environment Variables (Recommended)"}),"\n",(0,t.jsx)(n.p,{children:"Store your API key as an environment variable:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"# .env file\nADMESH_API_KEY=your_api_key_here\n"})}),"\n",(0,t.jsx)(n.h3,{id:"python-sdk",children:"Python SDK"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'import os\nfrom admesh import Admesh\n\n# Using environment variable (recommended)\nclient = Admesh(api_key=os.environ.get("ADMESH_API_KEY"))\n\n# Or pass directly (not recommended for production)\nclient = Admesh(api_key="your_api_key_here")\n'})}),"\n",(0,t.jsx)(n.h3,{id:"typescript-sdk",children:"TypeScript SDK"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\n// Using environment variable (recommended)\nconst client = new Admesh({\n  apiKey: process.env.ADMESH_API_KEY\n});\n\n// Or pass directly (not recommended for production)\nconst client = new Admesh({\n  apiKey: 'your_api_key_here'\n});\n"})}),"\n",(0,t.jsx)(n.h3,{id:"using-dotenv",children:"Using dotenv"}),"\n",(0,t.jsx)(n.p,{children:"For local development, use dotenv to load environment variables:"}),"\n",(0,t.jsx)(n.h4,{id:"python",children:"Python"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:"# Install: pip install python-dotenv\nfrom dotenv import load_dotenv\nload_dotenv()\n\nfrom admesh import Admesh\nclient = Admesh()  # Automatically uses ADMESH_API_KEY from .env\n"})}),"\n",(0,t.jsx)(n.h4,{id:"nodejs",children:"Node.js"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-typescript",children:"// Install: npm install dotenv\nrequire('dotenv').config();\n\nimport Admesh from 'admesh';\nconst client = new Admesh(); // Automatically uses ADMESH_API_KEY from .env\n"})}),"\n",(0,t.jsx)(n.h2,{id:"security-best-practices",children:"Security Best Practices"}),"\n",(0,t.jsx)(n.h3,{id:"-dos",children:"\u2705 Do's"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Store API keys in environment variables"}),"\n",(0,t.jsx)(n.li,{children:"Use different keys for different environments"}),"\n",(0,t.jsx)(n.li,{children:"Rotate keys regularly"}),"\n",(0,t.jsx)(n.li,{children:"Monitor API key usage in the dashboard"}),"\n",(0,t.jsx)(n.li,{children:"Use the principle of least privilege (minimal required permissions)"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"-donts",children:"\u274c Don'ts"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Never commit API keys to version control"}),"\n",(0,t.jsx)(n.li,{children:"Don't share API keys in chat or email"}),"\n",(0,t.jsx)(n.li,{children:"Don't use production keys in development"}),"\n",(0,t.jsx)(n.li,{children:"Don't hardcode keys in your application code"}),"\n",(0,t.jsx)(n.li,{children:"Don't expose keys in client-side code"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"rate-limits",children:"Rate Limits"}),"\n",(0,t.jsx)(n.p,{children:"Different subscription tiers have different rate limits:"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Plan"}),(0,t.jsx)(n.th,{children:"Daily Requests"}),(0,t.jsx)(n.th,{children:"Monthly Requests"}),(0,t.jsx)(n.th,{children:"Rate Limit"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:(0,t.jsx)(n.strong,{children:"Free"})}),(0,t.jsx)(n.td,{children:"10,000"}),(0,t.jsx)(n.td,{children:"100,000"}),(0,t.jsx)(n.td,{children:"100/minute"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:(0,t.jsx)(n.strong,{children:"Pro"})}),(0,t.jsx)(n.td,{children:"50,000"}),(0,t.jsx)(n.td,{children:"1,000,000"}),(0,t.jsx)(n.td,{children:"500/minute"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:(0,t.jsx)(n.strong,{children:"Enterprise"})}),(0,t.jsx)(n.td,{children:"Unlimited"}),(0,t.jsx)(n.td,{children:"Unlimited"}),(0,t.jsx)(n.td,{children:"1000/minute"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"managing-api-keys",children:"Managing API Keys"}),"\n",(0,t.jsx)(n.h3,{id:"viewing-usage",children:"Viewing Usage"}),"\n",(0,t.jsx)(n.p,{children:"Monitor your API key usage in the dashboard:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Request count and rate limit status"}),"\n",(0,t.jsx)(n.li,{children:"Error rates and response times"}),"\n",(0,t.jsx)(n.li,{children:"Geographic distribution of requests"}),"\n",(0,t.jsx)(n.li,{children:"Most popular endpoints"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"rotating-keys",children:"Rotating Keys"}),"\n",(0,t.jsx)(n.p,{children:"To rotate an API key:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Generate a new API key in the dashboard"}),"\n",(0,t.jsx)(n.li,{children:"Update your application with the new key"}),"\n",(0,t.jsx)(n.li,{children:"Test that everything works correctly"}),"\n",(0,t.jsx)(n.li,{children:"Delete the old API key"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"revoking-keys",children:"Revoking Keys"}),"\n",(0,t.jsx)(n.p,{children:"If an API key is compromised:"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Immediately revoke the key in the dashboard"}),"\n",(0,t.jsx)(n.li,{children:"Generate a new API key"}),"\n",(0,t.jsx)(n.li,{children:"Update all applications using the old key"}),"\n",(0,t.jsx)(n.li,{children:"Monitor for any unauthorized usage"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"troubleshooting",children:"Troubleshooting"}),"\n",(0,t.jsx)(n.h3,{id:"common-issues",children:"Common Issues"}),"\n",(0,t.jsx)(n.h4,{id:"invalid-api-key",children:"Invalid API Key"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "error": "Invalid API key",\n  "code": "INVALID_API_KEY",\n  "status": 401\n}\n'})}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"Solution"}),": Verify your API key is correct and hasn't been revoked."]}),"\n",(0,t.jsx)(n.h4,{id:"rate-limit-exceeded",children:"Rate Limit Exceeded"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "error": "Rate limit exceeded",\n  "code": "RATE_LIMIT_EXCEEDED", \n  "status": 429\n}\n'})}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"Solution"}),": Implement exponential backoff or upgrade your plan."]}),"\n",(0,t.jsx)(n.h4,{id:"insufficient-permissions",children:"Insufficient Permissions"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "error": "Insufficient permissions",\n  "code": "INSUFFICIENT_PERMISSIONS",\n  "status": 403\n}\n'})}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"Solution"}),": Check that your API key has the required permissions."]}),"\n",(0,t.jsx)(n.h3,{id:"getting-help",children:"Getting Help"}),"\n",(0,t.jsx)(n.p,{children:"If you're having trouble with API keys:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:["Contact support at ",(0,t.jsx)(n.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n",(0,t.jsxs)(n.li,{children:["Check our ",(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-python/issues",children:"GitHub Issues"})]}),"\n",(0,t.jsx)(n.li,{children:"Join our community discussions"}),"\n"]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsxs)(n.p,{children:["Now that you have your API key, let's make your first API call in the ",(0,t.jsx)(n.a,{href:"/getting-started/quick-start",children:"Quick Start Guide"}),"!"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(c,{...e})}):c(e)}}}]);