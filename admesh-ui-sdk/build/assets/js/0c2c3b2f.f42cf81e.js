"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[7194],{3228:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>l,contentTitle:()=>a,default:()=>h,frontMatter:()=>o,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"getting-started/ad-formats","title":"AdMesh Ad Formats","description":"Learn about AdMesh\'s unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models.","source":"@site/docs/getting-started/ad-formats.md","sourceDirName":"getting-started","slug":"/getting-started/ad-formats","permalink":"/getting-started/ad-formats","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/getting-started/ad-formats.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{"sidebar_position":4},"sidebar":"tutorialSidebar","previous":{"title":"Quick Start Guide","permalink":"/getting-started/quick-start"},"next":{"title":"AdMesh vs Traditional Advertising","permalink":"/getting-started/admesh-vs-traditional"}}');var i=t(4848),r=t(8453);const o={sidebar_position:4},a="AdMesh Ad Formats",l={},d=[{value:"\ud83d\udd04 Push vs Pull vs AdMesh&#39;s Contextual Model",id:"-push-vs-pull-vs-admeshs-contextual-model",level:2},{value:"Traditional Push Advertising",id:"traditional-push-advertising",level:3},{value:"Traditional Pull Advertising",id:"traditional-pull-advertising",level:3},{value:"AdMesh&#39;s Contextual Intelligence Model",id:"admeshs-contextual-intelligence-model",level:3},{value:"\ud83c\udfa8 AdMesh Ad Formats",id:"-admesh-ad-formats",level:2},{value:"1. Citation-Based Recommendations",id:"1-citation-based-recommendations",level:3},{value:"2. Conversational Recommendations",id:"2-conversational-recommendations",level:3},{value:"3. Auto-Triggered Suggestions",id:"3-auto-triggered-suggestions",level:3},{value:"4. Sidebar Recommendations",id:"4-sidebar-recommendations",level:3},{value:"\ud83e\udde0 How AdMesh is Different",id:"-how-admesh-is-different",level:2},{value:"Traditional Advertising Platforms",id:"traditional-advertising-platforms",level:3},{value:"AdMesh&#39;s Unique Advantages",id:"admeshs-unique-advantages",level:3},{value:"\ud83c\udfaf <strong>Intent Detection Without Explicit Search</strong>",id:"-intent-detection-without-explicit-search",level:4},{value:"\ud83d\udcda <strong>Academic-Style Citations</strong>",id:"-academic-style-citations",level:4},{value:"\ud83e\udd16 <strong>AI-Native Integration</strong>",id:"-ai-native-integration",level:4},{value:"\ud83d\udd04 <strong>Contextual Timing</strong>",id:"-contextual-timing",level:4},{value:"\ud83d\udcca Interactive Storybook Examples",id:"-interactive-storybook-examples",level:2},{value:"\ud83c\udfad <strong>Live Component Showcase</strong>",id:"-live-component-showcase",level:3},{value:"<strong>Access the Storybook</strong>",id:"access-the-storybook",level:4},{value:"<strong>What You&#39;ll Find in Storybook</strong>",id:"what-youll-find-in-storybook",level:4},{value:"\ud83d\udcca Storybook Integration Examples",id:"-storybook-integration-examples",level:2},{value:"Example 1: Business Advice Story",id:"example-1-business-advice-story",level:3},{value:"Example 2: Technical Tutorial Story",id:"example-2-technical-tutorial-story",level:3},{value:"Example 3: E-commerce Journey Story",id:"example-3-e-commerce-journey-story",level:3},{value:"\ud83c\udfad Storybook Component Implementation",id:"-storybook-component-implementation",level:2},{value:"\ud83d\ude80 Implementation Guide",id:"-implementation-guide",level:2},{value:"1. Detect Story Context",id:"1-detect-story-context",level:3},{value:"2. Extract Recommendation Opportunities",id:"2-extract-recommendation-opportunities",level:3},{value:"3. Generate Contextual Recommendations",id:"3-generate-contextual-recommendations",level:3},{value:"\ud83d\udcc8 Benefits of Story-Based Ads",id:"-benefits-of-story-based-ads",level:2},{value:"For Users",id:"for-users",level:3},{value:"For Advertisers",id:"for-advertisers",level:3},{value:"For Publishers",id:"for-publishers",level:3},{value:"\ud83c\udfaf Best Practices",id:"-best-practices",level:2},{value:"Story Integration",id:"story-integration",level:3},{value:"User Experience",id:"user-experience",level:3},{value:"Content Quality",id:"content-quality",level:3}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",hr:"hr",li:"li",mermaid:"mermaid",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,r.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"admesh-ad-formats",children:"AdMesh Ad Formats"})}),"\n",(0,i.jsx)(n.p,{children:"Learn about AdMesh's unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models."}),"\n",(0,i.jsx)(n.h2,{id:"-push-vs-pull-vs-admeshs-contextual-model",children:"\ud83d\udd04 Push vs Pull vs AdMesh's Contextual Model"}),"\n",(0,i.jsx)(n.h3,{id:"traditional-push-advertising",children:"Traditional Push Advertising"}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Definition"}),": Ads are forced upon users regardless of their current context or intent."]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Examples"}),":"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Banner ads on websites"}),"\n",(0,i.jsx)(n.li,{children:"Pop-up advertisements"}),"\n",(0,i.jsx)(n.li,{children:"TV commercials"}),"\n",(0,i.jsx)(n.li,{children:"Social media sponsored posts"}),"\n"]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Problems"}),":"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Interrupts user experience"}),"\n",(0,i.jsx)(n.li,{children:"Often irrelevant to current context"}),"\n",(0,i.jsx)(n.li,{children:"Creates ad fatigue"}),"\n",(0,i.jsx)(n.li,{children:"Low engagement rates"}),"\n",(0,i.jsx)(n.li,{children:'Users develop "banner blindness"'}),"\n"]}),"\n",(0,i.jsx)(n.mermaid,{value:"graph LR\n    A[Advertiser] --\x3e B[Push Ad]\n    B --\x3e C[User]\n    C --\x3e D[Ignores/Blocks]\n    D --\x3e E[Low ROI]"}),"\n",(0,i.jsx)(n.h3,{id:"traditional-pull-advertising",children:"Traditional Pull Advertising"}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Definition"}),": Users actively seek out advertising content when they're ready to make a purchase."]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Examples"}),":"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Google search ads"}),"\n",(0,i.jsx)(n.li,{children:"Product comparison sites"}),"\n",(0,i.jsx)(n.li,{children:"Shopping platforms"}),"\n",(0,i.jsx)(n.li,{children:"Review websites"}),"\n"]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Problems"}),":"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Only captures users at bottom of funnel"}),"\n",(0,i.jsx)(n.li,{children:"High competition and costs"}),"\n",(0,i.jsx)(n.li,{children:"Misses discovery opportunities"}),"\n",(0,i.jsx)(n.li,{children:"Limited to explicit search intent"}),"\n"]}),"\n",(0,i.jsx)(n.mermaid,{value:"graph LR\n    A[User Intent] --\x3e B[Search/Browse]\n    B --\x3e C[Pull Ad]\n    C --\x3e D[Conversion]\n    D --\x3e E[Higher ROI]"}),"\n",(0,i.jsx)(n.h3,{id:"admeshs-contextual-intelligence-model",children:"AdMesh's Contextual Intelligence Model"}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Definition"}),": AI-powered recommendations that appear naturally within conversations and content when contextually relevant."]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Key Principles"}),":"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Context-Aware"}),": Understands conversation flow and user intent"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Non-Intrusive"}),": Appears as helpful suggestions, not ads"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Intelligent Timing"}),": Shows recommendations at optimal moments"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Citation-Based"}),": References products like academic sources"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Value-First"}),": Provides genuine value before monetization"]}),"\n"]}),"\n",(0,i.jsx)(n.mermaid,{value:"graph TD\n    A[User Conversation] --\x3e B[AI Intent Detection]\n    B --\x3e C[Context Analysis]\n    C --\x3e D[Relevant Recommendations]\n    D --\x3e E[Citation-Based Display]\n    E --\x3e F[Natural Integration]\n    F --\x3e G[High Engagement]"}),"\n",(0,i.jsx)(n.h2,{id:"-admesh-ad-formats",children:"\ud83c\udfa8 AdMesh Ad Formats"}),"\n",(0,i.jsx)(n.h3,{id:"1-citation-based-recommendations",children:"1. Citation-Based Recommendations"}),"\n",(0,i.jsx)(n.p,{children:"Display recommendations as numbered references within conversational text, similar to academic papers."}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:'import { AdMeshCitationUnit } from \'admesh-ui-sdk\';\n\n<AdMeshCitationUnit\n  recommendations={recommendations}\n  conversationText="For your startup\'s CRM needs, I recommend HubSpot for its excellent free tier and Salesforce for enterprise features..."\n  citationStyle="numbered"\n  showCitationList={true}\n/>\n'})}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Output"}),":"]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"For your startup's CRM needs, I recommend HubSpot\xb9 for its excellent \nfree tier and Salesforce\xb2 for enterprise features...\n\nReferences:\n\xb9 HubSpot CRM - Free tier with excellent startup features\n\xb2 Salesforce - Enterprise-grade CRM with advanced automation\n"})}),"\n",(0,i.jsx)(n.h3,{id:"2-conversational-recommendations",children:"2. Conversational Recommendations"}),"\n",(0,i.jsx)(n.p,{children:"Recommendations that appear naturally within chat interfaces and AI conversations."}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:"import { AdMeshConversationalUnit } from 'admesh-ui-sdk';\n\n<AdMeshConversationalUnit\n  recommendations={recommendations}\n  config={{\n    displayMode: 'inline',\n    context: 'chat',\n    maxRecommendations: 3,\n    showPoweredBy: true\n  }}\n/>\n"})}),"\n",(0,i.jsx)(n.h3,{id:"3-auto-triggered-suggestions",children:"3. Auto-Triggered Suggestions"}),"\n",(0,i.jsx)(n.p,{children:"Proactive recommendations that appear based on conversation context without explicit user requests."}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:'import { AdMeshAutoRecommendationWidget } from \'admesh-ui-sdk\';\n\n<AdMeshAutoRecommendationWidget\n  recommendations={recommendations}\n  trigger="User mentioned project management challenges"\n  autoShow={true}\n  position="bottom-right"\n/>\n'})}),"\n",(0,i.jsx)(n.h3,{id:"4-sidebar-recommendations",children:"4. Sidebar Recommendations"}),"\n",(0,i.jsx)(n.p,{children:"Persistent recommendation panels that complement main content without interrupting it."}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:"import { AdMeshSidebar } from 'admesh-ui-sdk';\n\n<AdMeshSidebar\n  recommendations={recommendations}\n  config={{\n    position: 'right',\n    displayMode: 'recommendations',\n    collapsible: true\n  }}\n/>\n"})}),"\n",(0,i.jsx)(n.h2,{id:"-how-admesh-is-different",children:"\ud83e\udde0 How AdMesh is Different"}),"\n",(0,i.jsx)(n.h3,{id:"traditional-advertising-platforms",children:"Traditional Advertising Platforms"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Aspect"}),(0,i.jsx)(n.th,{children:"Traditional Push"}),(0,i.jsx)(n.th,{children:"Traditional Pull"}),(0,i.jsx)(n.th,{children:"AdMesh Contextual"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"Timing"})}),(0,i.jsx)(n.td,{children:"Interrupts user flow"}),(0,i.jsx)(n.td,{children:"User-initiated only"}),(0,i.jsx)(n.td,{children:"Context-triggered"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"Relevance"})}),(0,i.jsx)(n.td,{children:"Often irrelevant"}),(0,i.jsx)(n.td,{children:"High intent match"}),(0,i.jsx)(n.td,{children:"AI-determined relevance"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"User Experience"})}),(0,i.jsx)(n.td,{children:"Disruptive"}),(0,i.jsx)(n.td,{children:"Expected"}),(0,i.jsx)(n.td,{children:"Enhancing"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"Integration"})}),(0,i.jsx)(n.td,{children:"Separate from content"}),(0,i.jsx)(n.td,{children:"Search-based"}),(0,i.jsx)(n.td,{children:"Native to conversation"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"Intelligence"})}),(0,i.jsx)(n.td,{children:"Rule-based targeting"}),(0,i.jsx)(n.td,{children:"Keyword matching"}),(0,i.jsx)(n.td,{children:"AI intent detection"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:(0,i.jsx)(n.strong,{children:"Format"})}),(0,i.jsx)(n.td,{children:"Banner/display ads"}),(0,i.jsx)(n.td,{children:"Search results"}),(0,i.jsx)(n.td,{children:"Citation references"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"admeshs-unique-advantages",children:"AdMesh's Unique Advantages"}),"\n",(0,i.jsxs)(n.h4,{id:"-intent-detection-without-explicit-search",children:["\ud83c\udfaf ",(0,i.jsx)(n.strong,{children:"Intent Detection Without Explicit Search"})]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'# Traditional: User must search "best CRM software"\n# AdMesh: Detects intent from conversation\n\nuser_message = "I\'m struggling to keep track of my customers"\n# AdMesh AI detects CRM intent and suggests relevant tools\n'})}),"\n",(0,i.jsxs)(n.h4,{id:"-academic-style-citations",children:["\ud83d\udcda ",(0,i.jsx)(n.strong,{children:"Academic-Style Citations"})]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:'Traditional Ad: [\ud83d\udea8 BUY HUBSPOT NOW! 50% OFF! \ud83d\udea8]\n\nAdMesh Citation: "For customer management, consider HubSpot\xb9 \nfor its user-friendly interface..."\n\n\xb9 HubSpot CRM - Intuitive customer management platform\n'})}),"\n",(0,i.jsxs)(n.h4,{id:"-ai-native-integration",children:["\ud83e\udd16 ",(0,i.jsx)(n.strong,{children:"AI-Native Integration"})]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:"// Seamlessly integrates with AI applications\nfunction AIAssistant() {\n  const handleUserQuery = async (query) => {\n    const aiResponse = await getAIResponse(query);\n    const recommendations = await getAdMeshRecommendations(query);\n    \n    return (\n      <div>\n        <AIResponse text={aiResponse} />\n        <AdMeshCitations recommendations={recommendations} />\n      </div>\n    );\n  };\n}\n"})}),"\n",(0,i.jsxs)(n.h4,{id:"-contextual-timing",children:["\ud83d\udd04 ",(0,i.jsx)(n.strong,{children:"Contextual Timing"})]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"// Shows recommendations at optimal moments\nconst shouldShowRecommendations = (conversationContext) => {\n  return {\n    afterProblemStatement: true,    // User describes a challenge\n    beforeDecisionMaking: true,     // User weighing options\n    duringResearch: true,           // User gathering information\n    afterFailureStory: true         // User mentions tool limitations\n  };\n};\n"})}),"\n",(0,i.jsx)(n.h2,{id:"-interactive-storybook-examples",children:"\ud83d\udcca Interactive Storybook Examples"}),"\n",(0,i.jsxs)(n.h3,{id:"-live-component-showcase",children:["\ud83c\udfad ",(0,i.jsx)(n.strong,{children:"Live Component Showcase"})]}),"\n",(0,i.jsxs)(n.p,{children:["AdMesh UI SDK includes an interactive ",(0,i.jsx)(n.strong,{children:"Storybook"})," where you can see and interact with all the storybook ad formats in real-time."]}),"\n",(0,i.jsx)(n.h4,{id:"access-the-storybook",children:(0,i.jsx)(n.strong,{children:"Access the Storybook"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:"# Clone the UI SDK repository\ngit clone https://github.com/GouniManikumar12/admesh-ui-sdk.git\ncd admesh-ui-sdk\n\n# Install dependencies and start Storybook\nnpm install\nnpm run storybook\n"})}),"\n",(0,i.jsxs)(n.p,{children:["The Storybook will open at ",(0,i.jsx)(n.code,{children:"http://localhost:6006"})," with interactive examples of:"]}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"\ud83d\udcda Storybook Ad Formats"})," - Complete narrative examples"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"\ud83d\udcdd Citation Components"})," - Different citation styles"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"\ud83d\udcac Conversational Ads"})," - Chat interface integration"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"\ud83d\udcca Format Comparisons"})," - Traditional vs AdMesh side-by-side"]}),"\n"]}),"\n",(0,i.jsx)(n.h4,{id:"what-youll-find-in-storybook",children:(0,i.jsx)(n.strong,{children:"What You'll Find in Storybook"})}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Interactive Demos"})," - Click citations to see tracking in action"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Theme Variations"})," - Light/dark mode examples"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Citation Styles"})," - Numbered, bracketed, and lettered options"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Real Stories"})," - Business narratives with contextual recommendations"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Performance Comparisons"})," - Visual demonstrations of engagement improvements"]}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"-storybook-integration-examples",children:"\ud83d\udcca Storybook Integration Examples"}),"\n",(0,i.jsx)(n.h3,{id:"example-1-business-advice-story",children:"Example 1: Business Advice Story"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-markdown",children:"**The Startup Founder's Journey**\n\nSarah was a brilliant engineer who decided to start her own SaaS company. \nAs her customer base grew, she realized she needed better tools to manage \ncustomer relationships\xb9 and track her sales pipeline\xb2.\n\nShe also struggled with project management\xb3 as her team expanded, and \nneeded a reliable way to handle customer support tickets\u2074.\n\nReferences:\n\xb9 HubSpot CRM - Free CRM perfect for growing startups\n\xb2 Pipedrive - Visual sales pipeline management\n\xb3 Notion - All-in-one workspace for project management\n\u2074 Intercom - Customer support and messaging platform\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-2-technical-tutorial-story",children:"Example 2: Technical Tutorial Story"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-markdown",children:"**Building Your First AI Chatbot**\n\nOnce upon a time, a developer wanted to build an intelligent chatbot. \nThey needed a framework for natural language processing\xb9, a database \nto store conversation history\xb2, and a platform to deploy their bot\xb3.\n\nThe developer also wanted to add recommendation capabilities\u2074 to make \ntheir chatbot more helpful and potentially monetize it\u2075.\n\nReferences:\n\xb9 OpenAI API - Advanced language models for chatbots\n\xb2 MongoDB Atlas - Cloud database for conversation storage\n\xb3 Vercel - Easy deployment platform for web applications\n\u2074 AdMesh SDK - AI-powered recommendation engine\n\u2075 Stripe - Payment processing for monetization\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-3-e-commerce-journey-story",children:"Example 3: E-commerce Journey Story"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-markdown",children:"**The Online Store Owner's Challenge**\n\nEmma ran a successful online boutique but faced several challenges. \nHer website needed better analytics\xb9 to understand customer behavior, \nand she wanted to improve her email marketing campaigns\xb2.\n\nShe also needed inventory management software\xb3 and was looking for \nways to provide better customer service\u2074.\n\nReferences:\n\xb9 Google Analytics - Comprehensive website analytics\n\xb2 Mailchimp - Email marketing automation platform\n\xb3 TradeGecko - Inventory management for e-commerce\n\u2074 Zendesk - Customer service and support platform\n"})}),"\n",(0,i.jsx)(n.h2,{id:"-storybook-component-implementation",children:"\ud83c\udfad Storybook Component Implementation"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-tsx",children:'import React from \'react\';\nimport { AdMeshCitationUnit } from \'admesh-ui-sdk\';\n\ninterface StorybookAdProps {\n  story: string;\n  recommendations: AdMeshRecommendation[];\n  title: string;\n}\n\nexport function StorybookAd({ story, recommendations, title }: StorybookAdProps) {\n  return (\n    <div className="storybook-container">\n      <h2 className="story-title">{title}</h2>\n      \n      <AdMeshCitationUnit\n        recommendations={recommendations}\n        conversationText={story}\n        citationStyle="numbered"\n        showCitationList={true}\n        onRecommendationClick={(adId, link) => {\n          // Track story-based recommendation clicks\n          trackStorybookClick(adId, title);\n          window.open(link, \'_blank\');\n        }}\n      />\n      \n      <div className="story-footer">\n        <span className="powered-by">\n          \ud83d\udcda Story-based recommendations powered by AdMesh\n        </span>\n      </div>\n    </div>\n  );\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"-implementation-guide",children:"\ud83d\ude80 Implementation Guide"}),"\n",(0,i.jsx)(n.h3,{id:"1-detect-story-context",children:"1. Detect Story Context"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'def detect_story_context(content):\n    story_indicators = [\n        "once upon a time", "story", "journey", "challenge",\n        "struggled with", "needed", "wanted to", "faced"\n    ]\n    \n    context_score = sum(1 for indicator in story_indicators \n                       if indicator in content.lower())\n    \n    return context_score >= 2\n'})}),"\n",(0,i.jsx)(n.h3,{id:"2-extract-recommendation-opportunities",children:"2. Extract Recommendation Opportunities"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'def extract_recommendation_points(story_text):\n    # Look for problem statements and needs\n    patterns = [\n        r"needed (.*?)(?:\\.|,|$)",\n        r"struggled with (.*?)(?:\\.|,|$)",\n        r"wanted (.*?)(?:\\.|,|$)",\n        r"looking for (.*?)(?:\\.|,|$)"\n    ]\n    \n    opportunities = []\n    for pattern in patterns:\n        matches = re.findall(pattern, story_text, re.IGNORECASE)\n        opportunities.extend(matches)\n    \n    return opportunities\n'})}),"\n",(0,i.jsx)(n.h3,{id:"3-generate-contextual-recommendations",children:"3. Generate Contextual Recommendations"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'async def generate_story_recommendations(opportunities):\n    recommendations = []\n    \n    for opportunity in opportunities:\n        # Use AdMesh API to get relevant recommendations\n        response = await admesh_client.recommend.get_recommendations(\n            query=opportunity,\n            format="story_context",\n            max_recommendations=1\n        )\n        recommendations.extend(response.recommendations)\n    \n    return recommendations\n'})}),"\n",(0,i.jsx)(n.h2,{id:"-benefits-of-story-based-ads",children:"\ud83d\udcc8 Benefits of Story-Based Ads"}),"\n",(0,i.jsx)(n.h3,{id:"for-users",children:"For Users"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Non-Intrusive"}),": Enhances rather than interrupts the story"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Contextually Relevant"}),": Recommendations match story context"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Educational"}),": Learn about tools through relatable scenarios"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Natural Discovery"}),": Find solutions through storytelling"]}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"for-advertisers",children:"For Advertisers"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Higher Engagement"}),": Users more receptive to story-integrated ads"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Better Context"}),": Products shown in relevant use cases"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Emotional Connection"}),": Stories create emotional engagement"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Trust Building"}),": Recommendations feel like helpful suggestions"]}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"for-publishers",children:"For Publishers"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Monetization"}),": Generate revenue without disrupting content"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"User Experience"}),": Maintain content quality while monetizing"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Flexibility"}),": Easy integration with existing content"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Analytics"}),": Track story performance and recommendation effectiveness"]}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"-best-practices",children:"\ud83c\udfaf Best Practices"}),"\n",(0,i.jsx)(n.h3,{id:"story-integration",children:"Story Integration"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Natural Flow"}),": Recommendations should feel part of the narrative"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Relevant Timing"}),": Show recommendations when problems are introduced"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Appropriate Quantity"}),": Don't overwhelm with too many citations"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Clear Attribution"}),": Make it clear these are recommendations"]}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"user-experience",children:"User Experience"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Optional Interaction"}),": Users can ignore recommendations"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Value First"}),": Focus on story value, not selling"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Transparent Monetization"}),": Clear about recommendation nature"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Easy Dismissal"}),": Allow users to hide recommendations"]}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"content-quality",children:"Content Quality"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Authentic Stories"}),": Use real, relatable scenarios"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Problem-Solution Fit"}),": Ensure recommendations solve story problems"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Diverse Examples"}),": Cover various industries and use cases"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.strong,{children:"Regular Updates"}),": Keep stories and recommendations current"]}),"\n"]}),"\n",(0,i.jsx)(n.hr,{}),"\n",(0,i.jsx)(n.p,{children:"This story-based, citation-driven approach makes AdMesh fundamentally different from traditional advertising by creating value through context rather than interruption. It's advertising that enhances rather than disrupts the user experience."})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}}}]);