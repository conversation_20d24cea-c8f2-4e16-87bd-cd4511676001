<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-getting-started/ad-formats" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.8.1">
<title data-rh="true">AdMesh Ad Formats | AdMesh SDK Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://admesh-ui-sdk.vercel.app/img/admesh-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://admesh-ui-sdk.vercel.app/img/admesh-social-card.jpg"><meta data-rh="true" property="og:url" content="https://admesh-ui-sdk.vercel.app/getting-started/ad-formats"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="AdMesh Ad Formats | AdMesh SDK Documentation"><meta data-rh="true" name="description" content="Learn about AdMesh&#x27;s unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models."><meta data-rh="true" property="og:description" content="Learn about AdMesh&#x27;s unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models."><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://admesh-ui-sdk.vercel.app/getting-started/ad-formats"><link data-rh="true" rel="alternate" href="https://admesh-ui-sdk.vercel.app/getting-started/ad-formats" hreflang="en"><link data-rh="true" rel="alternate" href="https://admesh-ui-sdk.vercel.app/getting-started/ad-formats" hreflang="x-default"><link data-rh="true" rel="preconnect" href="https://YOUR_APP_ID-dsn.algolia.net" crossorigin="anonymous"><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"AdMesh Ad Formats","item":"https://admesh-ui-sdk.vercel.app/getting-started/ad-formats"}]}</script><link rel="search" type="application/opensearchdescription+xml" title="AdMesh SDK Documentation" href="/opensearch.xml"><link rel="stylesheet" href="/assets/css/styles.0178e490.css">
<script src="/assets/js/runtime~main.84b1124b.js" defer="defer"></script>
<script src="/assets/js/main.dd844d21.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;"><defs>
<symbol id="theme-svg-external-link" viewBox="0 0 24 24"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></symbol>
</defs></svg>
<script>!function(){var t="light";var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();document.documentElement.setAttribute("data-theme",e||t),document.documentElement.setAttribute("data-theme-choice",e||t)}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="theme-layout-navbar navbar navbar--fixed-top"><div class="navbar__inner"><div class="theme-layout-navbar-left navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.svg" alt="AdMesh Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/img/logo.svg" alt="AdMesh Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">AdMesh</b></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/">Documentation</a><a class="navbar__item navbar__link" target="_self" href="/storybook">Storybook</a></div><div class="theme-layout-navbar-right navbar__items navbar__items--right"><a href="https://useadmesh.com" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Dashboard<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Python SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">TypeScript SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-ui-sdk" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">UI SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="system mode" aria-label="Switch between dark and light mode (currently system mode)"><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP systemToggleIcon_QzmC"><path fill="currentColor" d="m12 21c4.971 0 9-4.029 9-9s-4.029-9-9-9-9 4.029-9 9 4.029 9 9 9zm4.95-13.95c1.313 1.313 2.05 3.093 2.05 4.95s-0.738 3.637-2.05 4.95c-1.313 1.313-3.093 2.05-4.95 2.05v-14c1.857 0 3.637 0.737 4.95 2.05z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search (Command+K)"><span class="DocSearch-Button-Container"><svg width="20" height="20" class="DocSearch-Search-Icon" viewBox="0 0 20 20" aria-hidden="true"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">Search</span></span><span class="DocSearch-Button-Keys"></span></button></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="theme-layout-main main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/getting-started/overview">Getting Started</a></div><ul class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/overview">Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/api-keys">Getting Your API Key</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/quick-start">Quick Start Guide</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/getting-started/ad-formats">AdMesh Ad Formats</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/admesh-vs-traditional">AdMesh vs Traditional Advertising</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/python-sdk/installation">Python SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/typescript-sdk/installation">TypeScript SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/ui-sdk/installation">UI SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/ai-integration/overview">AI Agent Integration</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/api/authentication">API Reference</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/examples/ai-assistant">Examples</a></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">Getting Started</span></li><li class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link">AdMesh Ad Formats</span></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>AdMesh Ad Formats</h1></header>
<p>Learn about AdMesh&#x27;s unique approach to advertising through conversational, citation-based, and contextual ad formats that differ fundamentally from traditional push/pull advertising models.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-push-vs-pull-vs-admeshs-contextual-model">🔄 Push vs Pull vs AdMesh&#x27;s Contextual Model<a href="#-push-vs-pull-vs-admeshs-contextual-model" class="hash-link" aria-label="Direct link to 🔄 Push vs Pull vs AdMesh&#x27;s Contextual Model" title="Direct link to 🔄 Push vs Pull vs AdMesh&#x27;s Contextual Model">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="traditional-push-advertising">Traditional Push Advertising<a href="#traditional-push-advertising" class="hash-link" aria-label="Direct link to Traditional Push Advertising" title="Direct link to Traditional Push Advertising">​</a></h3>
<p><strong>Definition</strong>: Ads are forced upon users regardless of their current context or intent.</p>
<p><strong>Examples</strong>:</p>
<ul>
<li>Banner ads on websites</li>
<li>Pop-up advertisements</li>
<li>TV commercials</li>
<li>Social media sponsored posts</li>
</ul>
<p><strong>Problems</strong>:</p>
<ul>
<li>Interrupts user experience</li>
<li>Often irrelevant to current context</li>
<li>Creates ad fatigue</li>
<li>Low engagement rates</li>
<li>Users develop &quot;banner blindness&quot;</li>
</ul>
<!-- -->
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="traditional-pull-advertising">Traditional Pull Advertising<a href="#traditional-pull-advertising" class="hash-link" aria-label="Direct link to Traditional Pull Advertising" title="Direct link to Traditional Pull Advertising">​</a></h3>
<p><strong>Definition</strong>: Users actively seek out advertising content when they&#x27;re ready to make a purchase.</p>
<p><strong>Examples</strong>:</p>
<ul>
<li>Google search ads</li>
<li>Product comparison sites</li>
<li>Shopping platforms</li>
<li>Review websites</li>
</ul>
<p><strong>Problems</strong>:</p>
<ul>
<li>Only captures users at bottom of funnel</li>
<li>High competition and costs</li>
<li>Misses discovery opportunities</li>
<li>Limited to explicit search intent</li>
</ul>
<!-- -->
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="admeshs-contextual-intelligence-model">AdMesh&#x27;s Contextual Intelligence Model<a href="#admeshs-contextual-intelligence-model" class="hash-link" aria-label="Direct link to AdMesh&#x27;s Contextual Intelligence Model" title="Direct link to AdMesh&#x27;s Contextual Intelligence Model">​</a></h3>
<p><strong>Definition</strong>: AI-powered recommendations that appear naturally within conversations and content when contextually relevant.</p>
<p><strong>Key Principles</strong>:</p>
<ul>
<li><strong>Context-Aware</strong>: Understands conversation flow and user intent</li>
<li><strong>Non-Intrusive</strong>: Appears as helpful suggestions, not ads</li>
<li><strong>Intelligent Timing</strong>: Shows recommendations at optimal moments</li>
<li><strong>Citation-Based</strong>: References products like academic sources</li>
<li><strong>Value-First</strong>: Provides genuine value before monetization</li>
</ul>
<!-- -->
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-admesh-ad-formats">🎨 AdMesh Ad Formats<a href="#-admesh-ad-formats" class="hash-link" aria-label="Direct link to 🎨 AdMesh Ad Formats" title="Direct link to 🎨 AdMesh Ad Formats">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-citation-based-recommendations">1. Citation-Based Recommendations<a href="#1-citation-based-recommendations" class="hash-link" aria-label="Direct link to 1. Citation-Based Recommendations" title="Direct link to 1. Citation-Based Recommendations">​</a></h3>
<p>Display recommendations as numbered references within conversational text, similar to academic papers.</p>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports punctuation" style="color:#393A34">{</span><span class="token imports"> </span><span class="token imports maybe-class-name">AdMeshCitationUnit</span><span class="token imports"> </span><span class="token imports punctuation" style="color:#393A34">}</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh-ui-sdk&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshCitationUnit</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">conversationText</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">For your startup&#x27;s CRM needs, I recommend HubSpot for its excellent free tier and Salesforce for enterprise features...</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">citationStyle</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">numbered</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">showCitationList</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript boolean" style="color:#36acaa">true</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f"></span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><br></span></code></pre></div></div>
<p><strong>Output</strong>:</p>
<div class="language-text codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-text codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">For your startup&#x27;s CRM needs, I recommend HubSpot¹ for its excellent </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">free tier and Salesforce² for enterprise features...</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">References:</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">¹ HubSpot CRM - Free tier with excellent startup features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">² Salesforce - Enterprise-grade CRM with advanced automation</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-conversational-recommendations">2. Conversational Recommendations<a href="#2-conversational-recommendations" class="hash-link" aria-label="Direct link to 2. Conversational Recommendations" title="Direct link to 2. Conversational Recommendations">​</a></h3>
<p>Recommendations that appear naturally within chat interfaces and AI conversations.</p>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports punctuation" style="color:#393A34">{</span><span class="token imports"> </span><span class="token imports maybe-class-name">AdMeshConversationalUnit</span><span class="token imports"> </span><span class="token imports punctuation" style="color:#393A34">}</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh-ui-sdk&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshConversationalUnit</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">config</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    displayMode</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript string" style="color:#e3116c">&#x27;inline&#x27;</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    context</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript string" style="color:#e3116c">&#x27;chat&#x27;</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    maxRecommendations</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript number" style="color:#36acaa">3</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    showPoweredBy</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript boolean" style="color:#36acaa">true</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">  </span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f"></span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-auto-triggered-suggestions">3. Auto-Triggered Suggestions<a href="#3-auto-triggered-suggestions" class="hash-link" aria-label="Direct link to 3. Auto-Triggered Suggestions" title="Direct link to 3. Auto-Triggered Suggestions">​</a></h3>
<p>Proactive recommendations that appear based on conversation context without explicit user requests.</p>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports punctuation" style="color:#393A34">{</span><span class="token imports"> </span><span class="token imports maybe-class-name">AdMeshAutoRecommendationWidget</span><span class="token imports"> </span><span class="token imports punctuation" style="color:#393A34">}</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh-ui-sdk&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshAutoRecommendationWidget</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">trigger</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">User mentioned project management challenges</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">autoShow</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript boolean" style="color:#36acaa">true</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">position</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">bottom-right</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f"></span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="4-sidebar-recommendations">4. Sidebar Recommendations<a href="#4-sidebar-recommendations" class="hash-link" aria-label="Direct link to 4. Sidebar Recommendations" title="Direct link to 4. Sidebar Recommendations">​</a></h3>
<p>Persistent recommendation panels that complement main content without interrupting it.</p>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports punctuation" style="color:#393A34">{</span><span class="token imports"> </span><span class="token imports maybe-class-name">AdMeshSidebar</span><span class="token imports"> </span><span class="token imports punctuation" style="color:#393A34">}</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh-ui-sdk&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshSidebar</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">  </span><span class="token tag attr-name" style="color:#00a4db">config</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    position</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript string" style="color:#e3116c">&#x27;right&#x27;</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    displayMode</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript string" style="color:#e3116c">&#x27;recommendations&#x27;</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">    collapsible</span><span class="token tag script language-javascript operator" style="color:#393A34">:</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript boolean" style="color:#36acaa">true</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">  </span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f"></span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-how-admesh-is-different">🧠 How AdMesh is Different<a href="#-how-admesh-is-different" class="hash-link" aria-label="Direct link to 🧠 How AdMesh is Different" title="Direct link to 🧠 How AdMesh is Different">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="traditional-advertising-platforms">Traditional Advertising Platforms<a href="#traditional-advertising-platforms" class="hash-link" aria-label="Direct link to Traditional Advertising Platforms" title="Direct link to Traditional Advertising Platforms">​</a></h3>
<table><thead><tr><th>Aspect</th><th>Traditional Push</th><th>Traditional Pull</th><th>AdMesh Contextual</th></tr></thead><tbody><tr><td><strong>Timing</strong></td><td>Interrupts user flow</td><td>User-initiated only</td><td>Context-triggered</td></tr><tr><td><strong>Relevance</strong></td><td>Often irrelevant</td><td>High intent match</td><td>AI-determined relevance</td></tr><tr><td><strong>User Experience</strong></td><td>Disruptive</td><td>Expected</td><td>Enhancing</td></tr><tr><td><strong>Integration</strong></td><td>Separate from content</td><td>Search-based</td><td>Native to conversation</td></tr><tr><td><strong>Intelligence</strong></td><td>Rule-based targeting</td><td>Keyword matching</td><td>AI intent detection</td></tr><tr><td><strong>Format</strong></td><td>Banner/display ads</td><td>Search results</td><td>Citation references</td></tr></tbody></table>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="admeshs-unique-advantages">AdMesh&#x27;s Unique Advantages<a href="#admeshs-unique-advantages" class="hash-link" aria-label="Direct link to AdMesh&#x27;s Unique Advantages" title="Direct link to AdMesh&#x27;s Unique Advantages">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="-intent-detection-without-explicit-search">🎯 <strong>Intent Detection Without Explicit Search</strong><a href="#-intent-detection-without-explicit-search" class="hash-link" aria-label="Direct link to -intent-detection-without-explicit-search" title="Direct link to -intent-detection-without-explicit-search">​</a></h4>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Traditional: User must search &quot;best CRM software&quot;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># AdMesh: Detects intent from conversation</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">user_message </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;I&#x27;m struggling to keep track of my customers&quot;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># AdMesh AI detects CRM intent and suggests relevant tools</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="-academic-style-citations">📚 <strong>Academic-Style Citations</strong><a href="#-academic-style-citations" class="hash-link" aria-label="Direct link to -academic-style-citations" title="Direct link to -academic-style-citations">​</a></h4>
<div class="language-text codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-text codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">Traditional Ad: [🚨 BUY HUBSPOT NOW! 50% OFF! 🚨]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">AdMesh Citation: &quot;For customer management, consider HubSpot¹ </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">for its user-friendly interface...&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">¹ HubSpot CRM - Intuitive customer management platform</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="-ai-native-integration">🤖 <strong>AI-Native Integration</strong><a href="#-ai-native-integration" class="hash-link" aria-label="Direct link to -ai-native-integration" title="Direct link to -ai-native-integration">​</a></h4>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">// Seamlessly integrates with AI applications</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">function</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">AIAssistant</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> </span><span class="token function-variable function" style="color:#d73a49">handleUserQuery</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">async</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">query</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token arrow operator" style="color:#393A34">=&gt;</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> aiResponse </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">await</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">getAIResponse</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">query</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> recommendations </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">await</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">getAdMeshRecommendations</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">query</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">      </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag" style="color:#00009f">div</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">        </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AIResponse</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">text</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">aiResponse</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"> </span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">        </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshCitations</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"> </span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><span class="token tag punctuation" style="color:#393A34">&lt;/</span><span class="token tag" style="color:#00009f">div</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token punctuation" style="color:#393A34">}</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="-contextual-timing">🔄 <strong>Contextual Timing</strong><a href="#-contextual-timing" class="hash-link" aria-label="Direct link to -contextual-timing" title="Direct link to -contextual-timing">​</a></h4>
<div class="language-javascript codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-javascript codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">// Shows recommendations at optimal moments</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> </span><span class="token function-variable function" style="color:#d73a49">shouldShowRecommendations</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token parameter">conversationContext</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">=&gt;</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token literal-property property" style="color:#36acaa">afterProblemStatement</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">true</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">    </span><span class="token comment" style="color:#999988;font-style:italic">// User describes a challenge</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token literal-property property" style="color:#36acaa">beforeDecisionMaking</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">true</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">     </span><span class="token comment" style="color:#999988;font-style:italic">// User weighing options</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token literal-property property" style="color:#36acaa">duringResearch</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">true</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">           </span><span class="token comment" style="color:#999988;font-style:italic">// User gathering information</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token literal-property property" style="color:#36acaa">afterFailureStory</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">true</span><span class="token plain">         </span><span class="token comment" style="color:#999988;font-style:italic">// User mentions tool limitations</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token punctuation" style="color:#393A34">}</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><span class="token punctuation" style="color:#393A34">;</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-interactive-storybook-examples">📊 Interactive Storybook Examples<a href="#-interactive-storybook-examples" class="hash-link" aria-label="Direct link to 📊 Interactive Storybook Examples" title="Direct link to 📊 Interactive Storybook Examples">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-live-component-showcase">🎭 <strong>Live Component Showcase</strong><a href="#-live-component-showcase" class="hash-link" aria-label="Direct link to -live-component-showcase" title="Direct link to -live-component-showcase">​</a></h3>
<p>AdMesh UI SDK includes an interactive <strong>Storybook</strong> where you can see and interact with all the storybook ad formats in real-time.</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="access-the-storybook"><strong>Access the Storybook</strong><a href="#access-the-storybook" class="hash-link" aria-label="Direct link to access-the-storybook" title="Direct link to access-the-storybook">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Clone the UI SDK repository</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">git</span><span class="token plain"> clone https://github.com/GouniManikumar12/admesh-ui-sdk.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token builtin class-name">cd</span><span class="token plain"> admesh-ui-sdk</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Install dependencies and start Storybook</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> run storybook</span><br></span></code></pre></div></div>
<p>The Storybook will open at <code>http://localhost:6006</code> with interactive examples of:</p>
<ul>
<li><strong>📚 Storybook Ad Formats</strong> - Complete narrative examples</li>
<li><strong>📝 Citation Components</strong> - Different citation styles</li>
<li><strong>💬 Conversational Ads</strong> - Chat interface integration</li>
<li><strong>📊 Format Comparisons</strong> - Traditional vs AdMesh side-by-side</li>
</ul>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="what-youll-find-in-storybook"><strong>What You&#x27;ll Find in Storybook</strong><a href="#what-youll-find-in-storybook" class="hash-link" aria-label="Direct link to what-youll-find-in-storybook" title="Direct link to what-youll-find-in-storybook">​</a></h4>
<ol>
<li><strong>Interactive Demos</strong> - Click citations to see tracking in action</li>
<li><strong>Theme Variations</strong> - Light/dark mode examples</li>
<li><strong>Citation Styles</strong> - Numbered, bracketed, and lettered options</li>
<li><strong>Real Stories</strong> - Business narratives with contextual recommendations</li>
<li><strong>Performance Comparisons</strong> - Visual demonstrations of engagement improvements</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-storybook-integration-examples">📊 Storybook Integration Examples<a href="#-storybook-integration-examples" class="hash-link" aria-label="Direct link to 📊 Storybook Integration Examples" title="Direct link to 📊 Storybook Integration Examples">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="example-1-business-advice-story">Example 1: Business Advice Story<a href="#example-1-business-advice-story" class="hash-link" aria-label="Direct link to Example 1: Business Advice Story" title="Direct link to Example 1: Business Advice Story">​</a></h3>
<div class="language-markdown codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-markdown codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token bold punctuation" style="color:#393A34">**</span><span class="token bold content">The Startup Founder&#x27;s Journey</span><span class="token bold punctuation" style="color:#393A34">**</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Sarah was a brilliant engineer who decided to start her own SaaS company. </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">As her customer base grew, she realized she needed better tools to manage </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">customer relationships¹ and track her sales pipeline².</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">She also struggled with project management³ as her team expanded, and </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">needed a reliable way to handle customer support tickets⁴.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">References:</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">¹ HubSpot CRM - Free CRM perfect for growing startups</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">² Pipedrive - Visual sales pipeline management</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">³ Notion - All-in-one workspace for project management</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">⁴ Intercom - Customer support and messaging platform</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="example-2-technical-tutorial-story">Example 2: Technical Tutorial Story<a href="#example-2-technical-tutorial-story" class="hash-link" aria-label="Direct link to Example 2: Technical Tutorial Story" title="Direct link to Example 2: Technical Tutorial Story">​</a></h3>
<div class="language-markdown codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-markdown codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token bold punctuation" style="color:#393A34">**</span><span class="token bold content">Building Your First AI Chatbot</span><span class="token bold punctuation" style="color:#393A34">**</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Once upon a time, a developer wanted to build an intelligent chatbot. </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">They needed a framework for natural language processing¹, a database </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">to store conversation history², and a platform to deploy their bot³.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">The developer also wanted to add recommendation capabilities⁴ to make </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">their chatbot more helpful and potentially monetize it⁵.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">References:</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">¹ OpenAI API - Advanced language models for chatbots</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">² MongoDB Atlas - Cloud database for conversation storage</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">³ Vercel - Easy deployment platform for web applications</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">⁴ AdMesh SDK - AI-powered recommendation engine</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">⁵ Stripe - Payment processing for monetization</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="example-3-e-commerce-journey-story">Example 3: E-commerce Journey Story<a href="#example-3-e-commerce-journey-story" class="hash-link" aria-label="Direct link to Example 3: E-commerce Journey Story" title="Direct link to Example 3: E-commerce Journey Story">​</a></h3>
<div class="language-markdown codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-markdown codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token bold punctuation" style="color:#393A34">**</span><span class="token bold content">The Online Store Owner&#x27;s Challenge</span><span class="token bold punctuation" style="color:#393A34">**</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Emma ran a successful online boutique but faced several challenges. </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Her website needed better analytics¹ to understand customer behavior, </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">and she wanted to improve her email marketing campaigns².</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">She also needed inventory management software³ and was looking for </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">ways to provide better customer service⁴.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">References:</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">¹ Google Analytics - Comprehensive website analytics</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">² Mailchimp - Email marketing automation platform</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">³ TradeGecko - Inventory management for e-commerce</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">⁴ Zendesk - Customer service and support platform</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-storybook-component-implementation">🎭 Storybook Component Implementation<a href="#-storybook-component-implementation" class="hash-link" aria-label="Direct link to 🎭 Storybook Component Implementation" title="Direct link to 🎭 Storybook Component Implementation">​</a></h2>
<div class="language-tsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-tsx codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports maybe-class-name">React</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;react&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports punctuation" style="color:#393A34">{</span><span class="token imports"> </span><span class="token imports maybe-class-name">AdMeshCitationUnit</span><span class="token imports"> </span><span class="token imports punctuation" style="color:#393A34">}</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh-ui-sdk&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">interface</span><span class="token plain"> </span><span class="token class-name">StorybookAdProps</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  story</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token builtin">string</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  recommendations</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token maybe-class-name">AdMeshRecommendation</span><span class="token punctuation" style="color:#393A34">[</span><span class="token punctuation" style="color:#393A34">]</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  title</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token builtin">string</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">export</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">function</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">StorybookAd</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"> story</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> recommendations</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> title </span><span class="token punctuation" style="color:#393A34">}</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token maybe-class-name">StorybookAdProps</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag" style="color:#00009f">div</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">className</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">storybook-container</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag" style="color:#00009f">h2</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">className</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">story-title</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">title</span><span class="token punctuation" style="color:#393A34">}</span><span class="token tag punctuation" style="color:#393A34">&lt;/</span><span class="token tag" style="color:#00009f">h2</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag class-name" style="color:#00009f">AdMeshCitationUnit</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">        </span><span class="token tag attr-name" style="color:#00a4db">recommendations</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">recommendations</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">        </span><span class="token tag attr-name" style="color:#00a4db">conversationText</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f">story</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">        </span><span class="token tag attr-name" style="color:#00a4db">citationStyle</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">numbered</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">        </span><span class="token tag attr-name" style="color:#00a4db">showCitationList</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript boolean" style="color:#36acaa">true</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">        </span><span class="token tag attr-name" style="color:#00a4db">onRecommendationClick</span><span class="token tag script language-javascript script-punctuation punctuation" style="color:#393A34">=</span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript punctuation" style="color:#393A34">(</span><span class="token tag script language-javascript" style="color:#00009f">adId</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"> link</span><span class="token tag script language-javascript punctuation" style="color:#393A34">)</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript arrow operator" style="color:#393A34">=&gt;</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript punctuation" style="color:#393A34">{</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">          </span><span class="token tag script language-javascript comment" style="color:#999988;font-style:italic">// Track story-based recommendation clicks</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">          </span><span class="token tag script language-javascript function" style="color:#d73a49">trackStorybookClick</span><span class="token tag script language-javascript punctuation" style="color:#393A34">(</span><span class="token tag script language-javascript" style="color:#00009f">adId</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"> title</span><span class="token tag script language-javascript punctuation" style="color:#393A34">)</span><span class="token tag script language-javascript punctuation" style="color:#393A34">;</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">          </span><span class="token tag script language-javascript dom variable" style="color:#36acaa">window</span><span class="token tag script language-javascript punctuation" style="color:#393A34">.</span><span class="token tag script language-javascript method function property-access" style="color:#d73a49">open</span><span class="token tag script language-javascript punctuation" style="color:#393A34">(</span><span class="token tag script language-javascript" style="color:#00009f">link</span><span class="token tag script language-javascript punctuation" style="color:#393A34">,</span><span class="token tag script language-javascript" style="color:#00009f"> </span><span class="token tag script language-javascript string" style="color:#e3116c">&#x27;_blank&#x27;</span><span class="token tag script language-javascript punctuation" style="color:#393A34">)</span><span class="token tag script language-javascript punctuation" style="color:#393A34">;</span><span class="token tag script language-javascript" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag script language-javascript" style="color:#00009f">        </span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag script language-javascript punctuation" style="color:#393A34">}</span><span class="token tag" style="color:#00009f"></span><br></span><span class="token-line" style="color:#393A34"><span class="token tag" style="color:#00009f">      </span><span class="token tag punctuation" style="color:#393A34">/&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag" style="color:#00009f">div</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">className</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">story-footer</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">        </span><span class="token tag punctuation" style="color:#393A34">&lt;</span><span class="token tag" style="color:#00009f">span</span><span class="token tag" style="color:#00009f"> </span><span class="token tag attr-name" style="color:#00a4db">className</span><span class="token tag attr-value punctuation attr-equals" style="color:#393A34">=</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag attr-value" style="color:#e3116c">powered-by</span><span class="token tag attr-value punctuation" style="color:#393A34">&quot;</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">          📚 Story-based recommendations powered by AdMesh</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">        </span><span class="token tag punctuation" style="color:#393A34">&lt;/</span><span class="token tag" style="color:#00009f">span</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">      </span><span class="token tag punctuation" style="color:#393A34">&lt;/</span><span class="token tag" style="color:#00009f">div</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain-text"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain-text">    </span><span class="token tag punctuation" style="color:#393A34">&lt;/</span><span class="token tag" style="color:#00009f">div</span><span class="token tag punctuation" style="color:#393A34">&gt;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-implementation-guide">🚀 Implementation Guide<a href="#-implementation-guide" class="hash-link" aria-label="Direct link to 🚀 Implementation Guide" title="Direct link to 🚀 Implementation Guide">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-detect-story-context">1. Detect Story Context<a href="#1-detect-story-context" class="hash-link" aria-label="Direct link to 1. Detect Story Context" title="Direct link to 1. Detect Story Context">​</a></h3>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">def</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">detect_story_context</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">content</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    story_indicators </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">[</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">&quot;once upon a time&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;story&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;journey&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;challenge&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">&quot;struggled with&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;needed&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;wanted to&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;faced&quot;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    context_score </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token builtin">sum</span><span class="token punctuation" style="color:#393A34">(</span><span class="token number" style="color:#36acaa">1</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">for</span><span class="token plain"> indicator </span><span class="token keyword" style="color:#00009f">in</span><span class="token plain"> story_indicators </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                       </span><span class="token keyword" style="color:#00009f">if</span><span class="token plain"> indicator </span><span class="token keyword" style="color:#00009f">in</span><span class="token plain"> content</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">lower</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> context_score </span><span class="token operator" style="color:#393A34">&gt;=</span><span class="token plain"> </span><span class="token number" style="color:#36acaa">2</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-extract-recommendation-opportunities">2. Extract Recommendation Opportunities<a href="#2-extract-recommendation-opportunities" class="hash-link" aria-label="Direct link to 2. Extract Recommendation Opportunities" title="Direct link to 2. Extract Recommendation Opportunities">​</a></h3>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">def</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">extract_recommendation_points</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">story_text</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token comment" style="color:#999988;font-style:italic"># Look for problem statements and needs</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    patterns </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">[</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">r&quot;needed (.*?)(?:\.|,|$)&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">r&quot;struggled with (.*?)(?:\.|,|$)&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">r&quot;wanted (.*?)(?:\.|,|$)&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token string" style="color:#e3116c">r&quot;looking for (.*?)(?:\.|,|$)&quot;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    opportunities </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">[</span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">for</span><span class="token plain"> pattern </span><span class="token keyword" style="color:#00009f">in</span><span class="token plain"> patterns</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        matches </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> re</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">findall</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">pattern</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> story_text</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> re</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">IGNORECASE</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        opportunities</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">extend</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">matches</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> opportunities</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-generate-contextual-recommendations">3. Generate Contextual Recommendations<a href="#3-generate-contextual-recommendations" class="hash-link" aria-label="Direct link to 3. Generate Contextual Recommendations" title="Direct link to 3. Generate Contextual Recommendations">​</a></h3>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">async</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">def</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">generate_story_recommendations</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">opportunities</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    recommendations </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">[</span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">for</span><span class="token plain"> opportunity </span><span class="token keyword" style="color:#00009f">in</span><span class="token plain"> opportunities</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token comment" style="color:#999988;font-style:italic"># Use AdMesh API to get relevant recommendations</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        response </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">await</span><span class="token plain"> admesh_client</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">recommend</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">get_recommendations</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            query</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">opportunity</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            </span><span class="token builtin">format</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;story_context&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            max_recommendations</span><span class="token operator" style="color:#393A34">=</span><span class="token number" style="color:#36acaa">1</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        recommendations</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">extend</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">response</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">recommendations</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">return</span><span class="token plain"> recommendations</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-benefits-of-story-based-ads">📈 Benefits of Story-Based Ads<a href="#-benefits-of-story-based-ads" class="hash-link" aria-label="Direct link to 📈 Benefits of Story-Based Ads" title="Direct link to 📈 Benefits of Story-Based Ads">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="for-users">For Users<a href="#for-users" class="hash-link" aria-label="Direct link to For Users" title="Direct link to For Users">​</a></h3>
<ul>
<li><strong>Non-Intrusive</strong>: Enhances rather than interrupts the story</li>
<li><strong>Contextually Relevant</strong>: Recommendations match story context</li>
<li><strong>Educational</strong>: Learn about tools through relatable scenarios</li>
<li><strong>Natural Discovery</strong>: Find solutions through storytelling</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="for-advertisers">For Advertisers<a href="#for-advertisers" class="hash-link" aria-label="Direct link to For Advertisers" title="Direct link to For Advertisers">​</a></h3>
<ul>
<li><strong>Higher Engagement</strong>: Users more receptive to story-integrated ads</li>
<li><strong>Better Context</strong>: Products shown in relevant use cases</li>
<li><strong>Emotional Connection</strong>: Stories create emotional engagement</li>
<li><strong>Trust Building</strong>: Recommendations feel like helpful suggestions</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="for-publishers">For Publishers<a href="#for-publishers" class="hash-link" aria-label="Direct link to For Publishers" title="Direct link to For Publishers">​</a></h3>
<ul>
<li><strong>Monetization</strong>: Generate revenue without disrupting content</li>
<li><strong>User Experience</strong>: Maintain content quality while monetizing</li>
<li><strong>Flexibility</strong>: Easy integration with existing content</li>
<li><strong>Analytics</strong>: Track story performance and recommendation effectiveness</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="-best-practices">🎯 Best Practices<a href="#-best-practices" class="hash-link" aria-label="Direct link to 🎯 Best Practices" title="Direct link to 🎯 Best Practices">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="story-integration">Story Integration<a href="#story-integration" class="hash-link" aria-label="Direct link to Story Integration" title="Direct link to Story Integration">​</a></h3>
<ol>
<li><strong>Natural Flow</strong>: Recommendations should feel part of the narrative</li>
<li><strong>Relevant Timing</strong>: Show recommendations when problems are introduced</li>
<li><strong>Appropriate Quantity</strong>: Don&#x27;t overwhelm with too many citations</li>
<li><strong>Clear Attribution</strong>: Make it clear these are recommendations</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="user-experience">User Experience<a href="#user-experience" class="hash-link" aria-label="Direct link to User Experience" title="Direct link to User Experience">​</a></h3>
<ol>
<li><strong>Optional Interaction</strong>: Users can ignore recommendations</li>
<li><strong>Value First</strong>: Focus on story value, not selling</li>
<li><strong>Transparent Monetization</strong>: Clear about recommendation nature</li>
<li><strong>Easy Dismissal</strong>: Allow users to hide recommendations</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="content-quality">Content Quality<a href="#content-quality" class="hash-link" aria-label="Direct link to Content Quality" title="Direct link to Content Quality">​</a></h3>
<ol>
<li><strong>Authentic Stories</strong>: Use real, relatable scenarios</li>
<li><strong>Problem-Solution Fit</strong>: Ensure recommendations solve story problems</li>
<li><strong>Diverse Examples</strong>: Cover various industries and use cases</li>
<li><strong>Regular Updates</strong>: Keep stories and recommendations current</li>
</ol>
<hr>
<p>This story-based, citation-driven approach makes AdMesh fundamentally different from traditional advertising by creating value through context rather than interruption. It&#x27;s advertising that enhances rather than disrupts the user experience.</p></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/getting-started/ad-formats.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="docusaurus-mt-lg pagination-nav" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/getting-started/quick-start"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Quick Start Guide</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/getting-started/admesh-vs-traditional"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">AdMesh vs Traditional Advertising</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#-push-vs-pull-vs-admeshs-contextual-model" class="table-of-contents__link toc-highlight">🔄 Push vs Pull vs AdMesh&#39;s Contextual Model</a><ul><li><a href="#traditional-push-advertising" class="table-of-contents__link toc-highlight">Traditional Push Advertising</a></li><li><a href="#traditional-pull-advertising" class="table-of-contents__link toc-highlight">Traditional Pull Advertising</a></li><li><a href="#admeshs-contextual-intelligence-model" class="table-of-contents__link toc-highlight">AdMesh&#39;s Contextual Intelligence Model</a></li></ul></li><li><a href="#-admesh-ad-formats" class="table-of-contents__link toc-highlight">🎨 AdMesh Ad Formats</a><ul><li><a href="#1-citation-based-recommendations" class="table-of-contents__link toc-highlight">1. Citation-Based Recommendations</a></li><li><a href="#2-conversational-recommendations" class="table-of-contents__link toc-highlight">2. Conversational Recommendations</a></li><li><a href="#3-auto-triggered-suggestions" class="table-of-contents__link toc-highlight">3. Auto-Triggered Suggestions</a></li><li><a href="#4-sidebar-recommendations" class="table-of-contents__link toc-highlight">4. Sidebar Recommendations</a></li></ul></li><li><a href="#-how-admesh-is-different" class="table-of-contents__link toc-highlight">🧠 How AdMesh is Different</a><ul><li><a href="#traditional-advertising-platforms" class="table-of-contents__link toc-highlight">Traditional Advertising Platforms</a></li><li><a href="#admeshs-unique-advantages" class="table-of-contents__link toc-highlight">AdMesh&#39;s Unique Advantages</a></li></ul></li><li><a href="#-interactive-storybook-examples" class="table-of-contents__link toc-highlight">📊 Interactive Storybook Examples</a><ul><li><a href="#-live-component-showcase" class="table-of-contents__link toc-highlight">🎭 <strong>Live Component Showcase</strong></a></li></ul></li><li><a href="#-storybook-integration-examples" class="table-of-contents__link toc-highlight">📊 Storybook Integration Examples</a><ul><li><a href="#example-1-business-advice-story" class="table-of-contents__link toc-highlight">Example 1: Business Advice Story</a></li><li><a href="#example-2-technical-tutorial-story" class="table-of-contents__link toc-highlight">Example 2: Technical Tutorial Story</a></li><li><a href="#example-3-e-commerce-journey-story" class="table-of-contents__link toc-highlight">Example 3: E-commerce Journey Story</a></li></ul></li><li><a href="#-storybook-component-implementation" class="table-of-contents__link toc-highlight">🎭 Storybook Component Implementation</a></li><li><a href="#-implementation-guide" class="table-of-contents__link toc-highlight">🚀 Implementation Guide</a><ul><li><a href="#1-detect-story-context" class="table-of-contents__link toc-highlight">1. Detect Story Context</a></li><li><a href="#2-extract-recommendation-opportunities" class="table-of-contents__link toc-highlight">2. Extract Recommendation Opportunities</a></li><li><a href="#3-generate-contextual-recommendations" class="table-of-contents__link toc-highlight">3. Generate Contextual Recommendations</a></li></ul></li><li><a href="#-benefits-of-story-based-ads" class="table-of-contents__link toc-highlight">📈 Benefits of Story-Based Ads</a><ul><li><a href="#for-users" class="table-of-contents__link toc-highlight">For Users</a></li><li><a href="#for-advertisers" class="table-of-contents__link toc-highlight">For Advertisers</a></li><li><a href="#for-publishers" class="table-of-contents__link toc-highlight">For Publishers</a></li></ul></li><li><a href="#-best-practices" class="table-of-contents__link toc-highlight">🎯 Best Practices</a><ul><li><a href="#story-integration" class="table-of-contents__link toc-highlight">Story Integration</a></li><li><a href="#user-experience" class="table-of-contents__link toc-highlight">User Experience</a></li><li><a href="#content-quality" class="table-of-contents__link toc-highlight">Content Quality</a></li></ul></li></ul></div></div></div></div></main></div></div></div><footer class="theme-layout-footer footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Documentation</div><ul class="footer__items clean-list"><li class="footer__item"><a class="footer__link-item" href="/getting-started/overview">Getting Started</a></li><li class="footer__item"><a class="footer__link-item" href="/python-sdk/installation">Python SDK</a></li><li class="footer__item"><a class="footer__link-item" href="/typescript-sdk/installation">TypeScript SDK</a></li><li class="footer__item"><a class="footer__link-item" href="/ui-sdk/installation">UI SDK</a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">SDKs</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" class="footer__link-item">Python SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" class="footer__link-item">TypeScript SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-ui-sdk" target="_blank" rel="noopener noreferrer" class="footer__link-item">UI SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://useadmesh.com" target="_blank" rel="noopener noreferrer" class="footer__link-item">AdMesh Dashboard<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12" target="_blank" rel="noopener noreferrer" class="footer__link-item">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" class="footer__link-item">Support<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 AdMesh. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>