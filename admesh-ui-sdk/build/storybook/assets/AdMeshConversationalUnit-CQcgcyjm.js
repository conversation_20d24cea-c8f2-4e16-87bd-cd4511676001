import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as m}from"./iframe-CaenRy-o.js";import{c as M}from"./AdMeshLinkTracker-7Al4Klcr.js";import{A as c}from"./AdMeshInlineRecommendation-B1kKoBBP.js";import{A as k}from"./AdMeshConversationSummary-Dd3pYoR4.js";import{A}from"./AdMeshProductCard-DUNDRGIQ.js";import{A as C}from"./AdMeshCitationUnit-DwppmyLf.js";const T=({recommendations:u,config:a,theme:s,conversationSummary:o,sessionId:g,onRecommendationClick:l,onDismiss:d,className:f})=>{const[j,p]=m.useState(a.autoShow!==!1),[x,y]=m.useState(!1);if(m.useEffect(()=>{if(a.delayMs&&a.delayMs>0){const r=setTimeout(()=>{p(!0),y(!0)},a.delayMs);return()=>clearTimeout(r)}else y(!0)},[a.delayMs]),!j||u.length===0)return null;const h=a.maxRecommendations||3,t=u.slice(0,h),i=(r,n)=>{l==null||l(r,n)},v=()=>{p(!1),d==null||d()},w=()=>{switch(a.displayMode){case"summary":return o?e.jsx(k,{recommendations:t,conversationSummary:o,theme:s,showTopRecommendations:h,onRecommendationClick:i,onStartNewConversation:d}):null;case"inline":return e.jsx("div",{className:"space-y-2",children:t.map((r,n)=>e.jsx(c,{recommendation:r,theme:s,compact:!0,showReason:!0,onClick:i},r.ad_id||n))});case"minimal":return t.length>0?e.jsxs("div",{className:"admesh-minimal-unit",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.length," intelligent match",t.length>1?"es":""," found"]})]}),e.jsx(c,{recommendation:t[0],theme:s,compact:!0,showReason:!1,onClick:i}),t.length>1&&e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["+",t.length-1," more recommendation",t.length>2?"s":""]})]}):null;case"citation":return o?e.jsx(C,{recommendations:t,conversationText:o,theme:s,showCitationList:!0,citationStyle:"numbered",onRecommendationClick:i}):null;case"floating":return e.jsxs("div",{className:"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200",children:"Recommended for you"})]}),d&&e.jsx("button",{onClick:v,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"space-y-2",children:t.map((r,n)=>e.jsx(c,{recommendation:r,theme:s,compact:!0,showReason:!1,onClick:i},r.ad_id||n))})]});default:return e.jsx("div",{className:"space-y-3",children:t.map((r,n)=>e.jsx(A,{recommendation:r,theme:s,showMatchScore:!1,showBadges:!0,onClick:i},r.ad_id||n))})}},b=M("admesh-conversational-unit","transition-all duration-300 ease-in-out",{"opacity-0 translate-y-2":!x,"opacity-100 translate-y-0":x,"fixed bottom-4 right-4 max-w-sm z-50":a.displayMode==="floating","my-3":a.displayMode==="inline","mt-4 pt-4 border-t border-gray-200 dark:border-slate-700":a.displayMode==="summary"},f),N=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return e.jsxs("div",{className:b,style:N,"data-admesh-theme":s==null?void 0:s.mode,"data-admesh-context":a.context,"data-session-id":g,children:[w(),a.showPoweredBy!==!1&&e.jsx("div",{className:"flex justify-end mt-2",children:e.jsx("span",{className:"text-xs text-gray-400 dark:text-gray-500",children:"Powered by AdMesh"})})]})};T.__docgenInfo={description:"",methods:[],displayName:"AdMeshConversationalUnit",props:{recommendations:{required:!0,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},config:{required:!0,tsType:{name:"ConversationalAdConfig"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},conversationSummary:{required:!1,tsType:{name:"string"},description:""},userQuery:{required:!1,tsType:{name:"string"},description:""},sessionId:{required:!1,tsType:{name:"string"},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onDismiss:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{T as A};
