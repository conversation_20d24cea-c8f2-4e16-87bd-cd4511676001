import{g as C,r as i}from"./iframe-CaenRy-o.js";import{j as A}from"./jsx-runtime-D_zvdyIk.js";var w={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var T;function x(){return T||(T=1,function(a){(function(){var u={}.hasOwnProperty;function o(){for(var e="",r=0;r<arguments.length;r++){var s=arguments[r];s&&(e=c(e,d(s)))}return e}function d(e){if(typeof e=="string"||typeof e=="number")return e;if(typeof e!="object")return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var r="";for(var s in e)u.call(e,s)&&e[s]&&(r=c(r,s));return r}function c(e,r){return r?e?e+" "+r:e+r:e}a.exports?(o.default=o,a.exports=o):window.classNames=o})()}(w)),w.exports}var _=x();const N=C(_),R="https://api.useadmesh.com/track";let S={apiBaseUrl:R,enabled:!0,debug:!1,retryAttempts:3,retryDelay:1e3};const q=a=>{const[u,o]=i.useState(!1),[d,c]=i.useState(null),e=i.useMemo(()=>({...S,...a}),[a]),r=i.useCallback((t,n)=>{e.debug&&console.log(`[AdMesh Tracker] ${t}`,n)},[e.debug]),s=i.useCallback(async(t,n)=>{if(!e.enabled){r("Tracking disabled, skipping event",{eventType:t,data:n});return}if(!n.adId||!n.admeshLink){const p="Missing required tracking data: adId and admeshLink are required";r(p,n),c(p);return}o(!0),c(null);const k={event_type:t,ad_id:n.adId,admesh_link:n.admeshLink,product_id:n.productId,user_id:n.userId,session_id:n.sessionId,revenue:n.revenue,conversion_type:n.conversionType,metadata:n.metadata,timestamp:new Date().toISOString(),user_agent:navigator.userAgent,referrer:document.referrer,page_url:window.location.href};r(`Sending ${t} event`,k);let l=null;for(let p=1;p<=(e.retryAttempts||3);p++)try{const f=await fetch(`${e.apiBaseUrl}/events`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(k)});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const h=await f.json();r(`${t} event tracked successfully`,h),o(!1);return}catch(f){l=f,r(`Attempt ${p} failed for ${t} event`,f),p<(e.retryAttempts||3)&&await new Promise(h=>setTimeout(h,(e.retryDelay||1e3)*p))}const v=`Failed to track ${t} event after ${e.retryAttempts} attempts: ${l==null?void 0:l.message}`;r(v,l),c(v),o(!1)},[e,r]),y=i.useCallback(async t=>s("click",t),[s]),m=i.useCallback(async t=>s("view",t),[s]),g=i.useCallback(async t=>(!t.revenue&&!t.conversionType&&r("Warning: Conversion tracking without revenue or conversion type",t),s("conversion",t)),[s,r]);return{trackClick:y,trackView:m,trackConversion:g,isTracking:u,error:d}},b=({adId:a,admeshLink:u,productId:o,children:d,onClick:c,trackingData:e,className:r})=>{const{trackClick:s,trackView:y}=q(),m=i.useRef(null),g=i.useRef(!1);i.useEffect(()=>{if(!m.current||g.current)return;const n=new IntersectionObserver(k=>{k.forEach(l=>{l.isIntersecting&&!g.current&&(g.current=!0,y({adId:a,admeshLink:u,productId:o,...e}).catch(console.error))})},{threshold:.5,rootMargin:"0px"});return n.observe(m.current),()=>{n.disconnect()}},[a,u,o,e,y]);const t=i.useCallback(async n=>{try{await s({adId:a,admeshLink:u,productId:o,...e})}catch(v){console.error("Failed to track click:",v)}c&&c(),n.target.closest("a")||window.open(u,"_blank","noopener,noreferrer")},[a,u,o,e,s,c]);return A.jsx("div",{ref:m,className:r,onClick:t,style:{cursor:"pointer"},children:d})};b.displayName="AdMeshLinkTracker";b.__docgenInfo={description:"",methods:[],displayName:"AdMeshLinkTracker",props:{adId:{required:!0,tsType:{name:"string"},description:""},admeshLink:{required:!0,tsType:{name:"string"},description:""},productId:{required:!1,tsType:{name:"string"},description:""},children:{required:!0,tsType:{name:"ReactReactNode",raw:"React.ReactNode"},description:""},onClick:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},trackingData:{required:!1,tsType:{name:"Record",elements:[{name:"string"},{name:"unknown"}],raw:"Record<string, unknown>"},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{b as A,N as c};
