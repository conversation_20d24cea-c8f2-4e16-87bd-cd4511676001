import{j as a}from"./jsx-runtime-D_zvdyIk.js";import"./iframe-DBb7zPuL.js";import{c as d}from"./AdMeshLinkTracker-Cu4CWCJq.js";const o={"Top Match":"primary","Free Tier":"success","AI Powered":"secondary",Popular:"warning",New:"primary","Trial Available":"success"},c={"Top Match":"★","Free Tier":"◆","AI Powered":"◉",Popular:"▲",New:"●","Trial Available":"◈"},r=({type:e,variant:l,size:n="md",className:i})=>{const t=l||o[e]||"secondary",s=c[e],m=d("admesh-component","admesh-badge",`admesh-badge--${t}`,`admesh-badge--${n}`,i);return a.jsxs("span",{className:m,children:[s&&a.jsx("span",{className:"admesh-badge__icon",children:s}),a.jsx("span",{className:"admesh-badge__text",children:e})]})};r.displayName="AdMeshBadge";r.__docgenInfo={description:"",methods:[],displayName:"AdMeshBadge",props:{type:{required:!0,tsType:{name:"union",raw:`| 'Top Match' 
| 'Free Tier' 
| 'AI Powered' 
| 'Popular' 
| 'New'
| 'Trial Available'`,elements:[{name:"literal",value:"'Top Match'"},{name:"literal",value:"'Free Tier'"},{name:"literal",value:"'AI Powered'"},{name:"literal",value:"'Popular'"},{name:"literal",value:"'New'"},{name:"literal",value:"'Trial Available'"}]},description:""},variant:{required:!1,tsType:{name:"union",raw:"'primary' | 'secondary' | 'success' | 'warning'",elements:[{name:"literal",value:"'primary'"},{name:"literal",value:"'secondary'"},{name:"literal",value:"'success'"},{name:"literal",value:"'warning'"}]},description:""},size:{required:!1,tsType:{name:"union",raw:"'sm' | 'md' | 'lg'",elements:[{name:"literal",value:"'sm'"},{name:"literal",value:"'md'"},{name:"literal",value:"'lg'"}]},description:"",defaultValue:{value:"'md'",computed:!1}},className:{required:!1,tsType:{name:"string"},description:""}}};export{r as A};
