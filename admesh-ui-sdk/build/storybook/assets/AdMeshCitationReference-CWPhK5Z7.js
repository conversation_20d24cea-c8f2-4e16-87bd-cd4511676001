import{j as r}from"./jsx-runtime-D_zvdyIk.js";import{r as g}from"./iframe-DBb7zPuL.js";import{c as f,A as v}from"./AdMeshLinkTracker-Cu4CWCJq.js";const y=({recommendation:e,citationNumber:a,citationStyle:s="numbered",theme:t,showTooltip:l=!0,onClick:n,onHover:d,className:u})=>{const[o,i]=g.useState(!1),c=()=>{i(!0),d==null||d(e)},p=()=>{i(!1)},m=()=>{n==null||n(e.ad_id,e.admesh_link)},x=()=>{switch(s){case"bracketed":return`[${a}]`;case"superscript":return a.toString();case"numbered":default:return a.toString()}},b=f("admesh-citation-reference","inline-flex items-center justify-center","cursor-pointer transition-all duration-200","text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300","font-medium",{"w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50":s==="numbered","px-1 text-sm hover:underline":s==="bracketed","text-xs align-super hover:underline":s==="superscript"},u),h=t!=null&&t.accentColor?{"--admesh-primary":t.accentColor}:void 0;return r.jsxs("span",{className:"relative inline-block",children:[r.jsx(v,{adId:e.ad_id,admeshLink:e.admesh_link,productId:e.product_id,onClick:m,trackingData:{title:e.title,matchScore:e.intent_match_score,citationNumber:a,citationStyle:s},className:b,children:r.jsx("span",{style:h,"data-admesh-theme":t==null?void 0:t.mode,onMouseEnter:c,onMouseLeave:p,children:x()})}),l&&o&&r.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50",children:r.jsxs("div",{className:"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs",children:[r.jsx("div",{className:"font-semibold mb-1",children:e.title}),e.reason&&r.jsx("div",{className:"text-gray-300 dark:text-gray-600 text-xs",children:e.reason.length>100?`${e.reason.substring(0,100)}...`:e.reason}),e.intent_match_score>=.7&&r.jsxs("div",{className:"text-green-400 dark:text-green-600 text-xs mt-1",children:[Math.round(e.intent_match_score*100),"% match"]}),r.jsx("div",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1 italic",children:"Click to visit product page"}),r.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"})]})})]})};y.__docgenInfo={description:"",methods:[],displayName:"AdMeshCitationReference",props:{recommendation:{required:!0,tsType:{name:"AdMeshRecommendation"},description:""},citationNumber:{required:!0,tsType:{name:"number"},description:""},citationStyle:{required:!1,tsType:{name:"union",raw:"'numbered' | 'bracketed' | 'superscript'",elements:[{name:"literal",value:"'numbered'"},{name:"literal",value:"'bracketed'"},{name:"literal",value:"'superscript'"}]},description:"",defaultValue:{value:"'numbered'",computed:!1}},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},showTooltip:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},onClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onHover:{required:!1,tsType:{name:"signature",type:"function",raw:"(recommendation: AdMeshRecommendation) => void",signature:{arguments:[{type:{name:"AdMeshRecommendation"},name:"recommendation"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{y as A};
