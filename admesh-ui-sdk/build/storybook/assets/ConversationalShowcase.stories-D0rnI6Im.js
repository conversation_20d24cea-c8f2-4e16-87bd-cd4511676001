import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as l}from"./iframe-6V_WvihQ.js";import"./AdMeshProductCard-BhpBY9Ff.js";import"./AdMeshLayout-DBgV3k-C.js";import"./AdMeshBadge-B-r0fatN.js";import"./AdMeshLinkTracker-BhGdnnRa.js";import{A as d}from"./AdMeshConversationalUnit-DQbn-Ijw.js";import{A as g}from"./AdMeshConversationSummary-wBmTX9d3.js";import{A as f}from"./AdMeshInlineRecommendation-iUkU6sFQ.js";import"./AdMeshCitationUnit-BfnoZkX0.js";import"./AdMeshCitationReference-Dm80h120.js";import"./AdMeshSidebar-DZv8zzrp.js";import"./AdMeshFloatingChat-BHkgq8np.js";import"./AdMeshChatInterface-DhjuPLqh.js";import"./AdMeshAutoRecommendationWidget-J9FDmkKX.js";const o=[{title:"HubSpot CRM",reason:"Perfect for remote teams with excellent collaboration features and robust automation capabilities",intent_match_score:.92,admesh_link:"https://useadmesh.com/track?ad_id=hubspot-123",ad_id:"hubspot-123",product_id:"hubspot-crm",has_free_tier:!0,trial_days:14,keywords:["CRM","Sales","Marketing","Automation"],categories:["Sales Tools","CRM"],features:["Contact Management","Deal Pipeline","Email Integration"],pricing:"Free tier available, paid plans from $45/month"},{title:"Salesforce Sales Cloud",reason:"Enterprise-grade CRM with advanced analytics and customization options for growing businesses",intent_match_score:.87,admesh_link:"https://useadmesh.com/track?ad_id=salesforce-456",ad_id:"salesforce-456",product_id:"salesforce-sales",has_free_tier:!1,trial_days:30,keywords:["CRM","Enterprise","Analytics","Customization"],categories:["Sales Tools","Enterprise Software"],features:["Advanced Analytics","Custom Objects","Workflow Automation"],pricing:"Starting from $25/user/month"},{title:"Pipedrive",reason:"Simple and intuitive CRM designed specifically for small to medium businesses",intent_match_score:.81,admesh_link:"https://useadmesh.com/track?ad_id=pipedrive-789",ad_id:"pipedrive-789",product_id:"pipedrive-crm",has_free_tier:!1,trial_days:14,keywords:["CRM","Simple","SMB","Pipeline"],categories:["Sales Tools","Small Business"],features:["Visual Pipeline","Activity Reminders","Email Sync"],pricing:"Starting from $14.90/user/month"}],y=()=>{const[a,n]=l.useState("chat"),[s,c]=l.useState(!1),t=(r,m)=>{alert(`Clicked: ${r}
Link: ${m}`)},x=()=>{c(!1),n("chat"),alert("Starting new conversation...")};return e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"AdMesh Conversational Ad Units"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Explore different ways to integrate product recommendations into conversational experiences."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-6",children:[e.jsx("button",{onClick:()=>n("chat"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${a==="chat"?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"Chat Interface"}),e.jsx("button",{onClick:()=>n("comparison"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${a==="comparison"?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"Display Modes"}),e.jsxs("button",{onClick:()=>c(!s),className:`px-4 py-2 rounded-lg font-medium transition-colors ${s?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[s?"Hide":"Show"," Summary"]})]}),a==="chat"&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-4",children:"Simulated Chat Interface"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"bg-blue-600 text-white rounded-lg px-4 py-2 max-w-xs",children:"I need a CRM for my remote team. Any recommendations?"})}),e.jsx("div",{className:"flex justify-start",children:e.jsx("div",{className:"bg-white rounded-lg px-4 py-2 max-w-md border border-gray-200",children:"I'd be happy to help you find the right CRM for your remote team! Let me show you some great options that work well for distributed teams."})}),e.jsx("div",{className:"flex justify-start",children:e.jsx("div",{className:"max-w-md",children:e.jsx(d,{recommendations:o,config:{displayMode:"inline",context:"chat",maxRecommendations:2,showPoweredBy:!0,autoShow:!0,delayMs:500},theme:{mode:"light"},sessionId:"demo-session-123",onRecommendationClick:t})})})]})]}),a==="comparison"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Inline Mode"}),e.jsx(d,{recommendations:o,config:{displayMode:"inline",context:"chat",maxRecommendations:2,showPoweredBy:!0},theme:{mode:"light"},onRecommendationClick:t})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Minimal Mode"}),e.jsx(d,{recommendations:o,config:{displayMode:"minimal",context:"assistant",maxRecommendations:2,showPoweredBy:!0},theme:{mode:"light"},onRecommendationClick:t})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Individual Inline Recommendations"}),e.jsx("div",{className:"space-y-2",children:o.slice(0,2).map((r,m)=>e.jsx(f,{recommendation:r,theme:{mode:"light"},compact:!0,showReason:!0,onClick:t},m))})]})]}),s&&e.jsx("div",{className:"mt-6",children:e.jsx(g,{recommendations:o,conversationSummary:"We discussed your need for a CRM solution that works well for remote teams. You mentioned wanting good collaboration features, automation capabilities, and preferably something with a free tier to start. Based on your requirements, I've identified several options that match your criteria, with HubSpot CRM being the top recommendation due to its excellent free tier and collaboration features.",theme:{mode:"light"},showTopRecommendations:3,onRecommendationClick:t,onStartNewConversation:x})})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Integration Examples"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Basic Usage"}),e.jsx("pre",{className:"bg-gray-100 rounded p-3 text-sm overflow-x-auto",children:`<AdMeshConversationalUnit
  recommendations={recommendations}
  config={{
    displayMode: 'inline',
    context: 'chat',
    maxRecommendations: 3
  }}
  onRecommendationClick={(adId, link) => window.open(link)}
/>`})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Conversation Summary"}),e.jsx("pre",{className:"bg-gray-100 rounded p-3 text-sm overflow-x-auto",children:`<AdMeshConversationSummary
  recommendations={recommendations}
  conversationSummary="Your conversation summary..."
  onRecommendationClick={(adId, link) => window.open(link)}
  onStartNewConversation={() => startNewChat()}
/>`})]})]})]})]})},$={title:"Conversational/Showcase",component:y,parameters:{layout:"fullscreen",docs:{description:{component:"Interactive showcase demonstrating all AdMesh conversational ad unit components in realistic usage scenarios. Explore different display modes, chat integration patterns, and conversation summary features."}}}},i={parameters:{docs:{description:{story:"Complete interactive demonstration of AdMesh conversational ad units. Switch between different modes to see how recommendations can be integrated into various conversational contexts."}}}};var h,p,u;i.parameters={...i.parameters,docs:{...(h=i.parameters)==null?void 0:h.docs,source:{originalSource:`{
  parameters: {
    docs: {
      description: {
        story: 'Complete interactive demonstration of AdMesh conversational ad units. Switch between different modes to see how recommendations can be integrated into various conversational contexts.'
      }
    }
  }
}`,...(u=(p=i.parameters)==null?void 0:p.docs)==null?void 0:u.source}}};const B=["InteractiveDemo"];export{i as InteractiveDemo,B as __namedExportsOrder,$ as default};
