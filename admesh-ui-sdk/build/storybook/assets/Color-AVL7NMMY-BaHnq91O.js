import{d as ce,x as M,B as Y,g as he,X as fe,D as de,M as ge,_ as be,a as q}from"./DocsRenderer-PQXLIZUC-CdghzaoF.js";import{r as p,R as v}from"./iframe-CaenRy-o.js";import"./react-18-BT15DNEv.js";import"./jsx-runtime-D_zvdyIk.js";var ve=q({"../../node_modules/color-name/index.js"(r,l){l.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}}),J=q({"../../node_modules/color-convert/conversions.js"(r,l){var s=ve(),h={};for(let e of Object.keys(s))h[s[e]]=e;var u={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};l.exports=u;for(let e of Object.keys(u)){if(!("channels"in u[e]))throw new Error("missing channels property: "+e);if(!("labels"in u[e]))throw new Error("missing channel labels property: "+e);if(u[e].labels.length!==u[e].channels)throw new Error("channel and label counts mismatch: "+e);let{channels:n,labels:t}=u[e];delete u[e].channels,delete u[e].labels,Object.defineProperty(u[e],"channels",{value:n}),Object.defineProperty(u[e],"labels",{value:t})}u.rgb.hsl=function(e){let n=e[0]/255,t=e[1]/255,a=e[2]/255,o=Math.min(n,t,a),i=Math.max(n,t,a),c=i-o,f,g;i===o?f=0:n===i?f=(t-a)/c:t===i?f=2+(a-n)/c:a===i&&(f=4+(n-t)/c),f=Math.min(f*60,360),f<0&&(f+=360);let b=(o+i)/2;return i===o?g=0:b<=.5?g=c/(i+o):g=c/(2-i-o),[f,g*100,b*100]},u.rgb.hsv=function(e){let n,t,a,o,i,c=e[0]/255,f=e[1]/255,g=e[2]/255,b=Math.max(c,f,g),y=b-Math.min(c,f,g),m=function(k){return(b-k)/6/y+1/2};return y===0?(o=0,i=0):(i=y/b,n=m(c),t=m(f),a=m(g),c===b?o=a-t:f===b?o=1/3+n-a:g===b&&(o=2/3+t-n),o<0?o+=1:o>1&&(o-=1)),[o*360,i*100,b*100]},u.rgb.hwb=function(e){let n=e[0],t=e[1],a=e[2],o=u.rgb.hsl(e)[0],i=1/255*Math.min(n,Math.min(t,a));return a=1-1/255*Math.max(n,Math.max(t,a)),[o,i*100,a*100]},u.rgb.cmyk=function(e){let n=e[0]/255,t=e[1]/255,a=e[2]/255,o=Math.min(1-n,1-t,1-a),i=(1-n-o)/(1-o)||0,c=(1-t-o)/(1-o)||0,f=(1-a-o)/(1-o)||0;return[i*100,c*100,f*100,o*100]};function d(e,n){return(e[0]-n[0])**2+(e[1]-n[1])**2+(e[2]-n[2])**2}u.rgb.keyword=function(e){let n=h[e];if(n)return n;let t=1/0,a;for(let o of Object.keys(s)){let i=s[o],c=d(e,i);c<t&&(t=c,a=o)}return a},u.keyword.rgb=function(e){return s[e]},u.rgb.xyz=function(e){let n=e[0]/255,t=e[1]/255,a=e[2]/255;n=n>.04045?((n+.055)/1.055)**2.4:n/12.92,t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,a=a>.04045?((a+.055)/1.055)**2.4:a/12.92;let o=n*.4124+t*.3576+a*.1805,i=n*.2126+t*.7152+a*.0722,c=n*.0193+t*.1192+a*.9505;return[o*100,i*100,c*100]},u.rgb.lab=function(e){let n=u.rgb.xyz(e),t=n[0],a=n[1],o=n[2];t/=95.047,a/=100,o/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,a=a>.008856?a**(1/3):7.787*a+16/116,o=o>.008856?o**(1/3):7.787*o+16/116;let i=116*a-16,c=500*(t-a),f=200*(a-o);return[i,c,f]},u.hsl.rgb=function(e){let n=e[0]/360,t=e[1]/100,a=e[2]/100,o,i,c;if(t===0)return c=a*255,[c,c,c];a<.5?o=a*(1+t):o=a+t-a*t;let f=2*a-o,g=[0,0,0];for(let b=0;b<3;b++)i=n+1/3*-(b-1),i<0&&i++,i>1&&i--,6*i<1?c=f+(o-f)*6*i:2*i<1?c=o:3*i<2?c=f+(o-f)*(2/3-i)*6:c=f,g[b]=c*255;return g},u.hsl.hsv=function(e){let n=e[0],t=e[1]/100,a=e[2]/100,o=t,i=Math.max(a,.01);a*=2,t*=a<=1?a:2-a,o*=i<=1?i:2-i;let c=(a+t)/2,f=a===0?2*o/(i+o):2*t/(a+t);return[n,f*100,c*100]},u.hsv.rgb=function(e){let n=e[0]/60,t=e[1]/100,a=e[2]/100,o=Math.floor(n)%6,i=n-Math.floor(n),c=255*a*(1-t),f=255*a*(1-t*i),g=255*a*(1-t*(1-i));switch(a*=255,o){case 0:return[a,g,c];case 1:return[f,a,c];case 2:return[c,a,g];case 3:return[c,f,a];case 4:return[g,c,a];case 5:return[a,c,f]}},u.hsv.hsl=function(e){let n=e[0],t=e[1]/100,a=e[2]/100,o=Math.max(a,.01),i,c;c=(2-t)*a;let f=(2-t)*o;return i=t*o,i/=f<=1?f:2-f,i=i||0,c/=2,[n,i*100,c*100]},u.hwb.rgb=function(e){let n=e[0]/360,t=e[1]/100,a=e[2]/100,o=t+a,i;o>1&&(t/=o,a/=o);let c=Math.floor(6*n),f=1-a;i=6*n-c,(c&1)!==0&&(i=1-i);let g=t+i*(f-t),b,y,m;switch(c){default:case 6:case 0:b=f,y=g,m=t;break;case 1:b=g,y=f,m=t;break;case 2:b=t,y=f,m=g;break;case 3:b=t,y=g,m=f;break;case 4:b=g,y=t,m=f;break;case 5:b=f,y=t,m=g;break}return[b*255,y*255,m*255]},u.cmyk.rgb=function(e){let n=e[0]/100,t=e[1]/100,a=e[2]/100,o=e[3]/100,i=1-Math.min(1,n*(1-o)+o),c=1-Math.min(1,t*(1-o)+o),f=1-Math.min(1,a*(1-o)+o);return[i*255,c*255,f*255]},u.xyz.rgb=function(e){let n=e[0]/100,t=e[1]/100,a=e[2]/100,o,i,c;return o=n*3.2406+t*-1.5372+a*-.4986,i=n*-.9689+t*1.8758+a*.0415,c=n*.0557+t*-.204+a*1.057,o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92,i=i>.0031308?1.055*i**(1/2.4)-.055:i*12.92,c=c>.0031308?1.055*c**(1/2.4)-.055:c*12.92,o=Math.min(Math.max(0,o),1),i=Math.min(Math.max(0,i),1),c=Math.min(Math.max(0,c),1),[o*255,i*255,c*255]},u.xyz.lab=function(e){let n=e[0],t=e[1],a=e[2];n/=95.047,t/=100,a/=108.883,n=n>.008856?n**(1/3):7.787*n+16/116,t=t>.008856?t**(1/3):7.787*t+16/116,a=a>.008856?a**(1/3):7.787*a+16/116;let o=116*t-16,i=500*(n-t),c=200*(t-a);return[o,i,c]},u.lab.xyz=function(e){let n=e[0],t=e[1],a=e[2],o,i,c;i=(n+16)/116,o=t/500+i,c=i-a/200;let f=i**3,g=o**3,b=c**3;return i=f>.008856?f:(i-16/116)/7.787,o=g>.008856?g:(o-16/116)/7.787,c=b>.008856?b:(c-16/116)/7.787,o*=95.047,i*=100,c*=108.883,[o,i,c]},u.lab.lch=function(e){let n=e[0],t=e[1],a=e[2],o;o=Math.atan2(a,t)*360/2/Math.PI,o<0&&(o+=360);let i=Math.sqrt(t*t+a*a);return[n,i,o]},u.lch.lab=function(e){let n=e[0],t=e[1],a=e[2]/360*2*Math.PI,o=t*Math.cos(a),i=t*Math.sin(a);return[n,o,i]},u.rgb.ansi16=function(e,n=null){let[t,a,o]=e,i=n===null?u.rgb.hsv(e)[2]:n;if(i=Math.round(i/50),i===0)return 30;let c=30+(Math.round(o/255)<<2|Math.round(a/255)<<1|Math.round(t/255));return i===2&&(c+=60),c},u.hsv.ansi16=function(e){return u.rgb.ansi16(u.hsv.rgb(e),e[2])},u.rgb.ansi256=function(e){let n=e[0],t=e[1],a=e[2];return n===t&&t===a?n<8?16:n>248?231:Math.round((n-8)/247*24)+232:16+36*Math.round(n/255*5)+6*Math.round(t/255*5)+Math.round(a/255*5)},u.ansi16.rgb=function(e){let n=e%10;if(n===0||n===7)return e>50&&(n+=3.5),n=n/10.5*255,[n,n,n];let t=(~~(e>50)+1)*.5,a=(n&1)*t*255,o=(n>>1&1)*t*255,i=(n>>2&1)*t*255;return[a,o,i]},u.ansi256.rgb=function(e){if(e>=232){let i=(e-232)*10+8;return[i,i,i]}e-=16;let n,t=Math.floor(e/36)/5*255,a=Math.floor((n=e%36)/6)/5*255,o=n%6/5*255;return[t,a,o]},u.rgb.hex=function(e){let n=(((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255)).toString(16).toUpperCase();return"000000".substring(n.length)+n},u.hex.rgb=function(e){let n=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!n)return[0,0,0];let t=n[0];n[0].length===3&&(t=t.split("").map(f=>f+f).join(""));let a=parseInt(t,16),o=a>>16&255,i=a>>8&255,c=a&255;return[o,i,c]},u.rgb.hcg=function(e){let n=e[0]/255,t=e[1]/255,a=e[2]/255,o=Math.max(Math.max(n,t),a),i=Math.min(Math.min(n,t),a),c=o-i,f,g;return c<1?f=i/(1-c):f=0,c<=0?g=0:o===n?g=(t-a)/c%6:o===t?g=2+(a-n)/c:g=4+(n-t)/c,g/=6,g%=1,[g*360,c*100,f*100]},u.hsl.hcg=function(e){let n=e[1]/100,t=e[2]/100,a=t<.5?2*n*t:2*n*(1-t),o=0;return a<1&&(o=(t-.5*a)/(1-a)),[e[0],a*100,o*100]},u.hsv.hcg=function(e){let n=e[1]/100,t=e[2]/100,a=n*t,o=0;return a<1&&(o=(t-a)/(1-a)),[e[0],a*100,o*100]},u.hcg.rgb=function(e){let n=e[0]/360,t=e[1]/100,a=e[2]/100;if(t===0)return[a*255,a*255,a*255];let o=[0,0,0],i=n%1*6,c=i%1,f=1-c,g=0;switch(Math.floor(i)){case 0:o[0]=1,o[1]=c,o[2]=0;break;case 1:o[0]=f,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=c;break;case 3:o[0]=0,o[1]=f,o[2]=1;break;case 4:o[0]=c,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=f}return g=(1-t)*a,[(t*o[0]+g)*255,(t*o[1]+g)*255,(t*o[2]+g)*255]},u.hcg.hsv=function(e){let n=e[1]/100,t=e[2]/100,a=n+t*(1-n),o=0;return a>0&&(o=n/a),[e[0],o*100,a*100]},u.hcg.hsl=function(e){let n=e[1]/100,t=e[2]/100*(1-n)+.5*n,a=0;return t>0&&t<.5?a=n/(2*t):t>=.5&&t<1&&(a=n/(2*(1-t))),[e[0],a*100,t*100]},u.hcg.hwb=function(e){let n=e[1]/100,t=e[2]/100,a=n+t*(1-n);return[e[0],(a-n)*100,(1-a)*100]},u.hwb.hcg=function(e){let n=e[1]/100,t=1-e[2]/100,a=t-n,o=0;return a<1&&(o=(t-a)/(1-a)),[e[0],a*100,o*100]},u.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},u.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},u.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},u.gray.hsl=function(e){return[0,0,e[0]]},u.gray.hsv=u.gray.hsl,u.gray.hwb=function(e){return[0,100,e[0]]},u.gray.cmyk=function(e){return[0,0,0,e[0]]},u.gray.lab=function(e){return[e[0],0,0]},u.gray.hex=function(e){let n=Math.round(e[0]/100*255)&255,t=((n<<16)+(n<<8)+n).toString(16).toUpperCase();return"000000".substring(t.length)+t},u.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}}}),me=q({"../../node_modules/color-convert/route.js"(r,l){var s=J();function h(){let n={},t=Object.keys(s);for(let a=t.length,o=0;o<a;o++)n[t[o]]={distance:-1,parent:null};return n}function u(n){let t=h(),a=[n];for(t[n].distance=0;a.length;){let o=a.pop(),i=Object.keys(s[o]);for(let c=i.length,f=0;f<c;f++){let g=i[f],b=t[g];b.distance===-1&&(b.distance=t[o].distance+1,b.parent=o,a.unshift(g))}}return t}function d(n,t){return function(a){return t(n(a))}}function e(n,t){let a=[t[n].parent,n],o=s[t[n].parent][n],i=t[n].parent;for(;t[i].parent;)a.unshift(t[i].parent),o=d(s[t[i].parent][i],o),i=t[i].parent;return o.conversion=a,o}l.exports=function(n){let t=u(n),a={},o=Object.keys(t);for(let i=o.length,c=0;c<i;c++){let f=o[c];t[f].parent!==null&&(a[f]=e(f,t))}return a}}}),pe=q({"../../node_modules/color-convert/index.js"(r,l){var s=J(),h=me(),u={},d=Object.keys(s);function e(t){let a=function(...o){let i=o[0];return i==null?i:(i.length>1&&(o=i),t(o))};return"conversion"in t&&(a.conversion=t.conversion),a}function n(t){let a=function(...o){let i=o[0];if(i==null)return i;i.length>1&&(o=i);let c=t(o);if(typeof c=="object")for(let f=c.length,g=0;g<f;g++)c[g]=Math.round(c[g]);return c};return"conversion"in t&&(a.conversion=t.conversion),a}d.forEach(t=>{u[t]={},Object.defineProperty(u[t],"channels",{value:s[t].channels}),Object.defineProperty(u[t],"labels",{value:s[t].labels});let a=h(t);Object.keys(a).forEach(o=>{let i=a[o];u[t][o]=n(i),u[t][o].raw=e(i)})}),l.exports=u}}),_=be(pe());function C(){return(C=Object.assign||function(r){for(var l=1;l<arguments.length;l++){var s=arguments[l];for(var h in s)Object.prototype.hasOwnProperty.call(s,h)&&(r[h]=s[h])}return r}).apply(this,arguments)}function V(r,l){if(r==null)return{};var s,h,u={},d=Object.keys(r);for(h=0;h<d.length;h++)l.indexOf(s=d[h])>=0||(u[s]=r[s]);return u}function L(r){var l=p.useRef(r),s=p.useRef(function(h){l.current&&l.current(h)});return l.current=r,s.current}var O=function(r,l,s){return l===void 0&&(l=0),s===void 0&&(s=1),r>s?s:r<l?l:r},S=function(r){return"touches"in r},B=function(r){return r&&r.ownerDocument.defaultView||self},T=function(r,l,s){var h=r.getBoundingClientRect(),u=S(l)?function(d,e){for(var n=0;n<d.length;n++)if(d[n].identifier===e)return d[n];return d[0]}(l.touches,s):l;return{left:O((u.pageX-(h.left+B(r).pageXOffset))/h.width),top:O((u.pageY-(h.top+B(r).pageYOffset))/h.height)}},W=function(r){!S(r)&&r.preventDefault()},G=v.memo(function(r){var l=r.onMove,s=r.onKey,h=V(r,["onMove","onKey"]),u=p.useRef(null),d=L(l),e=L(s),n=p.useRef(null),t=p.useRef(!1),a=p.useMemo(function(){var f=function(y){W(y),(S(y)?y.touches.length>0:y.buttons>0)&&u.current?d(T(u.current,y,n.current)):b(!1)},g=function(){return b(!1)};function b(y){var m=t.current,k=B(u.current),E=y?k.addEventListener:k.removeEventListener;E(m?"touchmove":"mousemove",f),E(m?"touchend":"mouseup",g)}return[function(y){var m=y.nativeEvent,k=u.current;if(k&&(W(m),!function(x,R){return R&&!S(x)}(m,t.current)&&k)){if(S(m)){t.current=!0;var E=m.changedTouches||[];E.length&&(n.current=E[0].identifier)}k.focus(),d(T(k,m,n.current)),b(!0)}},function(y){var m=y.which||y.keyCode;m<37||m>40||(y.preventDefault(),e({left:m===39?.05:m===37?-.05:0,top:m===40?.05:m===38?-.05:0}))},b]},[e,d]),o=a[0],i=a[1],c=a[2];return p.useEffect(function(){return c},[c]),v.createElement("div",C({},h,{onTouchStart:o,onMouseDown:o,className:"react-colorful__interactive",ref:u,onKeyDown:i,tabIndex:0,role:"slider"}))}),N=function(r){return r.filter(Boolean).join(" ")},F=function(r){var l=r.color,s=r.left,h=r.top,u=h===void 0?.5:h,d=N(["react-colorful__pointer",r.className]);return v.createElement("div",{className:d,style:{top:100*u+"%",left:100*s+"%"}},v.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:l}}))},w=function(r,l,s){return l===void 0&&(l=0),s===void 0&&(s=Math.pow(10,l)),Math.round(s*r)/s},ye={grad:.9,turn:360,rad:360/(2*Math.PI)},xe=function(r){return ee(X(r))},X=function(r){return r[0]==="#"&&(r=r.substring(1)),r.length<6?{r:parseInt(r[0]+r[0],16),g:parseInt(r[1]+r[1],16),b:parseInt(r[2]+r[2],16),a:r.length===4?w(parseInt(r[3]+r[3],16)/255,2):1}:{r:parseInt(r.substring(0,2),16),g:parseInt(r.substring(2,4),16),b:parseInt(r.substring(4,6),16),a:r.length===8?w(parseInt(r.substring(6,8),16)/255,2):1}},we=function(r,l){return l===void 0&&(l="deg"),Number(r)*(ye[l]||1)},ke=function(r){var l=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(r);return l?_e({h:we(l[1],l[2]),s:Number(l[3]),l:Number(l[4]),a:l[5]===void 0?1:Number(l[5])/(l[6]?100:1)}):{h:0,s:0,v:0,a:1}},_e=function(r){var l=r.s,s=r.l;return{h:r.h,s:(l*=(s<50?s:100-s)/100)>0?2*l/(s+l)*100:0,v:s+l,a:r.a}},Ee=function(r){return Ce(Z(r))},Q=function(r){var l=r.s,s=r.v,h=r.a,u=(200-l)*s/100;return{h:w(r.h),s:w(u>0&&u<200?l*s/100/(u<=100?u:200-u)*100:0),l:w(u/2),a:w(h,2)}},D=function(r){var l=Q(r);return"hsl("+l.h+", "+l.s+"%, "+l.l+"%)"},H=function(r){var l=Q(r);return"hsla("+l.h+", "+l.s+"%, "+l.l+"%, "+l.a+")"},Z=function(r){var l=r.h,s=r.s,h=r.v,u=r.a;l=l/360*6,s/=100,h/=100;var d=Math.floor(l),e=h*(1-s),n=h*(1-(l-d)*s),t=h*(1-(1-l+d)*s),a=d%6;return{r:w(255*[h,n,e,e,t,h][a]),g:w(255*[t,h,h,n,e,e][a]),b:w(255*[e,e,t,h,h,n][a]),a:w(u,2)}},Me=function(r){var l=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(r);return l?ee({r:Number(l[1])/(l[2]?100/255:1),g:Number(l[3])/(l[4]?100/255:1),b:Number(l[5])/(l[6]?100/255:1),a:l[7]===void 0?1:Number(l[7])/(l[8]?100:1)}):{h:0,s:0,v:0,a:1}},j=function(r){var l=r.toString(16);return l.length<2?"0"+l:l},Ce=function(r){var l=r.r,s=r.g,h=r.b,u=r.a,d=u<1?j(w(255*u)):"";return"#"+j(l)+j(s)+j(h)+d},ee=function(r){var l=r.r,s=r.g,h=r.b,u=r.a,d=Math.max(l,s,h),e=d-Math.min(l,s,h),n=e?d===l?(s-h)/e:d===s?2+(h-l)/e:4+(l-s)/e:0;return{h:w(60*(n<0?n+6:n)),s:w(d?e/d*100:0),v:w(d/255*100),a:u}},re=v.memo(function(r){var l=r.hue,s=r.onChange,h=N(["react-colorful__hue",r.className]);return v.createElement("div",{className:h},v.createElement(G,{onMove:function(u){s({h:360*u.left})},onKey:function(u){s({h:O(l+360*u.left,0,360)})},"aria-label":"Hue","aria-valuenow":w(l),"aria-valuemax":"360","aria-valuemin":"0"},v.createElement(F,{className:"react-colorful__hue-pointer",left:l/360,color:D({h:l,s:100,v:100,a:1})})))}),te=v.memo(function(r){var l=r.hsva,s=r.onChange,h={backgroundColor:D({h:l.h,s:100,v:100,a:1})};return v.createElement("div",{className:"react-colorful__saturation",style:h},v.createElement(G,{onMove:function(u){s({s:100*u.left,v:100-100*u.top})},onKey:function(u){s({s:O(l.s+100*u.left,0,100),v:O(l.v-100*u.top,0,100)})},"aria-label":"Color","aria-valuetext":"Saturation "+w(l.s)+"%, Brightness "+w(l.v)+"%"},v.createElement(F,{className:"react-colorful__saturation-pointer",top:1-l.v/100,left:l.s/100,color:D(l)})))}),ne=function(r,l){if(r===l)return!0;for(var s in r)if(r[s]!==l[s])return!1;return!0},ae=function(r,l){return r.replace(/\s/g,"")===l.replace(/\s/g,"")},$e=function(r,l){return r.toLowerCase()===l.toLowerCase()||ne(X(r),X(l))};function le(r,l,s){var h=L(s),u=p.useState(function(){return r.toHsva(l)}),d=u[0],e=u[1],n=p.useRef({color:l,hsva:d});p.useEffect(function(){if(!r.equal(l,n.current.color)){var a=r.toHsva(l);n.current={hsva:a,color:l},e(a)}},[l,r]),p.useEffect(function(){var a;ne(d,n.current.hsva)||r.equal(a=r.fromHsva(d),n.current.color)||(n.current={hsva:d,color:a},h(a))},[d,r,h]);var t=p.useCallback(function(a){e(function(o){return Object.assign({},o,a)})},[]);return[d,t]}var Oe=typeof window<"u"?p.useLayoutEffect:p.useEffect,Se=function(){return typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0},A=new Map,oe=function(r){Oe(function(){var l=r.current?r.current.ownerDocument:document;if(l!==void 0&&!A.has(l)){var s=l.createElement("style");s.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,A.set(l,s);var h=Se();h&&s.setAttribute("nonce",h),l.head.appendChild(s)}},[])},Ne=function(r){var l=r.className,s=r.colorModel,h=r.color,u=h===void 0?s.defaultColor:h,d=r.onChange,e=V(r,["className","colorModel","color","onChange"]),n=p.useRef(null);oe(n);var t=le(s,u,d),a=t[0],o=t[1],i=N(["react-colorful",l]);return v.createElement("div",C({},e,{ref:n,className:i}),v.createElement(te,{hsva:a,onChange:o}),v.createElement(re,{hue:a.h,onChange:o,className:"react-colorful__last-control"}))},Re={defaultColor:"000",toHsva:xe,fromHsva:function(r){return Ee({h:r.h,s:r.s,v:r.v,a:1})},equal:$e},je=function(r){return v.createElement(Ne,C({},r,{colorModel:Re}))},ze=function(r){var l=r.className,s=r.hsva,h=r.onChange,u={backgroundImage:"linear-gradient(90deg, "+H(Object.assign({},s,{a:0}))+", "+H(Object.assign({},s,{a:1}))+")"},d=N(["react-colorful__alpha",l]),e=w(100*s.a);return v.createElement("div",{className:d},v.createElement("div",{className:"react-colorful__alpha-gradient",style:u}),v.createElement(G,{onMove:function(n){h({a:n.left})},onKey:function(n){h({a:O(s.a+n.left)})},"aria-label":"Alpha","aria-valuetext":e+"%","aria-valuenow":e,"aria-valuemin":"0","aria-valuemax":"100"},v.createElement(F,{className:"react-colorful__alpha-pointer",left:s.a,color:H(s)})))},ue=function(r){var l=r.className,s=r.colorModel,h=r.color,u=h===void 0?s.defaultColor:h,d=r.onChange,e=V(r,["className","colorModel","color","onChange"]),n=p.useRef(null);oe(n);var t=le(s,u,d),a=t[0],o=t[1],i=N(["react-colorful",l]);return v.createElement("div",C({},e,{ref:n,className:i}),v.createElement(te,{hsva:a,onChange:o}),v.createElement(re,{hue:a.h,onChange:o}),v.createElement(ze,{hsva:a,onChange:o,className:"react-colorful__last-control"}))},He={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:ke,fromHsva:H,equal:ae},Ie=function(r){return v.createElement(ue,C({},r,{colorModel:He}))},qe={defaultColor:"rgba(0, 0, 0, 1)",toHsva:Me,fromHsva:function(r){var l=Z(r);return"rgba("+l.r+", "+l.g+", "+l.b+", "+l.a+")"},equal:ae},Pe=function(r){return v.createElement(ue,C({},r,{colorModel:qe}))},Le=M.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),Be=M(Y)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),Xe=M.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),De=M(fe)(({theme:r})=>({fontFamily:r.typography.fonts.base})),Ke=M.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),Ve=M.div(({theme:r,active:l})=>({width:16,height:16,boxShadow:l?`${r.appBorderColor} 0 0 0 1px inset, ${r.textMutedColor}50 0 0 0 4px`:`${r.appBorderColor} 0 0 0 1px inset`,borderRadius:r.appBorderRadius})),Ge=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,U=({value:r,style:l,...s})=>{let h=`linear-gradient(${r}, ${r}), ${Ge}, linear-gradient(#fff, #fff)`;return v.createElement(Ve,{...s,style:{...l,backgroundImage:h}})},Fe=M(de.Input)(({theme:r,readOnly:l})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:r.typography.fonts.base})),Te=M(ge)(({theme:r})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:r.input.color})),ie=(r=>(r.RGB="rgb",r.HSL="hsl",r.HEX="hex",r))(ie||{}),P=Object.values(ie),We=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,Ae=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,Ue=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,K=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,Ye=/^\s*#?([0-9a-f]{3})\s*$/i,Je={hex:je,rgb:Pe,hsl:Ie},z={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},se=r=>{let l=r==null?void 0:r.match(We);if(!l)return[0,0,0,1];let[,s,h,u,d=1]=l;return[s,h,u,d].map(Number)},Qe=r=>{let[l,s,h,u]=se(r),[d,e,n]=_.default.rgb.hsl([l,s,h])||[0,0,0];return{valid:!0,value:r,keyword:_.default.rgb.keyword([l,s,h]),colorSpace:"rgb",rgb:r,hsl:`hsla(${d}, ${e}%, ${n}%, ${u})`,hex:`#${_.default.rgb.hex([l,s,h]).toLowerCase()}`}},Ze=r=>{let[l,s,h,u]=se(r),[d,e,n]=_.default.hsl.rgb([l,s,h])||[0,0,0];return{valid:!0,value:r,keyword:_.default.hsl.keyword([l,s,h]),colorSpace:"hsl",rgb:`rgba(${d}, ${e}, ${n}, ${u})`,hsl:r,hex:`#${_.default.hsl.hex([l,s,h]).toLowerCase()}`}},er=r=>{let l=r.replace("#",""),s=_.default.keyword.rgb(l)||_.default.hex.rgb(l),h=_.default.rgb.hsl(s),u=r;/[^#a-f0-9]/i.test(r)?u=l:K.test(r)&&(u=`#${l}`);let d=!0;if(u.startsWith("#"))d=K.test(u);else try{_.default.keyword.hex(u)}catch{d=!1}return{valid:d,value:u,keyword:_.default.rgb.keyword(s),colorSpace:"hex",rgb:`rgba(${s[0]}, ${s[1]}, ${s[2]}, 1)`,hsl:`hsla(${h[0]}, ${h[1]}%, ${h[2]}%, 1)`,hex:u}},$=r=>{if(r)return Ae.test(r)?Qe(r):Ue.test(r)?Ze(r):er(r)},rr=(r,l,s)=>{if(!r||!(l!=null&&l.valid))return z[s];if(s!=="hex")return(l==null?void 0:l[s])||z[s];if(!l.hex.startsWith("#"))try{return`#${_.default.keyword.hex(l.hex)}`}catch{return z.hex}let h=l.hex.match(Ye);if(!h)return K.test(l.hex)?l.hex:z.hex;let[u,d,e]=h[1].split("");return`#${u}${u}${d}${d}${e}${e}`},tr=(r,l)=>{let[s,h]=p.useState(r||""),[u,d]=p.useState(()=>$(s)),[e,n]=p.useState((u==null?void 0:u.colorSpace)||"hex");p.useEffect(()=>{let i=r||"",c=$(i);h(i),d(c),n((c==null?void 0:c.colorSpace)||"hex")},[r]);let t=p.useMemo(()=>rr(s,u,e).toLowerCase(),[s,u,e]),a=p.useCallback(i=>{let c=$(i),f=(c==null?void 0:c.value)||i||"";h(f),f===""&&(d(void 0),l(void 0)),c&&(d(c),n(c.colorSpace),l(c.value))},[l]),o=p.useCallback(()=>{let i=(P.indexOf(e)+1)%P.length,c=P[i];n(c);let f=(u==null?void 0:u[c])||"";h(f),l(f)},[u,e,l]);return{value:s,realValue:t,updateValue:a,color:u,colorSpace:e,cycleColorSpace:o}},I=r=>r.replace(/\s*/,"").toLowerCase(),nr=(r,l,s)=>{let[h,u]=p.useState(l!=null&&l.valid?[l]:[]);p.useEffect(()=>{l===void 0&&u([])},[l]);let d=p.useMemo(()=>(r||[]).map(n=>typeof n=="string"?$(n):n.title?{...$(n.color),keyword:n.title}:$(n.color)).concat(h).filter(Boolean).slice(-27),[r,h]),e=p.useCallback(n=>{n!=null&&n.valid&&(d.some(t=>t&&t[s]&&I(t[s]||"")===I(n[s]||""))||u(t=>t.concat(n)))},[s,d]);return{presets:d,addPreset:e}},ar=({name:r,value:l,onChange:s,onFocus:h,onBlur:u,presetColors:d,startOpen:e=!1,argType:n})=>{var E;let t=p.useCallback(ce(s,200),[s]),{value:a,realValue:o,updateValue:i,color:c,colorSpace:f,cycleColorSpace:g}=tr(l,t),{presets:b,addPreset:y}=nr(d??[],c,f),m=Je[f],k=!!((E=n==null?void 0:n.table)!=null&&E.readonly);return v.createElement(Le,{"aria-readonly":k},v.createElement(Be,{startOpen:e,trigger:k?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>c&&y(c),tooltip:v.createElement(Xe,null,v.createElement(m,{color:o==="transparent"?"#000000":o,onChange:i,onFocus:h,onBlur:u}),b.length>0&&v.createElement(Ke,null,b.map((x,R)=>v.createElement(Y,{key:`${(x==null?void 0:x.value)||R}-${R}`,hasChrome:!1,tooltip:v.createElement(De,{note:(x==null?void 0:x.keyword)||(x==null?void 0:x.value)||""})},v.createElement(U,{value:(x==null?void 0:x[f])||"",active:!!(c&&x&&x[f]&&I(x[f]||"")===I(c[f])),onClick:()=>x&&i(x.value||"")})))))},v.createElement(U,{value:o,style:{margin:4}})),v.createElement(Fe,{id:he(r),value:a,onChange:x=>i(x.target.value),onFocus:x=>x.target.select(),readOnly:k,placeholder:"Choose color..."}),a?v.createElement(Te,{onClick:g}):null)},sr=ar;export{ar as ColorControl,sr as default};
