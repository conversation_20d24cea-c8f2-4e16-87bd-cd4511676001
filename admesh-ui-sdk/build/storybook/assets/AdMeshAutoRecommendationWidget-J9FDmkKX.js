import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r}from"./iframe-6V_WvihQ.js";import{c as k}from"./AdMeshLinkTracker-BhGdnnRa.js";import{A as N}from"./AdMeshConversationalUnit-DQbn-Ijw.js";const A=({recommendations:s,trigger:i,theme:t,title:c="AI Recommendations",position:u="bottom-right",size:h="md",autoShow:n=!0,showDelay:o=1e3,onRecommendationClick:p,onDismiss:a,className:g})=>{const[f,l]=r.useState(!1),[d,x]=r.useState(!1);r.useEffect(()=>{if(n&&s.length>0){const j=setTimeout(()=>{l(!0),x(!0)},o);return()=>clearTimeout(j)}},[n,s.length,o]);const m=()=>{l(!1),a==null||a()},y=()=>{switch(h){case"sm":return"w-72 max-h-80";case"md":return"w-80 max-h-96";case"lg":return"w-96 max-h-[28rem]";default:return"w-80 max-h-96"}},b=()=>{switch(u){case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";default:return"bottom-4 right-4"}};if(!f||s.length===0)return null;const v=k("admesh-auto-recommendation-widget","fixed z-50 transition-all duration-500 ease-out",b(),y(),{"opacity-0 scale-95 translate-y-2":!d,"opacity-100 scale-100 translate-y-0":d},g),w=t!=null&&t.accentColor?{"--admesh-primary":t.accentColor}:void 0;return e.jsx("div",{className:v,style:w,"data-admesh-theme":t==null?void 0:t.mode,children:e.jsxs("div",{className:"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm",children:c}),i&&e.jsxs("p",{className:"text-xs text-blue-100 truncate max-w-48",children:['Based on: "',i,'"']})]})]}),e.jsx("button",{onClick:m,className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[s.length," intelligent match",s.length>1?"es":""," found"]})]}),e.jsx(N,{recommendations:s,config:{displayMode:"inline",context:"assistant",maxRecommendations:3,showPoweredBy:!1,autoShow:!0,delayMs:200},theme:t,onRecommendationClick:p})]}),e.jsx("div",{className:"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Powered by AdMesh"}),e.jsx("button",{onClick:m,className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:"Dismiss"})]})})]})})};A.__docgenInfo={description:"",methods:[],displayName:"AdMeshAutoRecommendationWidget",props:{recommendations:{required:!0,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},trigger:{required:!1,tsType:{name:"string"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},title:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:"'AI Recommendations'",computed:!1}},position:{required:!1,tsType:{name:"union",raw:"'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'",elements:[{name:"literal",value:"'bottom-right'"},{name:"literal",value:"'bottom-left'"},{name:"literal",value:"'top-right'"},{name:"literal",value:"'top-left'"}]},description:"",defaultValue:{value:"'bottom-right'",computed:!1}},size:{required:!1,tsType:{name:"union",raw:"'sm' | 'md' | 'lg'",elements:[{name:"literal",value:"'sm'"},{name:"literal",value:"'md'"},{name:"literal",value:"'lg'"}]},description:"",defaultValue:{value:"'md'",computed:!1}},autoShow:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},showDelay:{required:!1,tsType:{name:"number"},description:"",defaultValue:{value:"1000",computed:!1}},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onDismiss:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{A};
