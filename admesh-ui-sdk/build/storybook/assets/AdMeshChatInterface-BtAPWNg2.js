import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as m}from"./iframe-CaenRy-o.js";import{c as u}from"./AdMeshLinkTracker-7Al4Klcr.js";import{A as I}from"./AdMeshConversationalUnit-CQcgcyjm.js";const v=({message:t,theme:s,onRecommendationClick:n,className:i})=>{const r=t.role==="user",h=t.role==="assistant",o=u("admesh-chat-message","flex items-start gap-3",{"flex-row-reverse":r},i),d=u("max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm",{"bg-gradient-to-r from-blue-600 to-indigo-600 text-white":r,"bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100":h,"bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100":t.role==="system"}),x=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0,c=g=>g.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return e.jsxs("div",{className:o,style:x,"data-admesh-theme":s==null?void 0:s.mode,children:[!r&&e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),r&&e.jsx("div",{className:"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),e.jsxs("div",{className:`flex flex-col ${r?"items-end":"items-start"} flex-1`,children:[e.jsx("div",{className:d,children:e.jsx("div",{className:"whitespace-pre-wrap break-words",children:t.content})}),e.jsx("div",{className:u("text-xs text-gray-500 dark:text-gray-400 mt-1",{"text-right":r}),children:c(t.timestamp)}),t.recommendations&&t.recommendations.length>0&&e.jsxs("div",{className:"mt-3 w-full max-w-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("svg",{className:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[t.recommendations.length," recommendation",t.recommendations.length>1?"s":""," found"]})]}),e.jsx(I,{recommendations:t.recommendations,config:{displayMode:"inline",context:"chat",maxRecommendations:3,showPoweredBy:!1,autoShow:!0,delayMs:300},theme:s,onRecommendationClick:n,className:"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700"})]})]})]})};v.__docgenInfo={description:"",methods:[],displayName:"AdMeshChatMessage",props:{message:{required:!0,tsType:{name:"ChatMessage"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};const w=({placeholder:t="Type your message...",disabled:s=!1,suggestions:n=[],theme:i,onSendMessage:r,className:h})=>{const[o,d]=m.useState(""),[x,c]=m.useState(!1),[g,f]=m.useState([]),a=m.useRef(null),y=l=>{const p=l.target.value;if(d(p),p.trim()&&n.length>0){const k=n.filter(A=>A.toLowerCase().includes(p.toLowerCase()));f(k),c(k.length>0)}else c(!1);a.current&&(a.current.style.height="auto",a.current.style.height=`${Math.min(a.current.scrollHeight,120)}px`)},j=l=>{l.key==="Enter"&&!l.shiftKey&&(l.preventDefault(),b())},b=()=>{const l=o.trim();l&&!s&&r&&(r(l),d(""),c(!1),a.current&&(a.current.style.height="auto"))},N=l=>{d(l),c(!1),a.current&&a.current.focus()},C=u("admesh-chat-input","relative",h),M=u("w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600","bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100","placeholder-gray-500 dark:placeholder-gray-400","focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent","transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600","pr-12 pl-4 py-3 text-sm leading-5",{"opacity-50 cursor-not-allowed":s}),T=u("absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200","flex items-center justify-center",{"bg-blue-600 hover:bg-blue-700 text-white":o.trim()&&!s,"bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed":!o.trim()||s}),L=i!=null&&i.accentColor?{"--admesh-primary":i.accentColor}:void 0;return e.jsxs("div",{className:C,style:L,"data-admesh-theme":i==null?void 0:i.mode,children:[x&&g.length>0&&e.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10",children:g.slice(0,5).map((l,p)=>e.jsx("button",{onClick:()=>N(l),className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg",children:l},p))}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{ref:a,value:o,onChange:y,onKeyDown:j,placeholder:t,disabled:s,rows:1,className:M,style:{minHeight:"44px",maxHeight:"120px"}}),e.jsx("button",{onClick:b,disabled:!o.trim()||s,className:T,"aria-label":"Send message",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})]}),e.jsxs("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{children:"Press Enter to send, Shift+Enter for new line"}),e.jsxs("span",{className:u("transition-opacity duration-200",{"opacity-0":o.length<100}),children:[o.length,"/500"]})]})]})};w.__docgenInfo={description:"",methods:[],displayName:"AdMeshChatInput",props:{placeholder:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:'"Type your message..."',computed:!1}},disabled:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},suggestions:{required:!1,tsType:{name:"Array",elements:[{name:"string"}],raw:"string[]"},description:"",defaultValue:{value:"[]",computed:!1}},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},onSendMessage:{required:!1,tsType:{name:"signature",type:"function",raw:"(message: string) => void",signature:{arguments:[{type:{name:"string"},name:"message"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};const q=({messages:t,config:s,theme:n,isLoading:i=!1,onSendMessage:r,onRecommendationClick:h,className:o})=>{const d=m.useRef(null),x=m.useRef(null);m.useEffect(()=>{d.current&&d.current.scrollIntoView({behavior:"smooth"})},[t]);const c=u("admesh-chat-interface","flex flex-col h-full bg-white dark:bg-slate-900",o),g=n!=null&&n.accentColor?{"--admesh-primary":n.accentColor}:void 0,f=s.maxMessages?t.slice(-s.maxMessages):t;return e.jsxs("div",{className:c,style:g,"data-admesh-theme":n==null?void 0:n.mode,children:[e.jsx("div",{ref:x,className:"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",children:f.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"Welcome to AdMesh AI"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 max-w-xs",children:"Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!"})]}):e.jsxs(e.Fragment,{children:[f.map(a=>e.jsx(v,{message:a,theme:n,onRecommendationClick:h},a.id)),i&&s.enableTypingIndicator!==!1&&e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("div",{className:"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs",children:e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),e.jsx("div",{ref:d})]})}),s.enableSuggestions&&s.suggestions&&s.suggestions.length>0&&t.length===0&&e.jsxs("div",{className:"px-4 pb-2",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"Quick suggestions:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.suggestions.slice(0,3).map((a,y)=>e.jsx("button",{onClick:()=>r==null?void 0:r(a),className:"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors",children:a},y))})]}),s.showInputField!==!1&&r&&e.jsx("div",{className:"border-t border-gray-200 dark:border-slate-700 p-4",children:e.jsx(w,{placeholder:s.placeholder||"Ask me about products, tools, or services...",disabled:i,suggestions:s.suggestions,theme:n,onSendMessage:r})})]})};q.__docgenInfo={description:"",methods:[],displayName:"AdMeshChatInterface",props:{messages:{required:!0,tsType:{name:"Array",elements:[{name:"ChatMessage"}],raw:"ChatMessage[]"},description:""},config:{required:!0,tsType:{name:"intersection",raw:`Partial<AdMeshChatConfig> & {
  showInputField?: boolean;
}`,elements:[{name:"Partial",elements:[{name:"AdMeshChatConfig"}],raw:"Partial<AdMeshChatConfig>"},{name:"signature",type:"object",raw:`{
  showInputField?: boolean;
}`,signature:{properties:[{key:"showInputField",value:{name:"boolean",required:!1}}]}}]},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},isLoading:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},onSendMessage:{required:!1,tsType:{name:"signature",type:"function",raw:"(message: string) => void",signature:{arguments:[{type:{name:"string"},name:"message"}],return:{name:"void"}}},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{q as A};
