import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as h}from"./iframe-CaenRy-o.js";import{c as p}from"./AdMeshLinkTracker-7Al4Klcr.js";import{A as M}from"./AdMeshInlineRecommendation-B1kKoBBP.js";import{A}from"./AdMeshProductCard-DUNDRGIQ.js";const T=({title:y,theme:r,collapsible:t=!1,isCollapsed:x=!1,onToggle:c,onSearch:n,showSearch:w=!1,className:f})=>{const[m,a]=h.useState(""),[b,v]=h.useState(!1),l=s=>{const o=s.target.value;a(o),n==null||n(o)},u=()=>{a(""),n==null||n("")},k=p("admesh-sidebar-header","flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",f),j=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor}:void 0;return e.jsxs("div",{className:k,style:j,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate",children:y}),t&&e.jsx("button",{onClick:c,className:"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0",title:x?"Expand sidebar":"Collapse sidebar",children:e.jsx("svg",{className:p("w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200",{"rotate-180":x}),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})})]}),w&&!x&&e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:p("relative flex items-center transition-all duration-200",{"ring-2 ring-blue-500 dark:ring-blue-400":b}),children:[e.jsx("div",{className:"absolute left-3 pointer-events-none",children:e.jsx("svg",{className:"w-4 h-4 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("input",{type:"text",value:m,onChange:l,onFocus:()=>v(!0),onBlur:()=>v(!1),placeholder:"Search recommendations...",className:p("w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg","placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100","focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent","transition-all duration-200")}),m&&e.jsx("button",{onClick:u,className:"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors",title:"Clear search",children:e.jsx("svg",{className:"w-3 h-3 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),m&&e.jsx("div",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:"Search results will be filtered in real-time"})]}),!x&&e.jsxs("div",{className:"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),e.jsx("span",{children:"Live recommendations"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),e.jsx("span",{children:"AI-powered"})]})]})]})};T.__docgenInfo={description:"",methods:[],displayName:"AdMeshSidebarHeader",props:{title:{required:!0,tsType:{name:"string"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},collapsible:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},isCollapsed:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},onToggle:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},onSearch:{required:!1,tsType:{name:"signature",type:"function",raw:"(query: string) => void",signature:{arguments:[{type:{name:"string"},name:"query"}],return:{name:"void"}}},description:""},showSearch:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},className:{required:!1,tsType:{name:"string"},description:""}}};const q=({recommendations:y,displayMode:r,theme:t,maxRecommendations:x,onRecommendationClick:c,className:n})=>{const[w,f]=h.useState(!1),[m,a]=h.useState("all"),b=x?y.slice(0,x):y,l=(()=>{switch(m){case"top":return b.filter(s=>s.intent_match_score>=.8).slice(0,5);case"recent":return b.slice(0,3);default:return b}})(),u=p("admesh-sidebar-content","flex flex-col h-full",n),k=t!=null&&t.accentColor?{"--admesh-primary":t.accentColor}:void 0,j=()=>{if(l.length===0)return e.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-gray-400 dark:text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No recommendations found"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Try adjusting your search or filters"})]});switch(r){case"recommendations":return e.jsx("div",{className:"space-y-3",children:l.map((s,o)=>e.jsx(M,{recommendation:s,theme:t,compact:!0,showReason:!0,onClick:c},s.ad_id||o))});case"history":return e.jsx("div",{className:"space-y-2",children:l.map((s,o)=>e.jsxs("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:s.title}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[Math.round(s.intent_match_score*100),"% match"]})]})]},s.ad_id||o))});case"favorites":return e.jsx("div",{className:"space-y-3",children:l.slice(0,3).map((s,o)=>e.jsxs("div",{className:"relative",children:[e.jsx(M,{recommendation:s,theme:t,compact:!0,showReason:!1,onClick:c}),e.jsx("button",{className:"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",children:e.jsx("svg",{className:"w-3 h-3 text-yellow-500",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})})]},s.ad_id||o))});case"mixed":return e.jsxs("div",{className:"space-y-4",children:[l[0]&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",children:"Top Pick"}),e.jsx(A,{recommendation:l[0],theme:t,showMatchScore:!0,showBadges:!0,onClick:c,className:"text-xs"})]}),l.slice(1).length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",children:"More Options"}),e.jsx("div",{className:"space-y-2",children:l.slice(1,4).map((s,o)=>e.jsx(M,{recommendation:s,theme:t,compact:!0,showReason:!1,onClick:c},s.ad_id||o))})]})]});default:return e.jsx("div",{className:"space-y-3",children:l.map((s,o)=>e.jsx(M,{recommendation:s,theme:t,compact:!0,showReason:!0,onClick:c},s.ad_id||o))})}};return e.jsxs("div",{className:u,style:k,"data-admesh-theme":t==null?void 0:t.mode,children:[e.jsxs("div",{className:"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900",children:[e.jsxs("button",{onClick:()=>a("all"),className:p("flex-1 px-3 py-2 text-xs font-medium transition-colors",m==="all"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:["All (",y.length,")"]}),e.jsx("button",{onClick:()=>a("top"),className:p("flex-1 px-3 py-2 text-xs font-medium transition-colors",m==="top"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:"Top"}),e.jsx("button",{onClick:()=>a("recent"),className:p("flex-1 px-3 py-2 text-xs font-medium transition-colors",m==="recent"?"text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),children:"Recent"})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:j()}),e.jsx("div",{className:"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",children:e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsxs("span",{className:"text-gray-500 dark:text-gray-400",children:[l.length," recommendation",l.length!==1?"s":""]}),e.jsx("button",{onClick:()=>f(!w),className:"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors",children:"Filters"})]})})]})};q.__docgenInfo={description:"",methods:[],displayName:"AdMeshSidebarContent",props:{recommendations:{required:!0,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},displayMode:{required:!0,tsType:{name:"union",raw:"'recommendations' | 'history' | 'favorites' | 'mixed'",elements:[{name:"literal",value:"'recommendations'"},{name:"literal",value:"'history'"},{name:"literal",value:"'favorites'"},{name:"literal",value:"'mixed'"}]},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},maxRecommendations:{required:!1,tsType:{name:"number"},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};const _=({recommendations:y,config:r,theme:t,title:x="Recommendations",isOpen:c=!0,onToggle:n,onRecommendationClick:w,onSearch:f,className:m})=>{const[a,b]=h.useState(r.defaultCollapsed||!1),[v,l]=h.useState(""),[u]=h.useState({});h.useEffect(()=>{if(r.autoRefresh&&r.refreshInterval){const i=setInterval(()=>{console.log("Auto-refreshing recommendations...")},r.refreshInterval);return()=>clearInterval(i)}},[r.autoRefresh,r.refreshInterval]);const k=h.useMemo(()=>{let i=[...y];if(v.trim()){const d=v.toLowerCase();i=i.filter(g=>{var N;return g.title.toLowerCase().includes(d)||g.reason.toLowerCase().includes(d)||((N=g.keywords)==null?void 0:N.some(C=>C.toLowerCase().includes(d)))})}return u.categories&&u.categories.length>0&&(i=i.filter(d=>{var g;return(g=d.categories)==null?void 0:g.some(N=>{var C;return(C=u.categories)==null?void 0:C.includes(N)})})),u.hasFreeTier&&(i=i.filter(d=>d.has_free_tier)),u.hasTrial&&(i=i.filter(d=>d.trial_days&&d.trial_days>0)),u.minMatchScore!==void 0&&(i=i.filter(d=>d.intent_match_score>=u.minMatchScore)),i.sort((d,g)=>g.intent_match_score-d.intent_match_score),r.maxRecommendations&&(i=i.slice(0,r.maxRecommendations)),i},[y,v,u,r.maxRecommendations]),j=()=>{r.collapsible&&(b(!a),n==null||n())},s=i=>{l(i),f==null||f(i)},S=p("admesh-sidebar","flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out",(()=>{switch(r.size){case"sm":return a?"w-12":"w-64";case"md":return a?"w-12":"w-80";case"lg":return a?"w-12":"w-96";case"xl":return a?"w-12":"w-[28rem]";default:return a?"w-12":"w-80"}})(),{"border-r":r.position==="left","border-l":r.position==="right","fixed top-0 bottom-0 z-50":!0,"left-0":r.position==="left","right-0":r.position==="right","transform -translate-x-full":r.position==="left"&&!c,"transform translate-x-full":r.position==="right"&&!c},m),L=t!=null&&t.accentColor?{"--admesh-primary":t.accentColor}:void 0;return!c&&!r.collapsible?null:e.jsxs(e.Fragment,{children:[c&&!a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>n==null?void 0:n()}),e.jsxs("div",{className:S,style:L,"data-admesh-theme":t==null?void 0:t.mode,"data-sidebar-position":r.position,"data-sidebar-size":r.size,children:[r.showHeader!==!1&&e.jsx(T,{title:x,theme:t,collapsible:r.collapsible,isCollapsed:a,onToggle:j,onSearch:r.showSearch?s:void 0,showSearch:r.showSearch&&!a}),!a&&e.jsx(q,{recommendations:k,displayMode:r.displayMode,theme:t,maxRecommendations:r.maxRecommendations,onRecommendationClick:w,className:"flex-1 overflow-hidden"}),a&&r.collapsible&&e.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center p-2",children:[e.jsx("button",{onClick:j,className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors",title:"Expand sidebar",children:e.jsx("svg",{className:"w-5 h-5 text-gray-600 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),e.jsx("div",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap",children:k.length})]}),!a&&e.jsx("div",{className:"p-3 border-t border-gray-200 dark:border-slate-700",children:e.jsx("div",{className:"text-xs text-gray-400 dark:text-gray-500 text-center",children:"Powered by AdMesh"})})]})]})};_.__docgenInfo={description:"",methods:[],displayName:"AdMeshSidebar",props:{recommendations:{required:!0,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},config:{required:!0,tsType:{name:"AdMeshSidebarConfig"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},title:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:"'Recommendations'",computed:!1}},isOpen:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},onToggle:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onSearch:{required:!1,tsType:{name:"signature",type:"function",raw:"(query: string) => void",signature:{arguments:[{type:{name:"string"},name:"query"}],return:{name:"void"}}},description:""},onFilter:{required:!1,tsType:{name:"signature",type:"function",raw:"(filters: SidebarFilters) => void",signature:{arguments:[{type:{name:"SidebarFilters"},name:"filters"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{_ as A};
