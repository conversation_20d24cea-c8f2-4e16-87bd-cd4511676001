import{j as t}from"./jsx-runtime-D_zvdyIk.js";import{r as b}from"./iframe-DBb7zPuL.js";import{c as y}from"./AdMeshLinkTracker-Cu4CWCJq.js";import{A as k}from"./AdMeshCitationReference-CWPhK5Z7.js";import{A as R}from"./AdMeshInlineRecommendation-g5CIQFV8.js";const $=({recommendations:d,conversationText:p,theme:s,showCitationList:N=!0,citationStyle:x="numbered",onRecommendationClick:u,onCitationHover:g,className:T})=>{const[i,_]=b.useState(null),w=b.useMemo(()=>{if(!p||d.length===0)return{text:p,citationMap:new Map};let e=p;const r=new Map;return[...d].sort((a,c)=>c.intent_match_score-a.intent_match_score).forEach((a,c)=>{const n=c+1,l=a.title;r.set(n,a);const m=new RegExp(`\\b${l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"gi");if(m.test(e))e=e.replace(m,o=>`${o}{{CITATION_${n}}}`);else{const o=a.keywords||[];let f=!1;for(const I of o){const h=new RegExp(`\\b${I.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}\\b`,"gi");if(h.test(e)&&!f){e=e.replace(h,C=>(f=!0,`${C}{{CITATION_${n}}}`));break}}f||(e+=`{{CITATION_${n}}}`)}}),{text:e,citationMap:r}},[p,d]),j=()=>{const{text:e,citationMap:r}=w;return e.split(/(\{\{CITATION_\d+\}\})/).map((a,c)=>{const n=a.match(/\{\{CITATION_(\d+)\}\}/);if(n){const l=parseInt(n[1]),m=r.get(l);if(m)return t.jsx(k,{recommendation:m,citationNumber:l,citationStyle:x,theme:s,showTooltip:!0,onClick:u,onHover:o=>{_(o),g==null||g(o)}},`citation-${l}`)}return t.jsx("span",{children:a},c)})},A=y("admesh-citation-unit","space-y-4",T),M=s!=null&&s.accentColor?{"--admesh-primary":s.accentColor}:void 0;return t.jsxs("div",{className:A,style:M,"data-admesh-theme":s==null?void 0:s.mode,children:[t.jsx("div",{className:"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed",children:j()}),N&&d.length>0&&t.jsx("div",{className:"admesh-citation-list",children:t.jsxs("div",{className:"border-t border-gray-200 dark:border-slate-700 pt-4",children:[t.jsxs("h4",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",children:[t.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})}),"References"]}),t.jsx("div",{className:"space-y-2",children:d.sort((e,r)=>r.intent_match_score-e.intent_match_score).map((e,r)=>t.jsxs("div",{className:y("flex items-start gap-3 p-2 rounded-lg transition-colors duration-200",{"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800":(i==null?void 0:i.ad_id)===e.ad_id,"hover:bg-gray-50 dark:hover:bg-slate-800/50":(i==null?void 0:i.ad_id)!==e.ad_id}),children:[t.jsx("div",{className:"flex-shrink-0 mt-1",children:t.jsx(k,{recommendation:e,citationNumber:r+1,citationStyle:x,theme:s,showTooltip:!1,onClick:u})}),t.jsx("div",{className:"flex-1 min-w-0",children:t.jsx(R,{recommendation:e,theme:s,compact:!0,showReason:!1,onClick:u})})]},e.ad_id||r))})]})})]})};$.__docgenInfo={description:"",methods:[],displayName:"AdMeshCitationUnit",props:{recommendations:{required:!0,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},conversationText:{required:!0,tsType:{name:"string"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},showCitationList:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},citationStyle:{required:!1,tsType:{name:"union",raw:"'numbered' | 'bracketed' | 'superscript'",elements:[{name:"literal",value:"'numbered'"},{name:"literal",value:"'bracketed'"},{name:"literal",value:"'superscript'"}]},description:"",defaultValue:{value:"'numbered'",computed:!1}},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onCitationHover:{required:!1,tsType:{name:"signature",type:"function",raw:"(recommendation: AdMeshRecommendation) => void",signature:{arguments:[{type:{name:"AdMeshRecommendation"},name:"recommendation"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{$ as A};
