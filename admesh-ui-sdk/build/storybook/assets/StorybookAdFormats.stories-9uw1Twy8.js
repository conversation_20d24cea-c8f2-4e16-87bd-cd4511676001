import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as C}from"./iframe-6V_WvihQ.js";import"./AdMeshProductCard-BhpBY9Ff.js";import"./AdMeshLayout-DBgV3k-C.js";import"./AdMeshBadge-B-r0fatN.js";import"./AdMeshLinkTracker-BhGdnnRa.js";import"./AdMeshConversationalUnit-DQbn-Ijw.js";import"./AdMeshConversationSummary-wBmTX9d3.js";import"./AdMeshInlineRecommendation-iUkU6sFQ.js";import{A}from"./AdMeshCitationUnit-BfnoZkX0.js";import"./AdMeshCitationReference-Dm80h120.js";import"./AdMeshSidebar-DZv8zzrp.js";import"./AdMeshFloatingChat-BHkgq8np.js";import"./AdMeshChatInterface-DhjuPLqh.js";import"./AdMeshAutoRecommendationWidget-J9FDmkKX.js";const s=[{title:"HubSpot CRM",reason:"Perfect for growing businesses with excellent free tier and automation features",intent_match_score:.94,admesh_link:"https://useadmesh.com/track?ad_id=hubspot-crm&story=startup-journey",ad_id:"hubspot-crm",product_id:"hubspot",has_free_tier:!0,trial_days:14,keywords:["CRM","Sales","Free"],categories:["Sales Tools"],features:["Contact Management","Deal Pipeline","Email Integration"],pricing:"Free tier available"},{title:"Pipedrive",reason:"Visual sales pipeline management that's perfect for tracking deals and opportunities",intent_match_score:.89,admesh_link:"https://useadmesh.com/track?ad_id=pipedrive&story=startup-journey",ad_id:"pipedrive",product_id:"pipedrive",has_free_tier:!1,trial_days:14,keywords:["CRM","Sales Pipeline","Visual"],categories:["Sales Tools"],features:["Visual Pipeline","Deal Tracking","Sales Reporting"],pricing:"Starting at $14.90/user/month"},{title:"Notion",reason:"All-in-one workspace perfect for project management and team collaboration",intent_match_score:.91,admesh_link:"https://useadmesh.com/track?ad_id=notion&story=startup-journey",ad_id:"notion",product_id:"notion",has_free_tier:!0,trial_days:0,keywords:["Productivity","Project Management","Collaboration"],categories:["Productivity Tools"],features:["Database","Wiki","Project Management","AI Assistant"],pricing:"Free for personal use"},{title:"Intercom",reason:"Customer support and messaging platform that scales with your business",intent_match_score:.87,admesh_link:"https://useadmesh.com/track?ad_id=intercom&story=startup-journey",ad_id:"intercom",product_id:"intercom",has_free_tier:!1,trial_days:14,keywords:["Customer Support","Live Chat","Messaging"],categories:["Customer Support"],features:["Live Chat","Help Desk","Customer Messaging","Automation"],pricing:"Starting at $39/month"}],D=`Sarah was a brilliant engineer who decided to start her own SaaS company. As her customer base grew from 10 to 1,000 users, she realized she needed better tools to manage customer relationships¹ and track her sales pipeline².

She also struggled with project management³ as her team expanded from 2 to 15 people, and needed a reliable way to handle the increasing volume of customer support tickets⁴. The spreadsheets and email chains that worked in the early days were no longer sufficient.

After researching various solutions and talking to other founders, Sarah found the perfect combination of tools that helped her scale efficiently while maintaining the personal touch that made her customers love the product.`,R=`Alex was a senior developer who joined a fast-growing startup. The team was using a mix of different tools that didn't integrate well together, causing friction in their development process.

They needed a better code repository hosting solution, a more efficient continuous integration pipeline, and a way to track bugs and feature requests that didn't get lost in Slack messages.

Alex also wanted to implement better monitoring and error tracking for their production applications, and needed a reliable way to manage their cloud infrastructure as the team grew.`,T=({story:r,recommendations:t,title:o,category:l})=>{const[p,M]=C.useState([]),F=(c,h)=>{var u;M(m=>[...m,c]),console.log("📚 Storybook Ad Clicked:",{adId:c,admeshLink:h,storyTitle:o,category:l,timestamp:new Date().toISOString()}),alert(`📚 Storybook Ad Clicked!

Product: ${(u=t.find(m=>m.ad_id===c))==null?void 0:u.title}
Story: ${o}
Category: ${l}

Link: ${h}`)};return e.jsxs("div",{className:"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"mb-6 border-b border-gray-200 pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:o}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800",children:l}),e.jsx("span",{className:"text-sm text-gray-500",children:"📚 Storybook Ad Format"})]})]}),e.jsx("div",{className:"prose prose-lg max-w-none mb-6",children:e.jsx(A,{recommendations:t,conversationText:r,citationStyle:"numbered",showCitationList:!0,onRecommendationClick:F,theme:{mode:"light"}})}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"📚 Contextual recommendations powered by"}),e.jsx("strong",{className:"text-purple-600",children:"AdMesh"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("span",{children:[t.length," relevant tools found"]}),p.length>0&&e.jsxs("span",{className:"text-green-600",children:[p.length," clicked"]})]})]}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-blue-900 mb-2",children:"🎭 Storybook Ad Format Demo"}),e.jsx("p",{className:"text-blue-800 text-sm",children:"This demonstrates AdMesh's revolutionary storybook advertising format. Notice how recommendations appear as academic-style citations within the narrative, enhancing the story rather than interrupting it. Click on any numbered citation to see the tracking in action!"})]})]})},P=()=>e.jsxs("div",{className:"max-w-6xl mx-auto p-6 space-y-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"📊 Ad Format Comparison"}),e.jsx("p",{className:"text-lg text-gray-600",children:"See how AdMesh's storybook format compares to traditional advertising"})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-red-600 mb-4",children:"❌ Traditional Push Ads (Intrusive)"}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsx("p",{className:"text-gray-800 mb-4",children:"Sarah was a brilliant engineer who decided to start her own SaaS company..."}),e.jsx("div",{className:"bg-red-100 border-2 border-red-300 p-4 rounded-lg mb-4 animate-pulse",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h4",{className:"text-red-800 font-bold text-lg",children:"🚨 URGENT! CRM SOFTWARE SALE! 🚨"}),e.jsx("p",{className:"text-red-700",children:"⚡ 50% OFF TODAY ONLY! ⚡"}),e.jsx("button",{className:"bg-red-600 text-white px-4 py-2 rounded font-bold mt-2",children:"👆 CLICK NOW OR MISS OUT! 👆"})]})}),e.jsx("p",{className:"text-gray-800",children:"...but she quickly realized managing customers was harder than expected..."})]}),e.jsxs("div",{className:"bg-red-50 p-3 rounded text-sm",children:[e.jsx("strong",{className:"text-red-800",children:"Problems:"}),e.jsxs("ul",{className:"text-red-700 mt-1 space-y-1",children:[e.jsx("li",{children:"• Interrupts reading flow"}),e.jsx("li",{children:"• Often irrelevant to content"}),e.jsx("li",{children:"• Creates user frustration"}),e.jsx("li",{children:"• Low engagement rates"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-xl font-semibold text-green-600 mb-4",children:"✅ AdMesh Storybook Format (Contextual)"}),e.jsx("div",{className:"bg-green-50 p-4 rounded-lg",children:e.jsx(A,{recommendations:s.slice(0,2),conversationText:"Sarah was a brilliant engineer who decided to start her own SaaS company. As her customer base grew, she realized she needed better tools to manage customer relationships¹ and track her sales pipeline².",citationStyle:"numbered",showCitationList:!0,onRecommendationClick:(r,t)=>{console.log("Citation clicked:",r,"Link:",t)},theme:{mode:"light"}})}),e.jsxs("div",{className:"bg-green-50 p-3 rounded text-sm",children:[e.jsx("strong",{className:"text-green-800",children:"Benefits:"}),e.jsxs("ul",{className:"text-green-700 mt-1 space-y-1",children:[e.jsx("li",{children:"• Enhances story content"}),e.jsx("li",{children:"• Contextually relevant"}),e.jsx("li",{children:"• Academic-style citations"}),e.jsx("li",{children:"• High engagement rates"})]})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"📈 Performance Comparison"}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:"0.05%"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Traditional CTR"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:"8-12%"}),e.jsx("div",{className:"text-sm text-gray-600",children:"AdMesh Engagement"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600",children:"300%"}),e.jsx("div",{className:"text-sm text-gray-600",children:"ROI Improvement"})]})]})]})]}),K={title:"AdMesh/Storybook Ad Formats",component:T,parameters:{layout:"fullscreen",docs:{description:{component:"Showcase of AdMesh's revolutionary storybook-style advertising format that integrates recommendations naturally into narratives, similar to academic citations or footnotes."}}},tags:["autodocs"]},a={args:{story:D,recommendations:s,title:"The Startup Founder's Journey",category:"Business Growth"},parameters:{docs:{description:{story:"A business growth story showing how recommendations appear as academic-style citations within the narrative. Click on the numbered citations to see the tracking in action."}}}},i={args:{story:R,recommendations:[{title:"GitHub",reason:"Industry-standard code repository hosting with excellent collaboration features",intent_match_score:.95,admesh_link:"https://useadmesh.com/track?ad_id=github&story=developer-workflow",ad_id:"github",product_id:"github",has_free_tier:!0,trial_days:0,keywords:["Git","Code Repository","Collaboration"],categories:["Development Tools"],features:["Version Control","Pull Requests","Actions","Issues"],pricing:"Free for public repositories"},{title:"Sentry",reason:"Real-time error tracking and performance monitoring for production applications",intent_match_score:.88,admesh_link:"https://useadmesh.com/track?ad_id=sentry&story=developer-workflow",ad_id:"sentry",product_id:"sentry",has_free_tier:!0,trial_days:14,keywords:["Error Tracking","Monitoring","Performance"],categories:["Development Tools"],features:["Error Tracking","Performance Monitoring","Release Health"],pricing:"Free tier available"}],title:"Building the Perfect Development Workflow",category:"Software Development"},parameters:{docs:{description:{story:"A developer-focused story demonstrating how technical tool recommendations can be naturally integrated into professional narratives."}}}},n={render:()=>e.jsx(P,{}),parameters:{docs:{description:{story:"Side-by-side comparison showing the difference between traditional intrusive advertising and AdMesh's contextual storybook format."}}}},d={render:()=>{const[r,t]=C.useState("startup"),o={startup:{story:D,recommendations:s,title:"The Startup Founder's Journey",category:"Business Growth"},developer:{story:R,recommendations:[s[0],s[1]],title:"Building the Perfect Development Workflow",category:"Software Development"}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"🎭 Interactive Storybook Ad Demo"}),e.jsxs("div",{className:"flex justify-center gap-4 mb-6",children:[e.jsx("button",{onClick:()=>t("startup"),className:`px-4 py-2 rounded-lg font-medium ${r==="startup"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"📈 Startup Story"}),e.jsx("button",{onClick:()=>t("developer"),className:`px-4 py-2 rounded-lg font-medium ${r==="developer"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"💻 Developer Story"})]})]}),e.jsx(T,{...o[r]})]})},parameters:{docs:{description:{story:"Interactive demo allowing you to switch between different story types and see how AdMesh recommendations adapt to different contexts and audiences."}}}};var g,y,x;a.parameters={...a.parameters,docs:{...(g=a.parameters)==null?void 0:g.docs,source:{originalSource:`{
  args: {
    story: startupJourneyStory,
    recommendations: businessStoryRecommendations,
    title: "The Startup Founder's Journey",
    category: "Business Growth"
  },
  parameters: {
    docs: {
      description: {
        story: 'A business growth story showing how recommendations appear as academic-style citations within the narrative. Click on the numbered citations to see the tracking in action.'
      }
    }
  }
}`,...(x=(y=a.parameters)==null?void 0:y.docs)==null?void 0:x.source}}};var b,f,v;i.parameters={...i.parameters,docs:{...(b=i.parameters)==null?void 0:b.docs,source:{originalSource:`{
  args: {
    story: developerStory,
    recommendations: [{
      title: "GitHub",
      reason: "Industry-standard code repository hosting with excellent collaboration features",
      intent_match_score: 0.95,
      admesh_link: "https://useadmesh.com/track?ad_id=github&story=developer-workflow",
      ad_id: "github",
      product_id: "github",
      has_free_tier: true,
      trial_days: 0,
      keywords: ["Git", "Code Repository", "Collaboration"],
      categories: ["Development Tools"],
      features: ["Version Control", "Pull Requests", "Actions", "Issues"],
      pricing: "Free for public repositories"
    }, {
      title: "Sentry",
      reason: "Real-time error tracking and performance monitoring for production applications",
      intent_match_score: 0.88,
      admesh_link: "https://useadmesh.com/track?ad_id=sentry&story=developer-workflow",
      ad_id: "sentry",
      product_id: "sentry",
      has_free_tier: true,
      trial_days: 14,
      keywords: ["Error Tracking", "Monitoring", "Performance"],
      categories: ["Development Tools"],
      features: ["Error Tracking", "Performance Monitoring", "Release Health"],
      pricing: "Free tier available"
    }],
    title: "Building the Perfect Development Workflow",
    category: "Software Development"
  },
  parameters: {
    docs: {
      description: {
        story: 'A developer-focused story demonstrating how technical tool recommendations can be naturally integrated into professional narratives.'
      }
    }
  }
}`,...(v=(f=i.parameters)==null?void 0:f.docs)==null?void 0:v.source}}};var w,S,k;n.parameters={...n.parameters,docs:{...(w=n.parameters)==null?void 0:w.docs,source:{originalSource:`{
  render: () => <AdFormatComparison />,
  parameters: {
    docs: {
      description: {
        story: 'Side-by-side comparison showing the difference between traditional intrusive advertising and AdMesh\\'s contextual storybook format.'
      }
    }
  }
}`,...(k=(S=n.parameters)==null?void 0:S.docs)==null?void 0:k.source}}};var j,_,N;d.parameters={...d.parameters,docs:{...(j=d.parameters)==null?void 0:j.docs,source:{originalSource:`{
  render: () => {
    const [selectedStory, setSelectedStory] = useState<'startup' | 'developer'>('startup');
    const stories = {
      startup: {
        story: startupJourneyStory,
        recommendations: businessStoryRecommendations,
        title: "The Startup Founder's Journey",
        category: "Business Growth"
      },
      developer: {
        story: developerStory,
        recommendations: [businessStoryRecommendations[0],
        // Reuse for demo
        businessStoryRecommendations[1]],
        title: "Building the Perfect Development Workflow",
        category: "Software Development"
      }
    };
    return <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            🎭 Interactive Storybook Ad Demo
          </h2>
          <div className="flex justify-center gap-4 mb-6">
            <button onClick={() => setSelectedStory('startup')} className={\`px-4 py-2 rounded-lg font-medium \${selectedStory === 'startup' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}\`}>
              📈 Startup Story
            </button>
            <button onClick={() => setSelectedStory('developer')} className={\`px-4 py-2 rounded-lg font-medium \${selectedStory === 'developer' ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}\`}>
              💻 Developer Story
            </button>
          </div>
        </div>
        
        <StorybookAdDemo {...stories[selectedStory]} />
      </div>;
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive demo allowing you to switch between different story types and see how AdMesh recommendations adapt to different contexts and audiences.'
      }
    }
  }
}`,...(N=(_=d.parameters)==null?void 0:_.docs)==null?void 0:N.source}}};const Q=["StartupJourneyStory","DeveloperWorkflowStory","AdFormatComparisonDemo","InteractiveStorybookDemo"];export{n as AdFormatComparisonDemo,i as DeveloperWorkflowStory,d as InteractiveStorybookDemo,a as StartupJourneyStory,Q as __namedExportsOrder,K as default};
