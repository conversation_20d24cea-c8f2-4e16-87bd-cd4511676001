import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as y}from"./iframe-6V_WvihQ.js";import{c as f,A as v}from"./AdMeshLinkTracker-BhGdnnRa.js";const n=({recommendation:s,theme:r,showMatchScore:c=!0,showBadges:o=!0,onClick:i,className:x})=>{const h=y.useMemo(()=>{var l;const a=[];s.intent_match_score>=.8&&a.push("Top Match"),s.has_free_tier&&a.push("Free Tier"),s.trial_days&&s.trial_days>0&&a.push("Trial Available");const t=["ai","artificial intelligence","machine learning","ml","automation"];return(((l=s.keywords)==null?void 0:l.some(g=>t.some(m=>g.toLowerCase().includes(m))))||s.title.toLowerCase().includes("ai"))&&a.push("AI Powered"),a},[s]),d=Math.round(s.intent_match_score*100),u=f("admesh-component","admesh-card","relative p-3 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow cursor-pointer",x),p=r!=null&&r.accentColor?{"--admesh-primary":r.accentColor,"--admesh-primary-hover":r.accentColor+"dd"}:void 0;return e.jsx(v,{adId:s.ad_id,admeshLink:s.admesh_link,productId:s.product_id,onClick:()=>i==null?void 0:i(s.ad_id,s.admesh_link),trackingData:{title:s.title,matchScore:s.intent_match_score},className:u,children:e.jsxs("div",{className:"h-full flex flex-col",style:p,"data-admesh-theme":r==null?void 0:r.mode,children:[e.jsx("div",{className:"flex justify-between items-start mb-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[o&&h.includes("Top Match")&&e.jsx("span",{className:"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full",children:"Top Match"}),e.jsx("h4",{className:"font-semibold text-gray-800 dark:text-gray-200",children:s.title}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{className:"text-xs px-2 py-1 rounded-full bg-black text-white hover:bg-gray-800 flex items-center",children:["Visit",e.jsx("svg",{className:"ml-1 h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})]})}),e.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300 mb-3",children:s.reason}),c&&typeof s.intent_match_score=="number"&&e.jsxs("div",{className:"mb-3",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx("span",{children:"Confidence"}),e.jsxs("span",{children:[d,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden",children:e.jsx("div",{className:"bg-black h-1.5",style:{width:`${d}%`}})})]}),e.jsxs("div",{className:"flex flex-wrap gap-2 text-xs mb-2",children:[s.pricing&&e.jsxs("span",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),s.pricing]}),s.has_free_tier&&e.jsxs("span",{className:"flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"})}),"Free Tier"]}),s.trial_days&&s.trial_days>0&&e.jsxs("span",{className:"flex items-center text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"})}),s.trial_days,"-day trial"]})]}),s.features&&s.features.length>0&&e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1",children:"Features:"}),e.jsx("div",{className:"flex flex-wrap gap-1.5",children:s.features.map((a,t)=>e.jsxs("span",{className:"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),a]},t))})]}),s.integrations&&s.integrations.length>0&&e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1",children:"Integrates with:"}),e.jsx("div",{className:"flex flex-wrap gap-1.5",children:s.integrations.map((a,t)=>e.jsxs("span",{className:"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300",children:[e.jsx("svg",{className:"h-3 w-3 mr-0.5 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),a]},t))})]}),s.reviews_summary&&e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 mt-2",children:s.reviews_summary}),e.jsx("div",{className:"flex justify-end mt-auto pt-2",children:e.jsx("span",{className:"text-xs text-gray-400 dark:text-gray-500",children:"Powered by AdMesh"})})]})})};n.displayName="AdMeshProductCard";n.__docgenInfo={description:"",methods:[],displayName:"AdMeshProductCard",props:{recommendation:{required:!0,tsType:{name:"AdMeshRecommendation"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},showMatchScore:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},showBadges:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},maxKeywords:{required:!1,tsType:{name:"number"},description:""},onClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},onTrackView:{required:!1,tsType:{name:"signature",type:"function",raw:"(data: TrackingData) => void",signature:{arguments:[{type:{name:"TrackingData"},name:"data"}],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{n as A};
