import{j as e}from"./jsx-runtime-D_zvdyIk.js";const i=()=>e.jsxs("div",{style:{padding:"40px",fontFamily:"system-ui, sans-serif",maxWidth:"800px",margin:"0 auto"},children:[e.jsxs("div",{style:{textAlign:"center",marginBottom:"40px"},children:[e.jsx("h1",{style:{fontSize:"2.5rem",color:"#333",marginBottom:"16px"},children:"🎭 AdMesh UI SDK Storybook"}),e.jsx("p",{style:{fontSize:"1.2rem",color:"#666",marginBottom:"32px"},children:"Interactive showcase of citation-based conversation ads and UI components"})]}),e.jsxs("div",{style:{background:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"8px",padding:"24px",marginBottom:"32px"},children:[e.jsx("h2",{style:{color:"#0066cc",marginBottom:"16px"},children:"🌐 Quick Links"}),e.jsxs("div",{style:{display:"grid",gap:"12px"},children:[e.jsx("a",{href:"https://gounimanikumar12.github.io/admesh-docs/",target:"_blank",rel:"noopener noreferrer",style:{display:"flex",alignItems:"center",padding:"12px 16px",background:"white",border:"1px solid #0066cc",borderRadius:"6px",textDecoration:"none",color:"#0066cc",fontSize:"16px",fontWeight:"500",transition:"all 0.2s"},onMouseOver:o=>{o.currentTarget.style.background="#0066cc",o.currentTarget.style.color="white"},onMouseOut:o=>{o.currentTarget.style.background="white",o.currentTarget.style.color="#0066cc"},children:"📚 Complete Documentation - Setup guides, API reference, and integration examples"}),e.jsx("a",{href:"https://useadmesh.com",target:"_blank",rel:"noopener noreferrer",style:{display:"flex",alignItems:"center",padding:"12px 16px",background:"white",border:"1px solid #28a745",borderRadius:"6px",textDecoration:"none",color:"#28a745",fontSize:"16px",fontWeight:"500",transition:"all 0.2s"},onMouseOver:o=>{o.currentTarget.style.background="#28a745",o.currentTarget.style.color="white"},onMouseOut:o=>{o.currentTarget.style.background="white",o.currentTarget.style.color="#28a745"},children:"🚀 AdMesh Dashboard - Get your API keys and manage campaigns"})]})]}),e.jsxs("div",{style:{marginBottom:"32px"},children:[e.jsx("h2",{style:{color:"#333",marginBottom:"16px"},children:"🎯 What's in this Storybook?"}),e.jsxs("div",{style:{display:"grid",gap:"16px"},children:[e.jsxs("div",{style:{padding:"16px",border:"1px solid #e9ecef",borderRadius:"6px",background:"white"},children:[e.jsx("h3",{style:{color:"#8b5cf6",marginBottom:"8px"},children:"💬 Conversational Ad Units"}),e.jsx("p",{style:{color:"#666",margin:0},children:"Citation-based conversation ads that display recommendations as numbered references within text, perfect for AI assistants and chatbots."})]}),e.jsxs("div",{style:{padding:"16px",border:"1px solid #e9ecef",borderRadius:"6px",background:"white"},children:[e.jsx("h3",{style:{color:"#f59e0b",marginBottom:"8px"},children:"📋 Sidebar Components"}),e.jsx("p",{style:{color:"#666",margin:0},children:"Persistent sidebar panels for displaying recommendations alongside your main content with search, filtering, and multiple display modes."})]}),e.jsxs("div",{style:{padding:"16px",border:"1px solid #e9ecef",borderRadius:"6px",background:"white"},children:[e.jsx("h3",{style:{color:"#10b981",marginBottom:"8px"},children:"💬 Chat Components"}),e.jsx("p",{style:{color:"#666",margin:0},children:"Floating chat widgets and embeddable chat interfaces for AI-powered recommendations with auto-recommendation triggers."})]}),e.jsxs("div",{style:{padding:"16px",border:"1px solid #e9ecef",borderRadius:"6px",background:"white"},children:[e.jsx("h3",{style:{color:"#ef4444",marginBottom:"8px"},children:"🧩 Core Components"}),e.jsx("p",{style:{color:"#666",margin:0},children:"Essential building blocks including product cards, comparison tables, badges, and link tracking components with built-in analytics."})]})]})]}),e.jsxs("div",{style:{background:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"8px",padding:"20px",marginBottom:"32px"},children:[e.jsx("h3",{style:{color:"#856404",marginBottom:"12px"},children:"🦖 Built with Modern Tools"}),e.jsxs("p",{style:{color:"#856404",margin:0},children:["This Storybook showcases components built with ",e.jsx("strong",{children:"React + TypeScript"}),", styled with ",e.jsx("strong",{children:"Tailwind CSS"}),", and documented with ",e.jsx("strong",{children:"Storybook"}),". The complete documentation site is powered by ",e.jsx("strong",{children:"Docusaurus"})," for the best developer experience."]})]}),e.jsx("div",{style:{textAlign:"center"},children:e.jsxs("p",{style:{color:"#666",fontSize:"14px"},children:["Ready to integrate AdMesh into your application?",e.jsx("br",{}),e.jsx("a",{href:"https://gounimanikumar12.github.io/admesh-docs/getting-started/overview",target:"_blank",rel:"noopener noreferrer",style:{color:"#0066cc",textDecoration:"none"},children:"Start with our Getting Started Guide →"})]})})]}),d={title:"🏠 Welcome",component:i,parameters:{layout:"fullscreen",docs:{description:{component:"Welcome to the AdMesh UI SDK Storybook! Explore our citation-based conversation ads and UI components."}}}},t={name:"🎭 Welcome to AdMesh UI SDK"};var r,n,s;t.parameters={...t.parameters,docs:{...(r=t.parameters)==null?void 0:r.docs,source:{originalSource:`{
  name: '🎭 Welcome to AdMesh UI SDK'
}`,...(s=(n=t.parameters)==null?void 0:n.docs)==null?void 0:s.source}}};const c=["WelcomeToAdMesh"];export{t as WelcomeToAdMesh,c as __namedExportsOrder,d as default};
