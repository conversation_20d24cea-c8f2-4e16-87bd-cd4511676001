import{j as e}from"./jsx-runtime-D_zvdyIk.js";import{r as d}from"./iframe-CaenRy-o.js";import{c as g}from"./AdMeshLinkTracker-7Al4Klcr.js";import{A as B}from"./AdMeshChatInterface-BtAPWNg2.js";const E=({config:s,theme:a,title:k="AI Assistant",subtitle:C="Get personalized recommendations",isOpen:m,onToggle:p,onSendMessage:f,onRecommendationClick:N,autoRecommendations:o,autoRecommendationTrigger:u,showInputField:w=!0,autoShowRecommendations:x=!1,onAutoRecommendationDismiss:y,className:A})=>{const[v,b]=d.useState(s.autoOpen||!1),[h,i]=d.useState([]),[L,M]=d.useState(!1),[T,q]=d.useState(!1),c=m!==void 0?m:v;d.useEffect(()=>{if(s.showWelcomeMessage&&s.welcomeMessage&&h.length===0){const t={id:"welcome",role:"assistant",content:s.welcomeMessage,timestamp:new Date};i([t])}},[s.showWelcomeMessage,s.welcomeMessage,h.length]),d.useEffect(()=>{if(o&&o.length>0&&x){const t={id:`auto-${Date.now()}`,role:"assistant",content:u?`Based on "${u}", here are some relevant recommendations:`:"I found some relevant recommendations for you:",timestamp:new Date,recommendations:o};m===void 0&&b(!0),i(n=>n.some(r=>r.id.startsWith("auto-"))?n.map(r=>r.id.startsWith("auto-")?t:r):[...n,t])}},[o,x,u,m]);const j=()=>{p?p():b(!v),q(!0)},I=async t=>{if(!f)return;const n={id:`user-${Date.now()}`,role:"user",content:t,timestamp:new Date};i(l=>[...l,n]),M(!0);try{const l=await f(t);i(r=>[...r,l])}catch(l){console.error("Error sending message:",l);const r={id:`error-${Date.now()}`,role:"assistant",content:"Sorry, I encountered an error. Please try again.",timestamp:new Date};i(z=>[...z,r])}finally{M(!1)}},D=()=>{switch(s.size){case"sm":return"w-80 h-96";case"md":return"w-96 h-[32rem]";case"lg":return"w-[28rem] h-[36rem]";case"xl":return"w-[32rem] h-[40rem]";default:return"w-96 h-[32rem]"}},W=g("admesh-floating-chat","fixed z-50 transition-all duration-300 ease-in-out",(()=>{switch(s.position){case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";default:return"bottom-4 right-4"}})(),A),S=g("bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",D(),{"opacity-0 scale-95 pointer-events-none":!c,"opacity-100 scale-100":c}),P=a!=null&&a.accentColor?{"--admesh-primary":a.accentColor}:void 0;return e.jsxs("div",{className:W,style:P,"data-admesh-theme":a==null?void 0:a.mode,children:[e.jsx("div",{className:S,children:c&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-sm",children:k}),e.jsx("p",{className:"text-xs text-blue-100",children:C})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[o&&o.length>0&&y&&e.jsx("button",{onClick:()=>{y(),i(t=>t.filter(n=>!n.id.startsWith("auto-")))},className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Dismiss recommendations",title:"Dismiss recommendations",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})})}),e.jsx("button",{onClick:j,className:"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors","aria-label":"Close chat",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsx(B,{messages:h,config:{...s,showInputField:w},theme:a,isLoading:L,onSendMessage:w?I:void 0,onRecommendationClick:N,className:"h-full"})]})}),!c&&e.jsxs("button",{onClick:j,className:g("w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700","text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200","flex items-center justify-center relative"),"aria-label":"Open chat",children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),!T&&e.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),c&&e.jsx("div",{className:"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm",children:"Powered by AdMesh"})]})};E.__docgenInfo={description:"",methods:[],displayName:"AdMeshFloatingChat",props:{config:{required:!0,tsType:{name:"AdMeshChatConfig"},description:""},theme:{required:!1,tsType:{name:"AdMeshTheme"},description:""},title:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:"'AI Assistant'",computed:!1}},subtitle:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:"'Get personalized recommendations'",computed:!1}},isOpen:{required:!1,tsType:{name:"boolean"},description:""},onToggle:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},onSendMessage:{required:!1,tsType:{name:"signature",type:"function",raw:"(message: string) => Promise<ChatMessage>",signature:{arguments:[{type:{name:"string"},name:"message"}],return:{name:"Promise",elements:[{name:"ChatMessage"}],raw:"Promise<ChatMessage>"}}},description:""},onRecommendationClick:{required:!1,tsType:{name:"signature",type:"function",raw:"(adId: string, admeshLink: string) => void",signature:{arguments:[{type:{name:"string"},name:"adId"},{type:{name:"string"},name:"admeshLink"}],return:{name:"void"}}},description:""},autoRecommendations:{required:!1,tsType:{name:"Array",elements:[{name:"AdMeshRecommendation"}],raw:"AdMeshRecommendation[]"},description:""},autoRecommendationTrigger:{required:!1,tsType:{name:"string"},description:""},showInputField:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},autoShowRecommendations:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}},onAutoRecommendationDismiss:{required:!1,tsType:{name:"signature",type:"function",raw:"() => void",signature:{arguments:[],return:{name:"void"}}},description:""},className:{required:!1,tsType:{name:"string"},description:""}}};export{E as A};
