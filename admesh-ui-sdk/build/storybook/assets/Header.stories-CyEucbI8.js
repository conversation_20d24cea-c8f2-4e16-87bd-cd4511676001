import{H as d}from"./Header-DyW3d9t5.js";import"./jsx-runtime-D_zvdyIk.js";import"./Button-Brdg18Zs.js";const{fn:r}=__STORYBOOK_MODULE_TEST__,i={title:"Example/Header",component:d,tags:["autodocs"],parameters:{layout:"fullscreen"},args:{onLogin:r(),onLogout:r(),onCreateAccount:r()}},e={args:{user:{name:"<PERSON>"}}},o={};var a,s,t;e.parameters={...e.parameters,docs:{...(a=e.parameters)==null?void 0:a.docs,source:{originalSource:`{
  args: {
    user: {
      name: '<PERSON>'
    }
  }
}`,...(t=(s=e.parameters)==null?void 0:s.docs)==null?void 0:t.source}}};var n,c,m;o.parameters={...o.parameters,docs:{...(n=o.parameters)==null?void 0:n.docs,source:{originalSource:"{}",...(m=(c=o.parameters)==null?void 0:c.docs)==null?void 0:m.source}}};const l=["LoggedIn","LoggedOut"];export{e as LoggedIn,o as LoggedOut,l as __namedExportsOrder,i as default};
