{"v": 5, "entries": {"chat-admeshautorecommendationwidget--default": {"type": "story", "id": "chat-admeshautorecommendationwidget--default", "name": "<PERSON><PERSON><PERSON>", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshautorecommendationwidget--bottom-left": {"type": "story", "id": "chat-admeshautorecommendationwidget--bottom-left", "name": "Bottom Left", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshautorecommendationwidget--large-size": {"type": "story", "id": "chat-admeshautorecommendationwidget--large-size", "name": "Large Size", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshautorecommendationwidget--dark-theme": {"type": "story", "id": "chat-admeshautorecommendationwidget--dark-theme", "name": "Dark Theme", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshautorecommendationwidget--fast-appearance": {"type": "story", "id": "chat-admeshautorecommendationwidget--fast-appearance", "name": "Fast Appearance", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshautorecommendationwidget--custom-trigger": {"type": "story", "id": "chat-admeshautorecommendationwidget--custom-trigger", "name": "Custom Trigger", "title": "Chat/AdMeshAutoRecommendationWidget", "importPath": "./src/stories/AdMeshAutoRecommendationWidget.stories.tsx", "componentPath": "./src/components/AdMeshAutoRecommendationWidget.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--default": {"type": "story", "id": "chat-admeshchatinterface--default", "name": "<PERSON><PERSON><PERSON>", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--empty-state": {"type": "story", "id": "chat-admesh<PERSON><PERSON>terface--empty-state", "name": "Empty State", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--with-typing-indicator": {"type": "story", "id": "chat-admeshchatinterface--with-typing-indicator", "name": "With Ty<PERSON> Indicator", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--dark-theme": {"type": "story", "id": "chat-admes<PERSON><PERSON><PERSON><PERSON><PERSON>--dark-theme", "name": "Dark Theme", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--limited-messages": {"type": "story", "id": "chat-admesh<PERSON><PERSON>terface--limited-messages", "name": "Limited Messages", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--no-suggestions": {"type": "story", "id": "chat-admesh<PERSON><PERSON>terface--no-suggestions", "name": "No Suggestions", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "chat-admeshchatinterface--custom-placeholder": {"type": "story", "id": "chat-admesh<PERSON><PERSON>terface--custom-placeholder", "name": "Custom Placeholder", "title": "Chat/AdMeshChatInterface", "importPath": "./src/stories/AdMeshChatInterface.stories.tsx", "componentPath": "./src/components/AdMeshChatInterface.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--numbered-style": {"type": "story", "id": "citation-admeshcitationreference--numbered-style", "name": "Numbered Style", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--bracketed-style": {"type": "story", "id": "citation-admeshcitationreference--bracketed-style", "name": "Bracketed Style", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--superscript-style": {"type": "story", "id": "citation-admeshcitationreference--superscript-style", "name": "Superscript Style", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--without-tooltip": {"type": "story", "id": "citation-admeshcitationreference--without-tooltip", "name": "Without Tooltip", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--dark-theme": {"type": "story", "id": "citation-admeshcitationreference--dark-theme", "name": "Dark Theme", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--high-match-score": {"type": "story", "id": "citation-admeshcitationreference--high-match-score", "name": "High Match Score", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationreference--interactive-demo": {"type": "story", "id": "citation-admeshcitationreference--interactive-demo", "name": "Interactive Demo", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test", "play-fn"]}, "citation-admeshcitationreference--multiple-styles": {"type": "story", "id": "citation-admeshcitationreference--multiple-styles", "name": "Multiple Styles", "title": "Citation/AdMeshCitationReference", "importPath": "./src/stories/AdMeshCitationReference.stories.tsx", "componentPath": "./src/components/AdMeshCitationReference.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--numbered-citations": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--numbered-citations", "name": "Numbered Citations", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--bracketed-citations": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--bracketed-citations", "name": "Bracketed Citations", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--superscript-citations": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--superscript-citations", "name": "Superscript Citations", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--without-reference-list": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--without-reference-list", "name": "Without Reference List", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--dark-theme": {"type": "story", "id": "citation-admesh<PERSON><PERSON><PERSON>--dark-theme", "name": "Dark Theme", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--short-text": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--short-text", "name": "Short Text", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--clickable-product-names": {"type": "story", "id": "citation-admeshcitationunit--clickable-product-names", "name": "Clickable Product Names", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "citation-admeshcitationunit--interactive-demo": {"type": "story", "id": "citation-admesh<PERSON><PERSON>t--interactive-demo", "name": "Interactive Demo", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test", "play-fn"]}, "citation-admeshcitationunit--storybook-business-narrative": {"type": "story", "id": "citation-admesh<PERSON><PERSON><PERSON>--storybook-business-narrative", "name": "Storybook Business Narrative", "title": "Citation/AdMeshCitationUnit", "importPath": "./src/stories/AdMeshCitationUnit.stories.tsx", "componentPath": "./src/components/AdMeshCitationUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--default": {"type": "story", "id": "conversational-admeshconversationsummary--default", "name": "<PERSON><PERSON><PERSON>", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--extended-summary": {"type": "story", "id": "conversational-admeshconversationsummary--extended-summary", "name": "Extended Summary", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--dark-theme": {"type": "story", "id": "conversational-admeshconversationsummary--dark-theme", "name": "Dark Theme", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--short-summary": {"type": "story", "id": "conversational-admeshconversationsummary--short-summary", "name": "Short Summary", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--single-recommendation": {"type": "story", "id": "conversational-admeshconversationsummary--single-recommendation", "name": "Single Recommendation", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--interactive-demo": {"type": "story", "id": "conversational-admeshconversationsummary--interactive-demo", "name": "Interactive Demo", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test", "play-fn"]}, "conversational-admeshconversationsummary--custom-accent-color": {"type": "story", "id": "conversational-admeshconversationsummary--custom-accent-color", "name": "Custom Accent Color", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationsummary--mobile-optimized": {"type": "story", "id": "conversational-admeshconversationsummary--mobile-optimized", "name": "Mobile Optimized", "title": "Conversational/AdMeshConversationSummary", "importPath": "./src/stories/AdMeshConversationSummary.stories.tsx", "componentPath": "./src/components/AdMeshConversationSummary.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--inline-mode": {"type": "story", "id": "conversational-admeshconversationalunit--inline-mode", "name": "Inline Mode", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--minimal-mode": {"type": "story", "id": "conversational-admeshconversationalunit--minimal-mode", "name": "Minimal Mode", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--floating-mode": {"type": "story", "id": "conversational-admeshconversationalunit--floating-mode", "name": "Floating Mode", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--summary-mode": {"type": "story", "id": "conversational-admeshconversationalunit--summary-mode", "name": "Summary Mode", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--dark-theme": {"type": "story", "id": "conversational-admeshconversationalunit--dark-theme", "name": "Dark Theme", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--delayed-display": {"type": "story", "id": "conversational-admeshconversationalunit--delayed-display", "name": "Delayed <PERSON>lay", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--chat-context": {"type": "story", "id": "conversational-admeshconversationalunit--chat-context", "name": "Chat Context", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test"]}, "conversational-admeshconversationalunit--citation-mode": {"type": "story", "id": "conversational-admeshconversationalunit--citation-mode", "name": "Citation Mode", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test", "play-fn"]}, "conversational-admeshconversationalunit--interactive-demo": {"type": "story", "id": "conversational-admeshconversationalunit--interactive-demo", "name": "Interactive Demo", "title": "Conversational/AdMeshConversationalUnit", "importPath": "./src/stories/AdMeshConversationalUnit.stories.tsx", "componentPath": "./src/components/AdMeshConversationalUnit.tsx", "tags": ["dev", "test", "play-fn"]}, "chat-admeshfloatingchat--default": {"type": "story", "id": "chat-admeshfloatingchat--default", "name": "<PERSON><PERSON><PERSON>", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "chat-admeshfloatingchat--bottom-left": {"type": "story", "id": "chat-admeshfloatingchat--bottom-left", "name": "Bottom Left", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "chat-admeshfloatingchat--large-size": {"type": "story", "id": "chat-admeshfloatingchat--large-size", "name": "Large Size", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "chat-admeshfloatingchat--dark-theme": {"type": "story", "id": "chat-admeshfloatingchat--dark-theme", "name": "Dark Theme", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "chat-admeshfloatingchat--auto-open": {"type": "story", "id": "chat-admeshfloatingchat--auto-open", "name": "Auto Open", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "chat-admeshfloatingchat--compact": {"type": "story", "id": "chat-admeshfloatingchat--compact", "name": "Compact", "title": "Chat/AdMeshFloatingChat", "importPath": "./src/stories/AdMeshFloatingChat.stories.tsx", "componentPath": "./src/components/AdMeshFloatingChat.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--default": {"type": "story", "id": "conversational-admeshinlinerecommendation--default", "name": "<PERSON><PERSON><PERSON>", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--compact": {"type": "story", "id": "conversational-admeshinlinerecommendation--compact", "name": "Compact", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--compact-no-reason": {"type": "story", "id": "conversational-admeshinlinerecommendation--compact-no-reason", "name": "Compact No Reason", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--high-match-score": {"type": "story", "id": "conversational-admeshinlinerecommendation--high-match-score", "name": "High Match Score", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--low-match-score": {"type": "story", "id": "conversational-admeshinlinerecommendation--low-match-score", "name": "Low Match Score", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--dark-theme": {"type": "story", "id": "conversational-admeshinlinerecommendation--dark-theme", "name": "Dark Theme", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--compact-dark": {"type": "story", "id": "conversational-admeshinlinerecommendation--compact-dark", "name": "Compact Dark", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--interactive-demo": {"type": "story", "id": "conversational-admeshinlinerecommendation--interactive-demo", "name": "Interactive Demo", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test", "play-fn"]}, "conversational-admeshinlinerecommendation--chat-bubble-example": {"type": "story", "id": "conversational-admeshinlinerecommendation--chat-bubble-example", "name": "Chat Bubble Example", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--multiple-recommendations": {"type": "story", "id": "conversational-admeshinlinerecommendation--multiple-recommendations", "name": "Multiple Recommendations", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "conversational-admeshinlinerecommendation--mobile-optimized": {"type": "story", "id": "conversational-admeshinlinerecommendation--mobile-optimized", "name": "Mobile Optimized", "title": "Conversational/AdMeshInlineRecommendation", "importPath": "./src/stories/AdMeshInlineRecommendation.stories.tsx", "componentPath": "./src/components/AdMeshInlineRecommendation.tsx", "tags": ["dev", "test"]}, "admesh-layout--docs": {"id": "admesh-layout--docs", "title": "AdMesh/Layout", "name": "Docs", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "admesh-layout--auto-layout": {"type": "story", "id": "admesh-layout--auto-layout", "name": "Auto Layout", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--cards-layout": {"type": "story", "id": "admesh-layout--cards-layout", "name": "Cards Layout", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--compare-layout": {"type": "story", "id": "admesh-layout--compare-layout", "name": "Compare Layout", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--single-product": {"type": "story", "id": "admesh-layout--single-product", "name": "Single Product", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--two-products": {"type": "story", "id": "admesh-layout--two-products", "name": "Two Products", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--dark-theme": {"type": "story", "id": "admesh-layout--dark-theme", "name": "Dark Theme", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--custom-accent-color": {"type": "story", "id": "admesh-layout--custom-accent-color", "name": "Custom Accent Color", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--empty-state": {"type": "story", "id": "admesh-layout--empty-state", "name": "Empty State", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--without-match-scores": {"type": "story", "id": "admesh-layout--without-match-scores", "name": "Without Match Scores", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-layout--limited-display": {"type": "story", "id": "admesh-layout--limited-display", "name": "Limited Display", "title": "AdMesh/Layout", "importPath": "./src/stories/AdMeshLayout.stories.tsx", "componentPath": "./src/components/AdMeshLayout.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--docs": {"id": "admesh-productcard--docs", "title": "AdMesh/ProductCard", "name": "Docs", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "admesh-productcard--default": {"type": "story", "id": "admesh-productcard--default", "name": "<PERSON><PERSON><PERSON>", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--high-match-score": {"type": "story", "id": "admesh-productcard--high-match-score", "name": "High Match Score", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--ai-product": {"type": "story", "id": "admesh-productcard--ai-product", "name": "AI Product", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--dark-theme": {"type": "story", "id": "admesh-productcard--dark-theme", "name": "Dark Theme", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--custom-accent-color": {"type": "story", "id": "admesh-productcard--custom-accent-color", "name": "Custom Accent Color", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--without-badges": {"type": "story", "id": "admesh-productcard--without-badges", "name": "Without Badges", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--without-match-score": {"type": "story", "id": "admesh-productcard--without-match-score", "name": "Without Match Score", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-productcard--minimal-data": {"type": "story", "id": "admesh-productcard--minimal-data", "name": "Minimal Data", "title": "AdMesh/ProductCard", "importPath": "./src/stories/AdMeshProductCard.stories.tsx", "componentPath": "./src/components/AdMeshProductCard.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--docs": {"id": "admesh-showcase--docs", "title": "AdMesh/Showcase", "name": "Docs", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "admesh-showcase--premium-showcase": {"type": "story", "id": "admesh-showcase--premium-showcase", "name": "Premium Showcase", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--dark-theme-showcase": {"type": "story", "id": "admesh-showcase--dark-theme-showcase", "name": "Dark Theme Showcase", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--custom-accent-cards": {"type": "story", "id": "admesh-showcase--custom-accent-cards", "name": "Custom Accent Cards", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--comparison-view": {"type": "story", "id": "admesh-showcase--comparison-view", "name": "Comparison View", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--premium-card": {"type": "story", "id": "admesh-showcase--premium-card", "name": "Premium Card", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--badge-showcase": {"type": "story", "id": "admesh-showcase--badge-showcase", "name": "Badge Showcase", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "admesh-showcase--responsive-showcase": {"type": "story", "id": "admesh-showcase--responsive-showcase", "name": "Responsive Showcase", "title": "AdMesh/Showcase", "importPath": "./src/stories/AdMeshShowcase.stories.tsx", "componentPath": "./src/components", "tags": ["dev", "test", "autodocs"]}, "sidebar-admeshsidebar--default": {"type": "story", "id": "sidebar-admeshsidebar--default", "name": "<PERSON><PERSON><PERSON>", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--right-sidebar": {"type": "story", "id": "sidebar-admeshsidebar--right-sidebar", "name": "Right Sidebar", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--compact-sidebar": {"type": "story", "id": "sidebar-admeshsidebar--compact-sidebar", "name": "Compact Sidebar", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--mixed-display-mode": {"type": "story", "id": "sidebar-admeshsidebar--mixed-display-mode", "name": "Mixed Display Mode", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--dark-theme": {"type": "story", "id": "sidebar-admeshsidebar--dark-theme", "name": "Dark Theme", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--collapsed-by-default": {"type": "story", "id": "sidebar-admeshsidebar--collapsed-by-default", "name": "Collapsed By Default", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "sidebar-admeshsidebar--extra-large": {"type": "story", "id": "sidebar-admeshsidebar--extra-large", "name": "Extra Large", "title": "Sidebar/AdMeshSidebar", "importPath": "./src/stories/AdMeshSidebar.stories.tsx", "componentPath": "./src/components/AdMeshSidebar.tsx", "tags": ["dev", "test"]}, "example-button--docs": {"id": "example-button--docs", "title": "Example/Button", "name": "Docs", "importPath": "./src/stories/Button.stories.ts", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "example-button--primary": {"type": "story", "id": "example-button--primary", "name": "Primary", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--secondary": {"type": "story", "id": "example-button--secondary", "name": "Secondary", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--large": {"type": "story", "id": "example-button--large", "name": "Large", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "example-button--small": {"type": "story", "id": "example-button--small", "name": "Small", "title": "Example/Button", "importPath": "./src/stories/Button.stories.ts", "componentPath": "./src/stories/Button.tsx", "tags": ["dev", "test", "autodocs"]}, "conversational-showcase--interactive-demo": {"type": "story", "id": "conversational-showcase--interactive-demo", "name": "Interactive Demo", "title": "Conversational/Showcase", "importPath": "./src/stories/ConversationalShowcase.stories.tsx", "tags": ["dev", "test"]}, "example-header--docs": {"id": "example-header--docs", "title": "Example/Header", "name": "Docs", "importPath": "./src/stories/Header.stories.ts", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "example-header--logged-in": {"type": "story", "id": "example-header--logged-in", "name": "Logged In", "title": "Example/Header", "importPath": "./src/stories/Header.stories.ts", "componentPath": "./src/stories/Header.tsx", "tags": ["dev", "test", "autodocs"]}, "example-header--logged-out": {"type": "story", "id": "example-header--logged-out", "name": "Logged Out", "title": "Example/Header", "importPath": "./src/stories/Header.stories.ts", "componentPath": "./src/stories/Header.tsx", "tags": ["dev", "test", "autodocs"]}, "example-page--logged-out": {"type": "story", "id": "example-page--logged-out", "name": "Logged Out", "title": "Example/Page", "importPath": "./src/stories/Page.stories.ts", "componentPath": "./src/stories/Page.tsx", "tags": ["dev", "test"]}, "example-page--logged-in": {"type": "story", "id": "example-page--logged-in", "name": "Logged In", "title": "Example/Page", "importPath": "./src/stories/Page.stories.ts", "componentPath": "./src/stories/Page.tsx", "tags": ["dev", "test", "play-fn"]}, "admesh-storybook-ad-formats--docs": {"id": "admesh-storybook-ad-formats--docs", "title": "AdMesh/Storybook Ad Formats", "name": "Docs", "importPath": "./src/stories/StorybookAdFormats.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "admesh-storybook-ad-formats--startup-journey-story": {"type": "story", "id": "admesh-storybook-ad-formats--startup-journey-story", "name": "Startup Journey Story", "title": "AdMesh/Storybook Ad Formats", "importPath": "./src/stories/StorybookAdFormats.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-storybook-ad-formats--developer-workflow-story": {"type": "story", "id": "admesh-storybook-ad-formats--developer-workflow-story", "name": "Developer Workflow Story", "title": "AdMesh/Storybook Ad Formats", "importPath": "./src/stories/StorybookAdFormats.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-storybook-ad-formats--ad-format-comparison-demo": {"type": "story", "id": "admesh-storybook-ad-formats--ad-format-comparison-demo", "name": "Ad Format Comparison Demo", "title": "AdMesh/Storybook Ad Formats", "importPath": "./src/stories/StorybookAdFormats.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "admesh-storybook-ad-formats--interactive-storybook-demo": {"type": "story", "id": "admesh-storybook-ad-formats--interactive-storybook-demo", "name": "Interactive Storybook Demo", "title": "AdMesh/Storybook Ad Formats", "importPath": "./src/stories/StorybookAdFormats.stories.tsx", "tags": ["dev", "test", "autodocs"]}, "🏠-welcome--welcome-to-ad-mesh": {"type": "story", "id": "🏠-welcome--welcome-to-ad-mesh", "name": "🎭 Welcome to AdMesh UI SDK", "title": "🏠 Welcome", "importPath": "./src/stories/Welcome.stories.tsx", "tags": ["dev", "test"]}}}