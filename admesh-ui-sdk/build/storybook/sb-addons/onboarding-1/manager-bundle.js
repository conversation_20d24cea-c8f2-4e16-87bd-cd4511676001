try{
(()=>{var bo=Object.defineProperty;var ue=(e,t)=>()=>(e&&(t=e(e=0)),t);var vo=(e,t)=>{for(var n in t)bo(e,n,{get:t[n],enumerable:!0})};var X=ue(()=>{});var Z=ue(()=>{});var Q=ue(()=>{});var y,qs,De,$s,Vs,Ks,Js,mn,Xs,Zs,Ft,Qs,V,el,tl,yn,Bt,gn,nl,rl,ol,tt,il,al,sl,ae,ll,cl,ul,pl,fl,dl,Wt,pe,hl,ml,yl,vt=ue(()=>{X();Z();Q();y=__REACT__,{Children:qs,Component:De,Fragment:$s,Profiler:Vs,PureComponent:Ks,StrictMode:Js,Suspense:mn,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Xs,act:Zs,cloneElement:Ft,createContext:Qs,createElement:V,createFactory:el,createRef:tl,forwardRef:yn,isValidElement:Bt,lazy:gn,memo:nl,startTransition:rl,unstable_act:ol,useCallback:tt,useContext:il,useDebugValue:al,useDeferredValue:sl,useEffect:ae,useId:ll,useImperativeHandle:cl,useInsertionEffect:ul,useLayoutEffect:pl,useMemo:fl,useReducer:dl,useRef:Wt,useState:pe,useSyncExternalStore:hl,useTransition:ml,version:yl}=__REACT__});var Fe,El,Et,Ol,Sl,wl,Tl,Il,Cl,bn,Pl,vn,Rl,Ot=ue(()=>{X();Z();Q();Fe=__REACT_DOM__,{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:El,createPortal:Et,createRoot:Ol,findDOMNode:Sl,flushSync:wl,hydrate:Tl,hydrateRoot:Il,render:Cl,unmountComponentAtNode:bn,unstable_batchedUpdates:Pl,unstable_renderSubtreeIntoContainer:vn,version:Rl}=__REACT_DOM__});var St,Ht=ue(()=>{X();Z();Q();St="addon-controls"});var Ml,Dl,Fl,Bl,Wl,Hl,Ul,zl,Gl,Yl,ql,$l,Vl,Kl,Jl,Xl,Zl,Ql,ec,tc,nc,rc,oc,ic,ac,sc,En,lc,cc,uc,pc,fc,dc,hc,mc,yc,gc,bc,vc,Ec,Oc,Sc,wc,Tc,Ic,Cc,Pc,Rc,xc,On,_c,Ac,Nc,kc,Lc,jc,Mc,Dc,Ut=ue(()=>{X();Z();Q();Ml=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:Dl,ARGTYPES_INFO_RESPONSE:Fl,CHANNEL_CREATED:Bl,CHANNEL_WS_DISCONNECT:Wl,CONFIG_ERROR:Hl,CREATE_NEW_STORYFILE_REQUEST:Ul,CREATE_NEW_STORYFILE_RESPONSE:zl,CURRENT_STORY_WAS_SET:Gl,DOCS_PREPARED:Yl,DOCS_RENDERED:ql,FILE_COMPONENT_SEARCH_REQUEST:$l,FILE_COMPONENT_SEARCH_RESPONSE:Vl,FORCE_REMOUNT:Kl,FORCE_RE_RENDER:Jl,GLOBALS_UPDATED:Xl,NAVIGATE_URL:Zl,PLAY_FUNCTION_THREW_EXCEPTION:Ql,PRELOAD_ENTRIES:ec,PREVIEW_BUILDER_PROGRESS:tc,PREVIEW_KEYDOWN:nc,REGISTER_SUBSCRIPTION:rc,REQUEST_WHATS_NEW_DATA:oc,RESET_STORY_ARGS:ic,RESULT_WHATS_NEW_DATA:ac,SAVE_STORY_REQUEST:sc,SAVE_STORY_RESPONSE:En,SELECT_STORY:lc,SET_CONFIG:cc,SET_CURRENT_STORY:uc,SET_FILTER:pc,SET_GLOBALS:fc,SET_INDEX:dc,SET_STORIES:hc,SET_WHATS_NEW_CACHE:mc,SHARED_STATE_CHANGED:yc,SHARED_STATE_SET:gc,STORIES_COLLAPSE_ALL:bc,STORIES_EXPAND_ALL:vc,STORY_ARGS_UPDATED:Ec,STORY_CHANGED:Oc,STORY_ERRORED:Sc,STORY_FINISHED:wc,STORY_HOT_UPDATED:Tc,STORY_INDEX_INVALIDATED:Ic,STORY_MISSING:Cc,STORY_PREPARED:Pc,STORY_RENDERED:Rc,STORY_RENDER_PHASE_CHANGED:xc,STORY_SPECIFIED:On,STORY_THREW_EXCEPTION:_c,STORY_UNCHANGED:Ac,TELEMETRY_ERROR:Nc,TOGGLE_WHATS_NEW_NOTIFICATIONS:kc,UNHANDLED_ERRORS_WHILE_PLAYING:Lc,UPDATE_GLOBALS:jc,UPDATE_QUERY_PARAMS:Mc,UPDATE_STORY_ARGS:Dc}=__STORYBOOK_CORE_EVENTS__});var ju,Mu,Du,Fu,Bu,Wu,Hu,Uu,zu,Gu,Yu,qu,$u,Vu,Ku,Ju,Xu,Zu,Qu,ep,tp,np,rp,op,ip,wn,ap,sp,lp,cp,up,pp,fp,dp,hp,mp,yp,gp,bp,vp,Ep,Op,Sp,wp,Tn,Tp,Ip,Cp,Pp,Rp,xp,_p,Ap,Np,kp,Lp,jp,Mp,Dp,Fp,Bp,Wp,Hp,Up,zp,Gp,Yp,In=ue(()=>{X();Z();Q();ju=__STORYBOOK_COMPONENTS__,{A:Mu,ActionBar:Du,AddonPanel:Fu,Badge:Bu,Bar:Wu,Blockquote:Hu,Button:Uu,Checkbox:zu,ClipboardCode:Gu,Code:Yu,DL:qu,Div:$u,DocumentWrapper:Vu,EmptyTabContent:Ku,ErrorFormatter:Ju,FlexBar:Xu,Form:Zu,H1:Qu,H2:ep,H3:tp,H4:np,H5:rp,H6:op,HR:ip,IconButton:wn,Img:ap,LI:sp,Link:lp,ListItem:cp,Loader:up,Modal:pp,OL:fp,P:dp,Placeholder:hp,Pre:mp,ProgressSpinner:yp,ResetWrapper:gp,ScrollArea:bp,Separator:vp,Spaced:Ep,Span:Op,StorybookIcon:Sp,StorybookLogo:wp,SyntaxHighlighter:Tn,TT:Tp,TabBar:Ip,TabButton:Cp,TabWrapper:Pp,Table:Rp,Tabs:xp,TabsState:_p,TooltipLinkList:Ap,TooltipMessage:Np,TooltipNote:kp,UL:Lp,WithTooltip:jp,WithTooltipPure:Mp,Zoom:Dp,codeCommon:Fp,components:Bp,createCopyToClipboardFunction:Wp,getStoryHref:Hp,interleaveSeparators:Up,nameSpaceClassNames:zp,resetComponents:Gp,withReset:Yp}=__STORYBOOK_COMPONENTS__});var Jp,Xp,Zp,Qp,zt,ef,wt,Gt,tf,nf,rf,of,af,sf,lf,cf,uf,pf,nt,ff,te,Cn,df,Pn,hf,Rn=ue(()=>{X();Z();Q();Jp=__STORYBOOK_THEMING__,{CacheProvider:Xp,ClassNames:Zp,Global:Qp,ThemeProvider:zt,background:ef,color:wt,convert:Gt,create:tf,createCache:nf,createGlobal:rf,createReset:of,css:af,darken:sf,ensure:lf,ignoreSsrWarning:cf,isPropValid:uf,jsx:pf,keyframes:nt,lighten:ff,styled:te,themes:Cn,typography:df,useTheme:Pn,withTheme:hf}=__STORYBOOK_THEMING__});var vf,Ef,Of,Sf,wf,Tf,If,Cf,Pf,Rf,xf,_f,Af,Nf,kf,xn,Lf,jf,Mf,Df,Ff,Bf,Wf,Hf,Uf,zf,Gf,Yf,qf,$f,Vf,Kf,Jf,Xf,Zf,Qf,ed,td,nd,rd,od,id,ad,sd,ld,cd,ud,pd,fd,dd,hd,md,yd,gd,bd,vd,Ed,Od,Sd,wd,Td,Id,Cd,_n,Pd,Rd,xd,_d,Ad,Nd,kd,Ld,jd,Md,Dd,Fd,Bd,Wd,Hd,Ud,zd,Gd,Yd,qd,$d,Vd,Kd,Jd,Xd,Zd,Qd,eh,th,nh,rh,oh,ih,ah,sh,lh,ch,uh,ph,fh,dh,hh,mh,yh,gh,bh,vh,Eh,Oh,Sh,wh,Th,Ih,Ch,Ph,Rh,xh,_h,Ah,Nh,kh,Lh,jh,Mh,Dh,Fh,Bh,Wh,Hh,Uh,zh,Gh,Yh,qh,$h,Vh,Kh,Jh,Xh,Zh,Qh,em,tm,nm,rm,om,im,am,sm,lm,cm,um,pm,fm,dm,hm,mm,ym,gm,bm,vm,Em,Om,Sm,wm,Tm,Im,Cm,Pm,Rm,xm,_m,Am,Nm,km,Lm,jm,Mm,Dm,Fm,Bm,Wm,Hm,Um,zm,Gm,Ym,qm,$m,Vm,Km,Jm,Xm,Zm,Qm,ey,ty,ny,ry,oy,iy,ay,sy,ly,cy,uy,py,fy,dy,hy,my,yy,gy,by,vy,Ey,Oy,Sy,wy,Ty,Iy,Cy,Py,Ry,xy,_y,Ay,Ny,ky,Ly,jy,My,Dy,Fy,By,Wy,Hy,Uy,zy,Gy,Yy,qy,$y,An=ue(()=>{X();Z();Q();vf=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Ef,AccessibilityIcon:Of,AccessibilityIgnoredIcon:Sf,AddIcon:wf,AdminIcon:Tf,AlertAltIcon:If,AlertIcon:Cf,AlignLeftIcon:Pf,AlignRightIcon:Rf,AppleIcon:xf,ArrowBottomLeftIcon:_f,ArrowBottomRightIcon:Af,ArrowDownIcon:Nf,ArrowLeftIcon:kf,ArrowRightIcon:xn,ArrowSolidDownIcon:Lf,ArrowSolidLeftIcon:jf,ArrowSolidRightIcon:Mf,ArrowSolidUpIcon:Df,ArrowTopLeftIcon:Ff,ArrowTopRightIcon:Bf,ArrowUpIcon:Wf,AzureDevOpsIcon:Hf,BackIcon:Uf,BasketIcon:zf,BatchAcceptIcon:Gf,BatchDenyIcon:Yf,BeakerIcon:qf,BellIcon:$f,BitbucketIcon:Vf,BoldIcon:Kf,BookIcon:Jf,BookmarkHollowIcon:Xf,BookmarkIcon:Zf,BottomBarIcon:Qf,BottomBarToggleIcon:ed,BoxIcon:td,BranchIcon:nd,BrowserIcon:rd,ButtonIcon:od,CPUIcon:id,CalendarIcon:ad,CameraIcon:sd,CameraStabilizeIcon:ld,CategoryIcon:cd,CertificateIcon:ud,ChangedIcon:pd,ChatIcon:fd,CheckIcon:dd,ChevronDownIcon:hd,ChevronLeftIcon:md,ChevronRightIcon:yd,ChevronSmallDownIcon:gd,ChevronSmallLeftIcon:bd,ChevronSmallRightIcon:vd,ChevronSmallUpIcon:Ed,ChevronUpIcon:Od,ChromaticIcon:Sd,ChromeIcon:wd,CircleHollowIcon:Td,CircleIcon:Id,ClearIcon:Cd,CloseAltIcon:_n,CloseIcon:Pd,CloudHollowIcon:Rd,CloudIcon:xd,CogIcon:_d,CollapseIcon:Ad,CommandIcon:Nd,CommentAddIcon:kd,CommentIcon:Ld,CommentsIcon:jd,CommitIcon:Md,CompassIcon:Dd,ComponentDrivenIcon:Fd,ComponentIcon:Bd,ContrastIcon:Wd,ContrastIgnoredIcon:Hd,ControlsIcon:Ud,CopyIcon:zd,CreditIcon:Gd,CrossIcon:Yd,DashboardIcon:qd,DatabaseIcon:$d,DeleteIcon:Vd,DiamondIcon:Kd,DirectionIcon:Jd,DiscordIcon:Xd,DocChartIcon:Zd,DocListIcon:Qd,DocumentIcon:eh,DownloadIcon:th,DragIcon:nh,EditIcon:rh,EllipsisIcon:oh,EmailIcon:ih,ExpandAltIcon:ah,ExpandIcon:sh,EyeCloseIcon:lh,EyeIcon:ch,FaceHappyIcon:uh,FaceNeutralIcon:ph,FaceSadIcon:fh,FacebookIcon:dh,FailedIcon:hh,FastForwardIcon:mh,FigmaIcon:yh,FilterIcon:gh,FlagIcon:bh,FolderIcon:vh,FormIcon:Eh,GDriveIcon:Oh,GithubIcon:Sh,GitlabIcon:wh,GlobeIcon:Th,GoogleIcon:Ih,GraphBarIcon:Ch,GraphLineIcon:Ph,GraphqlIcon:Rh,GridAltIcon:xh,GridIcon:_h,GrowIcon:Ah,HeartHollowIcon:Nh,HeartIcon:kh,HomeIcon:Lh,HourglassIcon:jh,InfoIcon:Mh,ItalicIcon:Dh,JumpToIcon:Fh,KeyIcon:Bh,LightningIcon:Wh,LightningOffIcon:Hh,LinkBrokenIcon:Uh,LinkIcon:zh,LinkedinIcon:Gh,LinuxIcon:Yh,ListOrderedIcon:qh,ListUnorderedIcon:$h,LocationIcon:Vh,LockIcon:Kh,MarkdownIcon:Jh,MarkupIcon:Xh,MediumIcon:Zh,MemoryIcon:Qh,MenuIcon:em,MergeIcon:tm,MirrorIcon:nm,MobileIcon:rm,MoonIcon:om,NutIcon:im,OutboxIcon:am,OutlineIcon:sm,PaintBrushIcon:lm,PaperClipIcon:cm,ParagraphIcon:um,PassedIcon:pm,PhoneIcon:fm,PhotoDragIcon:dm,PhotoIcon:hm,PhotoStabilizeIcon:mm,PinAltIcon:ym,PinIcon:gm,PlayAllHollowIcon:bm,PlayBackIcon:vm,PlayHollowIcon:Em,PlayIcon:Om,PlayNextIcon:Sm,PlusIcon:wm,PointerDefaultIcon:Tm,PointerHandIcon:Im,PowerIcon:Cm,PrintIcon:Pm,ProceedIcon:Rm,ProfileIcon:xm,PullRequestIcon:_m,QuestionIcon:Am,RSSIcon:Nm,RedirectIcon:km,ReduxIcon:Lm,RefreshIcon:jm,ReplyIcon:Mm,RepoIcon:Dm,RequestChangeIcon:Fm,RewindIcon:Bm,RulerIcon:Wm,SaveIcon:Hm,SearchIcon:Um,ShareAltIcon:zm,ShareIcon:Gm,ShieldIcon:Ym,SideBySideIcon:qm,SidebarAltIcon:$m,SidebarAltToggleIcon:Vm,SidebarIcon:Km,SidebarToggleIcon:Jm,SpeakerIcon:Xm,StackedIcon:Zm,StarHollowIcon:Qm,StarIcon:ey,StatusFailIcon:ty,StatusIcon:ny,StatusPassIcon:ry,StatusWarnIcon:oy,StickerIcon:iy,StopAltHollowIcon:ay,StopAltIcon:sy,StopIcon:ly,StorybookIcon:cy,StructureIcon:uy,SubtractIcon:py,SunIcon:fy,SupportIcon:dy,SweepIcon:hy,SwitchAltIcon:my,SyncIcon:yy,TabletIcon:gy,ThumbsUpIcon:by,TimeIcon:vy,TimerIcon:Ey,TransferIcon:Oy,TrashIcon:Sy,TwitterIcon:wy,TypeIcon:Ty,UbuntuIcon:Iy,UndoIcon:Cy,UnfoldIcon:Py,UnlockIcon:Ry,UnpinIcon:xy,UploadIcon:_y,UserAddIcon:Ay,UserAltIcon:Ny,UserIcon:ky,UsersIcon:Ly,VSCodeIcon:jy,VerifiedIcon:My,VideoIcon:Dy,WandIcon:Fy,WatchIcon:By,WindowsIcon:Wy,WrenchIcon:Hy,XIcon:Uy,YoutubeIcon:zy,ZoomIcon:Gy,ZoomOutIcon:Yy,ZoomResetIcon:qy,iconList:$y}=__STORYBOOK_ICONS__});var yo={};vo(yo,{default:()=>Ds});function Do(e,t={}){let{colors:n=kn,duration:r=Ln,force:o=jn,particleCount:i=Mn,particleShape:a=Dn,particleSize:s=Fn,particleClass:c=Bn,destroyAfterDone:l=Wn,stageHeight:p=Hn,stageWidth:u=Un}=t;(function(m){if(document.querySelector("style[data-neoconfetti]"))return;let v=Yt("style");v.dataset.neoconfetti="",v.textContent=m,qt(document.head,v)})(jo),e.classList.add(Mo),e.style.setProperty("--sh",p+"px");let f=[],h=[],d=()=>lt(Ee()*(Uo-1)),g=(m,v)=>a!=="rectangles"&&(m==="circles"||zo(v));function S(m,v){let w=d(),U=g(a,w),x=(Ie,je)=>m.style.setProperty(Ie,je+"");x("--xlp",$t(Tt(qn(v,90)-180),0,180,-u/2,u/2)+"px"),x("--dc",r-lt(1e3*Ee())+"ms");let z=Ee()<Bo?ze(Ee()*Wo,2):0;x("--x1",z),x("--x2",-1*z),x("--x3",z),x("--x4",ze(Tt($t(Tt(qn(v,90)-180),0,180,-1,1)),4)),x("--y1",ze(Ee()*Gn,4)),x("--y2",ze(Ee()*o*(Nr()?1:-1),4)),x("--y3",Gn),x("--y4",ze(Ho($t(Tt(v-180),0,180,o,-o),0),4)),x("--w",(U?s:lt(4*Ee())+s/2)+"px"),x("--h",(U?s:lt(2*Ee())+s)+"px");let ee=w.toString(2).padStart(3,"0").split("");x("--hr",ee.map(Ie=>+Ie/2+"").join(" ")),x("--r",ee.join(" ")),x("--rd",ze(Ee()*(Fo-zn)+zn)+"ms"),x("--br",U?"50%":0)}let b;function R(){e.innerHTML="",clearTimeout(b),f=Yn(i,n),h=function(m,v=[],w){let U=[];for(let{color:x}of v){let z=Yt("div");z.className=`${Nn} ${w}`,z.style.setProperty("--bgc",x);let ee=Yt("div");qt(z,ee),qt(m,z),U.push(z)}return U}(e,f,c);for(let[m,v]of $n(h))S(v,f[+m].degree);b=setTimeout(()=>{l&&(e.innerHTML="")},r)}return R(),{update(m){let v=m.particleCount??Mn,w=m.particleShape??Dn,U=m.particleSize??Fn,x=m.particleClass??Bn,z=m.colors??kn,ee=m.stageHeight??Hn,Ie=m.duration??Ln,je=m.force??jn,Qe=m.stageWidth??Un,Me=m.destroyAfterDone??Wn;f=Yn(v,z);let Ue=!1;if(v===i){h=Array.from(e.querySelectorAll(`.${Nn}`));for(let[et,{color:xe}]of $n(f)){let _e=h[+et];JSON.stringify(n)!==JSON.stringify(z)&&_e.style.setProperty("--bgc",xe),w!==a&&_e.style.setProperty("--br",g(w,d())?"50%":"0"),x!==c&&(c&&_e.classList.remove(c),x&&_e.classList.add(x))}}else Ue=!0;l&&!Me&&clearTimeout(b),e.style.setProperty("--sh",ee+"px"),r=Ie,n=z,o=je,i=v,a=w,s=U,c=x,l=Me,p=ee,u=Qe,Ue&&R()},destroy(){e.innerHTML="",clearTimeout(b)}}}function Go({class:e,...t}){let n=Wt(null),r=Wt();return ae(()=>{if(typeof window<"u"&&n.current){if(r.current)return r.current.update(t),r.current.destroy;r.current=Do(n.current,t)}},[t]),V("div",{ref:n,className:e})}function Vn({targetSelector:e,pulsating:t=!1}){return ae(()=>{let n=document.querySelector(e);if(n)if(t){n.style.animation="pulsate 3s infinite",n.style.transformOrigin="center",n.style.animationTimingFunction="ease-in-out";let r=`
        @keyframes pulsate {
          0% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0.7), 0 0 0 0 rgba(2, 156, 253, 0.4);
          }
          50% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 20px rgba(2, 156, 253, 0), 0 0 0 40px rgba(2, 156, 253, 0);
          }
          100% {
            box-shadow: rgba(2,156,253,1) 0 0 2px 1px, 0 0 0 0 rgba(2, 156, 253, 0), 0 0 0 0 rgba(2, 156, 253, 0);
          }
        }
      `,o=document.createElement("style");o.id="sb-onboarding-pulsating-effect",o.innerHTML=r,document.head.appendChild(o)}else n.style.boxShadow="rgba(2,156,253,1) 0 0 2px 1px";return()=>{let r=document.querySelector("#sb-onboarding-pulsating-effect");r&&r.remove(),n&&(n.style.animation="",n.style.boxShadow="")}},[e,t]),null}function kr(e){return t=>typeof t===e}function Jo(e,t){let{length:n}=e;if(n!==t.length)return!1;for(let r=n;r--!==0;)if(!oe(e[r],t[r]))return!1;return!0}function Xo(e,t){if(e.byteLength!==t.byteLength)return!1;let n=new DataView(e.buffer),r=new DataView(t.buffer),o=e.byteLength;for(;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}function Zo(e,t){if(e.size!==t.size)return!1;for(let n of e.entries())if(!t.has(n[0]))return!1;for(let n of e.entries())if(!oe(n[1],t.get(n[0])))return!1;return!0}function Qo(e,t){if(e.size!==t.size)return!1;for(let n of e.entries())if(!t.has(n[0]))return!1;return!0}function oe(e,t){if(e===t)return!0;if(e&&Xn(e)&&t&&Xn(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return Jo(e,t);if(e instanceof Map&&t instanceof Map)return Zo(e,t);if(e instanceof Set&&t instanceof Set)return Qo(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return Xo(e,t);if(Jn(e)&&Jn(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let o=n.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,n[o]))return!1;for(let o=n.length;o--!==0;){let i=n[o];if(!(i==="_owner"&&e.$$typeof)&&!oe(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function kt(e){let t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(ni(t))return t}function de(e){return t=>kt(t)===e}function ni(e){return ei.includes(e)}function Ke(e){return t=>typeof t===e}function ri(e){return ti.includes(e)}function I(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}return I.array(e)?"Array":I.plainFunction(e)?"Function":kt(e)||"Object"}function ii(...e){return e.every(t=>_.string(t)||_.array(t)||_.plainObject(t))}function ai(e,t,n){return Lr(e,t)?[e,t].every(_.array)?!e.some(nr(n))&&t.some(nr(n)):[e,t].every(_.plainObject)?!Object.entries(e).some(tr(n))&&Object.entries(t).some(tr(n)):t===n:!1}function Zn(e,t,n){let{actual:r,key:o,previous:i,type:a}=n,s=we(e,o),c=we(t,o),l=[s,c].every(_.number)&&(a==="increased"?s<c:s>c);return _.undefined(r)||(l=l&&c===r),_.undefined(i)||(l=l&&s===i),l}function Qn(e,t,n){let{key:r,type:o,value:i}=n,a=we(e,r),s=we(t,r),c=o==="added"?a:s,l=o==="added"?s:a;if(!_.nullOrUndefined(i)){if(_.defined(c)){if(_.array(c)||_.plainObject(c))return ai(c,l,i)}else return oe(l,i);return!1}return[a,s].every(_.array)?!l.every(an(c)):[a,s].every(_.plainObject)?si(Object.keys(c),Object.keys(l)):![a,s].every(p=>_.primitive(p)&&_.defined(p))&&(o==="added"?!_.defined(a)&&_.defined(s):_.defined(a)&&!_.defined(s))}function er(e,t,{key:n}={}){let r=we(e,n),o=we(t,n);if(!Lr(r,o))throw new TypeError("Inputs have different types");if(!ii(r,o))throw new TypeError("Inputs don't have length");return[r,o].every(_.plainObject)&&(r=Object.keys(r),o=Object.keys(o)),[r,o]}function tr(e){return([t,n])=>_.array(e)?oe(e,n)||e.some(r=>oe(r,n)||_.array(n)&&an(n)(r)):_.plainObject(e)&&e[t]?!!e[t]&&oe(e[t],n):oe(e,n)}function si(e,t){return t.some(n=>!e.includes(n))}function nr(e){return t=>_.array(e)?e.some(n=>oe(n,t)||_.array(t)&&an(t)(n)):oe(e,t)}function rt(e,t){return _.array(e)?e.some(n=>oe(n,t)):oe(e,t)}function an(e){return t=>e.some(n=>oe(n,t))}function Lr(...e){return e.every(_.array)||e.every(_.number)||e.every(_.plainObject)||e.every(_.string)}function we(e,t){return _.plainObject(e)||_.array(e)?_.string(t)?t.split(".").reduce((n,r)=>n&&n[r],e):_.number(t)?e[t]:e:e}function xt(e,t){if([e,t].some(_.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(n=>_.plainObject(n)||_.array(n)))throw new Error("Expected plain objects or array");return{added:(n,r)=>{try{return Qn(e,t,{key:n,type:"added",value:r})}catch{return!1}},changed:(n,r,o)=>{try{let i=we(e,n),a=we(t,n),s=_.defined(r),c=_.defined(o);if(s||c){let l=c?rt(o,i):!rt(r,i),p=rt(r,a);return l&&p}return[i,a].every(_.array)||[i,a].every(_.plainObject)?!oe(i,a):i!==a}catch{return!1}},changedFrom:(n,r,o)=>{if(!_.defined(n))return!1;try{let i=we(e,n),a=we(t,n),s=_.defined(o);return rt(r,i)&&(s?rt(o,a):!s)}catch{return!1}},decreased:(n,r,o)=>{if(!_.defined(n))return!1;try{return Zn(e,t,{key:n,actual:r,previous:o,type:"decreased"})}catch{return!1}},emptied:n=>{try{let[r,o]=er(e,t,{key:n});return!!r.length&&!o.length}catch{return!1}},filled:n=>{try{let[r,o]=er(e,t,{key:n});return!r.length&&!!o.length}catch{return!1}},increased:(n,r,o)=>{if(!_.defined(n))return!1;try{return Zn(e,t,{key:n,actual:r,previous:o,type:"increased"})}catch{return!1}},removed:(n,r)=>{try{return Qn(e,t,{key:n,type:"removed",value:r})}catch{return!1}}}}function ui(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}function pi(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},ci))}}function Mr(e){var t={};return e&&t.toString.call(e)==="[object Function]"}function He(e,t){if(e.nodeType!==1)return[];var n=e.ownerDocument.defaultView,r=n.getComputedStyle(e,null);return t?r[t]:r}function sn(e){return e.nodeName==="HTML"?e:e.parentNode||e.host}function pt(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=He(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/(auto|scroll|overlay)/.test(n+o+r)?e:pt(sn(e))}function Dr(e){return e&&e.referenceNode?e.referenceNode:e}function Je(e){return e===11?or:e===10?ir:or||ir}function Ye(e){if(!e)return document.documentElement;for(var t=Je(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var r=n&&n.nodeName;return!r||r==="BODY"||r==="HTML"?e?e.ownerDocument.documentElement:document.documentElement:["TH","TD","TABLE"].indexOf(n.nodeName)!==-1&&He(n,"position")==="static"?Ye(n):n}function hi(e){var t=e.nodeName;return t==="BODY"?!1:t==="HTML"||Ye(e.firstElementChild)===e}function Xt(e){return e.parentNode!==null?Xt(e.parentNode):e}function _t(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?e:t,o=n?t:e,i=document.createRange();i.setStart(r,0),i.setEnd(o,0);var a=i.commonAncestorContainer;if(e!==a&&t!==a||r.contains(o))return hi(a)?a:Ye(a);var s=Xt(e);return s.host?_t(s.host,t):_t(e,Xt(t).host)}function qe(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"top",n=t==="top"?"scrollTop":"scrollLeft",r=e.nodeName;if(r==="BODY"||r==="HTML"){var o=e.ownerDocument.documentElement,i=e.ownerDocument.scrollingElement||o;return i[n]}return e[n]}function mi(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=qe(t,"top"),o=qe(t,"left"),i=n?-1:1;return e.top+=r*i,e.bottom+=r*i,e.left+=o*i,e.right+=o*i,e}function ar(e,t){var n=t==="x"?"Left":"Top",r=n==="Left"?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+r+"Width"])}function sr(e,t,n,r){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],Je(10)?parseInt(n["offset"+e])+parseInt(r["margin"+(e==="Height"?"Top":"Left")])+parseInt(r["margin"+(e==="Height"?"Bottom":"Right")]):0)}function Fr(e){var t=e.body,n=e.documentElement,r=Je(10)&&getComputedStyle(n);return{height:sr("Height",t,n,r),width:sr("Width",t,n,r)}}function ke(e){return le({},e,{right:e.left+e.width,bottom:e.top+e.height})}function Zt(e){var t={};try{if(Je(10)){t=e.getBoundingClientRect();var n=qe(e,"top"),r=qe(e,"left");t.top+=n,t.left+=r,t.bottom+=n,t.right+=r}else t=e.getBoundingClientRect()}catch{}var o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i=e.nodeName==="HTML"?Fr(e.ownerDocument):{},a=i.width||e.clientWidth||o.width,s=i.height||e.clientHeight||o.height,c=e.offsetWidth-a,l=e.offsetHeight-s;if(c||l){var p=He(e);c-=ar(p,"x"),l-=ar(p,"y"),o.width-=c,o.height-=l}return ke(o)}function ln(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=Je(10),o=t.nodeName==="HTML",i=Zt(e),a=Zt(t),s=pt(e),c=He(t),l=parseFloat(c.borderTopWidth),p=parseFloat(c.borderLeftWidth);n&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var u=ke({top:i.top-a.top-l,left:i.left-a.left-p,width:i.width,height:i.height});if(u.marginTop=0,u.marginLeft=0,!r&&o){var f=parseFloat(c.marginTop),h=parseFloat(c.marginLeft);u.top-=l-f,u.bottom-=l-f,u.left-=p-h,u.right-=p-h,u.marginTop=f,u.marginLeft=h}return(r&&!n?t.contains(s):t===s&&s.nodeName!=="BODY")&&(u=mi(u,t)),u}function bi(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=e.ownerDocument.documentElement,r=ln(e,n),o=Math.max(n.clientWidth,window.innerWidth||0),i=Math.max(n.clientHeight,window.innerHeight||0),a=t?0:qe(n),s=t?0:qe(n,"left"),c={top:a-r.top+r.marginTop,left:s-r.left+r.marginLeft,width:o,height:i};return ke(c)}function Br(e){var t=e.nodeName;if(t==="BODY"||t==="HTML")return!1;if(He(e,"position")==="fixed")return!0;var n=sn(e);return n?Br(n):!1}function Wr(e){if(!e||!e.parentElement||Je())return document.documentElement;for(var t=e.parentElement;t&&He(t,"transform")==="none";)t=t.parentElement;return t||document.documentElement}function cn(e,t,n,r){var o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,i={top:0,left:0},a=o?Wr(e):_t(e,Dr(t));if(r==="viewport")i=bi(a,o);else{var s=void 0;r==="scrollParent"?(s=pt(sn(t)),s.nodeName==="BODY"&&(s=e.ownerDocument.documentElement)):r==="window"?s=e.ownerDocument.documentElement:s=r;var c=ln(s,a,o);if(s.nodeName==="HTML"&&!Br(a)){var l=Fr(e.ownerDocument),p=l.height,u=l.width;i.top+=c.top-c.marginTop,i.bottom=p+c.top,i.left+=c.left-c.marginLeft,i.right=u+c.left}else i=c}n=n||0;var f=typeof n=="number";return i.left+=f?n:n.left||0,i.top+=f?n:n.top||0,i.right-=f?n:n.right||0,i.bottom-=f?n:n.bottom||0,i}function vi(e){var t=e.width,n=e.height;return t*n}function Hr(e,t,n,r,o){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:0;if(e.indexOf("auto")===-1)return e;var a=cn(n,r,i,o),s={top:{width:a.width,height:t.top-a.top},right:{width:a.right-t.right,height:a.height},bottom:{width:a.width,height:a.bottom-t.bottom},left:{width:t.left-a.left,height:a.height}},c=Object.keys(s).map(function(f){return le({key:f},s[f],{area:vi(s[f])})}).sort(function(f,h){return h.area-f.area}),l=c.filter(function(f){var h=f.width,d=f.height;return h>=n.clientWidth&&d>=n.clientHeight}),p=l.length>0?l[0].key:c[0].key,u=e.split("-")[1];return p+(u?"-"+u:"")}function Ur(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,o=r?Wr(t):_t(t,Dr(n));return ln(n,o,r)}function zr(e){var t=e.ownerDocument.defaultView,n=t.getComputedStyle(e),r=parseFloat(n.marginTop||0)+parseFloat(n.marginBottom||0),o=parseFloat(n.marginLeft||0)+parseFloat(n.marginRight||0),i={width:e.offsetWidth+o,height:e.offsetHeight+r};return i}function At(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(n){return t[n]})}function Gr(e,t,n){n=n.split("-")[0];var r=zr(e),o={width:r.width,height:r.height},i=["right","left"].indexOf(n)!==-1,a=i?"top":"left",s=i?"left":"top",c=i?"height":"width",l=i?"width":"height";return o[a]=t[a]+t[c]/2-r[c]/2,n===s?o[s]=t[s]-r[l]:o[s]=t[At(s)],o}function ft(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Ei(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(o){return o[t]===n});var r=ft(e,function(o){return o[t]===n});return e.indexOf(r)}function Yr(e,t,n){var r=n===void 0?e:e.slice(0,Ei(e,"name",n));return r.forEach(function(o){o.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=o.function||o.fn;o.enabled&&Mr(i)&&(t.offsets.popper=ke(t.offsets.popper),t.offsets.reference=ke(t.offsets.reference),t=i(t,o))}),t}function Oi(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=Ur(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=Hr(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=Gr(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=Yr(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function qr(e,t){return e.some(function(n){var r=n.name,o=n.enabled;return o&&r===t})}function un(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),r=0;r<t.length;r++){var o=t[r],i=o?""+o+n:e;if(typeof document.body.style[i]<"u")return i}return null}function Si(){return this.state.isDestroyed=!0,qr(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[un("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function $r(e){var t=e.ownerDocument;return t?t.defaultView:window}function Vr(e,t,n,r){var o=e.nodeName==="BODY",i=o?e.ownerDocument.defaultView:e;i.addEventListener(t,n,{passive:!0}),o||Vr(pt(i.parentNode),t,n,r),r.push(i)}function wi(e,t,n,r){n.updateBound=r,$r(e).addEventListener("resize",n.updateBound,{passive:!0});var o=pt(e);return Vr(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function Ti(){this.state.eventsEnabled||(this.state=wi(this.reference,this.options,this.state,this.scheduleUpdate))}function Ii(e,t){return $r(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(n){n.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function Ci(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=Ii(this.reference,this.state))}function pn(e){return e!==""&&!isNaN(parseFloat(e))&&isFinite(e)}function Qt(e,t){Object.keys(t).forEach(function(n){var r="";["width","height","top","right","bottom","left"].indexOf(n)!==-1&&pn(t[n])&&(r="px"),e.style[n]=t[n]+r})}function Pi(e,t){Object.keys(t).forEach(function(n){var r=t[n];r!==!1?e.setAttribute(n,t[n]):e.removeAttribute(n)})}function Ri(e){return Qt(e.instance.popper,e.styles),Pi(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&Qt(e.arrowElement,e.arrowStyles),e}function xi(e,t,n,r,o){var i=Ur(o,t,e,n.positionFixed),a=Hr(n.placement,i,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",a),Qt(t,{position:n.positionFixed?"fixed":"absolute"}),n}function _i(e,t){var n=e.offsets,r=n.popper,o=n.reference,i=Math.round,a=Math.floor,s=function(S){return S},c=i(o.width),l=i(r.width),p=["left","right"].indexOf(e.placement)!==-1,u=e.placement.indexOf("-")!==-1,f=c%2===l%2,h=c%2===1&&l%2===1,d=t?p||u||f?i:a:s,g=t?i:s;return{left:d(h&&!u&&t?r.left-1:r.left),top:g(r.top),bottom:g(r.bottom),right:d(r.right)}}function Ni(e,t){var n=t.x,r=t.y,o=e.offsets.popper,i=ft(e.instance.modifiers,function(m){return m.name==="applyStyle"}).gpuAcceleration;i!==void 0&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=i!==void 0?i:t.gpuAcceleration,s=Ye(e.instance.popper),c=Zt(s),l={position:o.position},p=_i(e,window.devicePixelRatio<2||!Ai),u=n==="bottom"?"top":"bottom",f=r==="right"?"left":"right",h=un("transform"),d=void 0,g=void 0;if(u==="bottom"?s.nodeName==="HTML"?g=-s.clientHeight+p.bottom:g=-c.height+p.bottom:g=p.top,f==="right"?s.nodeName==="HTML"?d=-s.clientWidth+p.right:d=-c.width+p.right:d=p.left,a&&h)l[h]="translate3d("+d+"px, "+g+"px, 0)",l[u]=0,l[f]=0,l.willChange="transform";else{var S=u==="bottom"?-1:1,b=f==="right"?-1:1;l[u]=g*S,l[f]=d*b,l.willChange=u+", "+f}var R={"x-placement":e.placement};return e.attributes=le({},R,e.attributes),e.styles=le({},l,e.styles),e.arrowStyles=le({},e.offsets.arrow,e.arrowStyles),e}function Kr(e,t,n){var r=ft(e,function(s){var c=s.name;return c===t}),o=!!r&&e.some(function(s){return s.name===n&&s.enabled&&s.order<r.order});if(!o){var i="`"+t+"`",a="`"+n+"`";console.warn(a+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")}return o}function ki(e,t){var n;if(!Kr(e.instance.modifiers,"arrow","keepTogether"))return e;var r=t.element;if(typeof r=="string"){if(r=e.instance.popper.querySelector(r),!r)return e}else if(!e.instance.popper.contains(r))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var o=e.placement.split("-")[0],i=e.offsets,a=i.popper,s=i.reference,c=["left","right"].indexOf(o)!==-1,l=c?"height":"width",p=c?"Top":"Left",u=p.toLowerCase(),f=c?"left":"top",h=c?"bottom":"right",d=zr(r)[l];s[h]-d<a[u]&&(e.offsets.popper[u]-=a[u]-(s[h]-d)),s[u]+d>a[h]&&(e.offsets.popper[u]+=s[u]+d-a[h]),e.offsets.popper=ke(e.offsets.popper);var g=s[u]+s[l]/2-d/2,S=He(e.instance.popper),b=parseFloat(S["margin"+p]),R=parseFloat(S["border"+p+"Width"]),m=g-e.offsets.popper[u]-b-R;return m=Math.max(Math.min(a[l]-d,m),0),e.arrowElement=r,e.offsets.arrow=(n={},$e(n,u,Math.round(m)),$e(n,f,""),n),e}function Li(e){return e==="end"?"start":e==="start"?"end":e}function lr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=Vt.indexOf(e),r=Vt.slice(n+1).concat(Vt.slice(0,n));return t?r.reverse():r}function ji(e,t){if(qr(e.instance.modifiers,"inner")||e.flipped&&e.placement===e.originalPlacement)return e;var n=cn(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),r=e.placement.split("-")[0],o=At(r),i=e.placement.split("-")[1]||"",a=[];switch(t.behavior){case Kt.FLIP:a=[r,o];break;case Kt.CLOCKWISE:a=lr(r);break;case Kt.COUNTERCLOCKWISE:a=lr(r,!0);break;default:a=t.behavior}return a.forEach(function(s,c){if(r!==s||a.length===c+1)return e;r=e.placement.split("-")[0],o=At(r);var l=e.offsets.popper,p=e.offsets.reference,u=Math.floor,f=r==="left"&&u(l.right)>u(p.left)||r==="right"&&u(l.left)<u(p.right)||r==="top"&&u(l.bottom)>u(p.top)||r==="bottom"&&u(l.top)<u(p.bottom),h=u(l.left)<u(n.left),d=u(l.right)>u(n.right),g=u(l.top)<u(n.top),S=u(l.bottom)>u(n.bottom),b=r==="left"&&h||r==="right"&&d||r==="top"&&g||r==="bottom"&&S,R=["top","bottom"].indexOf(r)!==-1,m=!!t.flipVariations&&(R&&i==="start"&&h||R&&i==="end"&&d||!R&&i==="start"&&g||!R&&i==="end"&&S),v=!!t.flipVariationsByContent&&(R&&i==="start"&&d||R&&i==="end"&&h||!R&&i==="start"&&S||!R&&i==="end"&&g),w=m||v;(f||b||w)&&(e.flipped=!0,(f||b)&&(r=a[c+1]),w&&(i=Li(i)),e.placement=r+(i?"-"+i:""),e.offsets.popper=le({},e.offsets.popper,Gr(e.instance.popper,e.offsets.reference,e.placement)),e=Yr(e.instance.modifiers,e,"flip"))}),e}function Mi(e){var t=e.offsets,n=t.popper,r=t.reference,o=e.placement.split("-")[0],i=Math.floor,a=["top","bottom"].indexOf(o)!==-1,s=a?"right":"bottom",c=a?"left":"top",l=a?"width":"height";return n[s]<i(r[c])&&(e.offsets.popper[c]=i(r[c])-n[l]),n[c]>i(r[s])&&(e.offsets.popper[c]=i(r[s])),e}function Di(e,t,n,r){var o=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),i=+o[1],a=o[2];if(!i)return e;if(a.indexOf("%")===0){var s=void 0;switch(a){case"%p":s=n;break;case"%":case"%r":default:s=r}var c=ke(s);return c[t]/100*i}else if(a==="vh"||a==="vw"){var l=void 0;return a==="vh"?l=Math.max(document.documentElement.clientHeight,window.innerHeight||0):l=Math.max(document.documentElement.clientWidth,window.innerWidth||0),l/100*i}else return i}function Fi(e,t,n,r){var o=[0,0],i=["right","left"].indexOf(r)!==-1,a=e.split(/(\+|\-)/).map(function(p){return p.trim()}),s=a.indexOf(ft(a,function(p){return p.search(/,|\s/)!==-1}));a[s]&&a[s].indexOf(",")===-1&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var c=/\s*,\s*|\s+/,l=s!==-1?[a.slice(0,s).concat([a[s].split(c)[0]]),[a[s].split(c)[1]].concat(a.slice(s+1))]:[a];return l=l.map(function(p,u){var f=(u===1?!i:i)?"height":"width",h=!1;return p.reduce(function(d,g){return d[d.length-1]===""&&["+","-"].indexOf(g)!==-1?(d[d.length-1]=g,h=!0,d):h?(d[d.length-1]+=g,h=!1,d):d.concat(g)},[]).map(function(d){return Di(d,f,t,n)})}),l.forEach(function(p,u){p.forEach(function(f,h){pn(f)&&(o[u]+=f*(p[h-1]==="-"?-1:1))})}),o}function Bi(e,t){var n=t.offset,r=e.placement,o=e.offsets,i=o.popper,a=o.reference,s=r.split("-")[0],c=void 0;return pn(+n)?c=[+n,0]:c=Fi(n,i,a,s),s==="left"?(i.top+=c[0],i.left-=c[1]):s==="right"?(i.top+=c[0],i.left+=c[1]):s==="top"?(i.left+=c[0],i.top-=c[1]):s==="bottom"&&(i.left+=c[0],i.top+=c[1]),e.popper=i,e}function Wi(e,t){var n=t.boundariesElement||Ye(e.instance.popper);e.instance.reference===n&&(n=Ye(n));var r=un("transform"),o=e.instance.popper.style,i=o.top,a=o.left,s=o[r];o.top="",o.left="",o[r]="";var c=cn(e.instance.popper,e.instance.reference,t.padding,n,e.positionFixed);o.top=i,o.left=a,o[r]=s,t.boundaries=c;var l=t.priority,p=e.offsets.popper,u={primary:function(f){var h=p[f];return p[f]<c[f]&&!t.escapeWithReference&&(h=Math.max(p[f],c[f])),$e({},f,h)},secondary:function(f){var h=f==="right"?"left":"top",d=p[h];return p[f]>c[f]&&!t.escapeWithReference&&(d=Math.min(p[h],c[f]-(f==="right"?p.width:p.height))),$e({},h,d)}};return l.forEach(function(f){var h=["left","top"].indexOf(f)!==-1?"primary":"secondary";p=le({},p,u[h](f))}),e.offsets.popper=p,e}function Hi(e){var t=e.placement,n=t.split("-")[0],r=t.split("-")[1];if(r){var o=e.offsets,i=o.reference,a=o.popper,s=["bottom","top"].indexOf(n)!==-1,c=s?"left":"top",l=s?"width":"height",p={start:$e({},c,i[c]),end:$e({},c,i[c]+i[l]-a[l])};e.offsets.popper=le({},a,p[r])}return e}function Ui(e){if(!Kr(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=ft(e.instance.modifiers,function(r){return r.name==="preventOverflow"}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(e.hide===!0)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(e.hide===!1)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}function zi(e){var t=e.placement,n=t.split("-")[0],r=e.offsets,o=r.popper,i=r.reference,a=["left","right"].indexOf(n)!==-1,s=["top","left"].indexOf(n)===-1;return o[a?"left":"top"]=i[n]-(s?o[a?"width":"height"]:0),e.placement=At(t),e.offsets.popper=ke(o),e}function jt(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(Ki(t))return t}function he(e){return function(t){return jt(t)===e}}function Ki(e){return $i.includes(e)}function Xe(e){return function(t){return typeof t===e}}function Ji(e){return Vi.includes(e)}function C(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(C.array(e))return"Array";if(C.plainFunction(e))return"Function";var t=jt(e);return t||"Object"}function Xr(e){return function(t){return typeof t===e}}function ea(e,t){var n=e.length;if(n!==t.length)return!1;for(var r=n;r--!==0;)if(!ie(e[r],t[r]))return!1;return!0}function ta(e,t){if(e.byteLength!==t.byteLength)return!1;for(var n=new DataView(e.buffer),r=new DataView(t.buffer),o=e.byteLength;o--;)if(n.getUint8(o)!==r.getUint8(o))return!1;return!0}function na(e,t){var n,r,o,i;if(e.size!==t.size)return!1;try{for(var a=tn(e.entries()),s=a.next();!s.done;s=a.next()){var c=s.value;if(!t.has(c[0]))return!1}}catch(u){n={error:u}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}try{for(var l=tn(e.entries()),p=l.next();!p.done;p=l.next()){var c=p.value;if(!ie(c[1],t.get(c[0])))return!1}}catch(u){o={error:u}}finally{try{p&&!p.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}return!0}function ra(e,t){var n,r;if(e.size!==t.size)return!1;try{for(var o=tn(e.entries()),i=o.next();!i.done;i=o.next()){var a=i.value;if(!t.has(a[0]))return!1}}catch(s){n={error:s}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return!0}function ie(e,t){if(e===t)return!0;if(e&&pr(e)&&t&&pr(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return ea(e,t);if(e instanceof Map&&t instanceof Map)return na(e,t);if(e instanceof Set&&t instanceof Set)return ra(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return ta(e,t);if(ur(e)&&ur(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=n.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,n[o]))return!1;for(var o=n.length;o--!==0;){var i=n[o];if(!(i==="_owner"&&e.$$typeof)&&!ie(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function Mt(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(sa(t))return t}function me(e){return function(t){return Mt(t)===e}}function sa(e){return ia.includes(e)}function Ze(e){return function(t){return typeof t===e}}function la(e){return aa.includes(e)}function P(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(P.array(e))return"Array";if(P.plainFunction(e))return"Function";var t=Mt(e);return t||"Object"}function ca(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(function(n){return k.string(n)||k.array(n)||k.plainObject(n)})}function ua(e,t,n){return Zr(e,t)?[e,t].every(k.array)?!e.some(yr(n))&&t.some(yr(n)):[e,t].every(k.plainObject)?!Object.entries(e).some(mr(n))&&Object.entries(t).some(mr(n)):t===n:!1}function fr(e,t,n){var r=n.actual,o=n.key,i=n.previous,a=n.type,s=Te(e,o),c=Te(t,o),l=[s,c].every(k.number)&&(a==="increased"?s<c:s>c);return k.undefined(r)||(l=l&&c===r),k.undefined(i)||(l=l&&s===i),l}function dr(e,t,n){var r=n.key,o=n.type,i=n.value,a=Te(e,r),s=Te(t,r),c=o==="added"?a:s,l=o==="added"?s:a;if(!k.nullOrUndefined(i)){if(k.defined(c)){if(k.array(c)||k.plainObject(c))return ua(c,l,i)}else return ie(l,i);return!1}return[a,s].every(k.array)?!l.every(fn(c)):[a,s].every(k.plainObject)?pa(Object.keys(c),Object.keys(l)):![a,s].every(function(p){return k.primitive(p)&&k.defined(p)})&&(o==="added"?!k.defined(a)&&k.defined(s):k.defined(a)&&!k.defined(s))}function hr(e,t,n){var r=n===void 0?{}:n,o=r.key,i=Te(e,o),a=Te(t,o);if(!Zr(i,a))throw new TypeError("Inputs have different types");if(!ca(i,a))throw new TypeError("Inputs don't have length");return[i,a].every(k.plainObject)&&(i=Object.keys(i),a=Object.keys(a)),[i,a]}function mr(e){return function(t){var n=t[0],r=t[1];return k.array(e)?ie(e,r)||e.some(function(o){return ie(o,r)||k.array(r)&&fn(r)(o)}):k.plainObject(e)&&e[n]?!!e[n]&&ie(e[n],r):ie(e,r)}}function pa(e,t){return t.some(function(n){return!e.includes(n)})}function yr(e){return function(t){return k.array(e)?e.some(function(n){return ie(n,t)||k.array(t)&&fn(t)(n)}):ie(e,t)}}function ot(e,t){return k.array(e)?e.some(function(n){return ie(n,t)}):ie(e,t)}function fn(e){return function(t){return e.some(function(n){return ie(n,t)})}}function Zr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(k.array)||e.every(k.number)||e.every(k.plainObject)||e.every(k.string)}function Te(e,t){if(k.plainObject(e)||k.array(e)){if(k.string(t)){var n=t.split(".");return n.reduce(function(r,o){return r&&r[o]},e)}return k.number(t)?e[t]:e}return e}function fa(e,t){if([e,t].some(k.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(function(u){return k.plainObject(u)||k.array(u)}))throw new Error("Expected plain objects or array");var n=function(u,f){try{return dr(e,t,{key:u,type:"added",value:f})}catch{return!1}},r=function(u,f,h){try{var d=Te(e,u),g=Te(t,u),S=k.defined(f),b=k.defined(h);if(S||b){var R=b?ot(h,d):!ot(f,d),m=ot(f,g);return R&&m}return[d,g].every(k.array)||[d,g].every(k.plainObject)?!ie(d,g):d!==g}catch{return!1}},o=function(u,f,h){if(!k.defined(u))return!1;try{var d=Te(e,u),g=Te(t,u),S=k.defined(h);return ot(f,d)&&(S?ot(h,g):!S)}catch{return!1}},i=function(u,f){return k.defined(u)?(console.warn("`changedTo` is deprecated! Replace it with `change`"),r(u,f)):!1},a=function(u,f,h){if(!k.defined(u))return!1;try{return fr(e,t,{key:u,actual:f,previous:h,type:"decreased"})}catch{return!1}},s=function(u){try{var f=hr(e,t,{key:u}),h=f[0],d=f[1];return!!h.length&&!d.length}catch{return!1}},c=function(u){try{var f=hr(e,t,{key:u}),h=f[0],d=f[1];return!h.length&&!!d.length}catch{return!1}},l=function(u,f,h){if(!k.defined(u))return!1;try{return fr(e,t,{key:u,actual:f,previous:h,type:"increased"})}catch{return!1}},p=function(u,f){try{return dr(e,t,{key:u,type:"removed",value:f})}catch{return!1}};return{added:n,changed:r,changedFrom:o,changedTo:i,decreased:a,emptied:s,filled:c,increased:l,removed:p}}function gr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,r)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?gr(Object(n),!0).forEach(function(r){re(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gr(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function dt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function br(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,eo(r.key),r)}}function ht(e,t,n){return t&&br(e.prototype,t),n&&br(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function re(e,t,n){return t=eo(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mt(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nn(e,t)}function Nt(e){return Nt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Nt(e)}function nn(e,t){return nn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},nn(e,t)}function da(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ha(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Qr(e,t){if(e==null)return{};var n=ha(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Pe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ma(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Pe(e)}function yt(e){var t=da();return function(){var n=Nt(e),r;if(t){var o=Nt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return ma(this,r)}}function ya(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eo(e){var t=ya(e,"string");return typeof t=="symbol"?t:String(t)}function Ea(e,t,n,r){return typeof e=="boolean"?e:typeof e=="function"?e(t,n,r):e?!!e:!1}function Oa(e,t){return Object.hasOwnProperty.call(e,t)}function Sa(e,t,n,r){return r?new Error(r):new Error("Required ".concat(e[t]," `").concat(t,"` was not specified in `").concat(n,"`."))}function wa(e,t){if(typeof e!="function")throw new TypeError(ba);if(t&&typeof t!="string")throw new TypeError(va)}function vr(e,t,n){return wa(e,n),function(r,o,i){for(var a=arguments.length,s=new Array(a>3?a-3:0),c=3;c<a;c++)s[c-3]=arguments[c];return Ea(t,r,o,i)?Oa(r,o)?e.apply(void 0,[r,o,i].concat(s)):Sa(r,o,i,n):e.apply(void 0,[r,o,i].concat(s))}}function Oe(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Jt(){return"ontouchstart"in window&&/Mobi/.test(navigator.userAgent)}function Pt(e){var t=e.title,n=e.data,r=e.warn,o=r===void 0?!1:r,i=e.debug,a=i===void 0?!1:i,s=o?console.warn||console.error:console.log;a&&t&&n&&(console.groupCollapsed("%creact-floater: ".concat(t),"color: #9b00ff; font-weight: bold; font-size: 12px;"),Array.isArray(n)?n.forEach(function(c){se.plainObject(c)&&c.key?s.apply(console,[c.key,c.value]):s.apply(console,[c])}):s.apply(console,[n]),console.groupEnd())}function Ta(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.addEventListener(t,n,r)}function Ia(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.removeEventListener(t,n,r)}function Ca(e,t,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o;o=function(i){n(i),Ia(e,t,o)},Ta(e,t,o,r)}function Er(){}function ro(e){var t=e.handleClick,n=e.styles,r=n.color,o=n.height,i=n.width,a=Qr(n,Pa);return y.createElement("button",{"aria-label":"close",onClick:t,style:a,type:"button"},y.createElement("svg",{width:"".concat(i,"px"),height:"".concat(o,"px"),viewBox:"0 0 18 18",version:"1.1",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},y.createElement("g",null,y.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:r}))))}function oo(e){var t=e.content,n=e.footer,r=e.handleClick,o=e.open,i=e.positionWrapper,a=e.showCloseButton,s=e.title,c=e.styles,l={content:y.isValidElement(t)?t:y.createElement("div",{className:"__floater__content",style:c.content},t)};return s&&(l.title=y.isValidElement(s)?s:y.createElement("div",{className:"__floater__title",style:c.title},s)),n&&(l.footer=y.isValidElement(n)?n:y.createElement("div",{className:"__floater__footer",style:c.footer},n)),(a||i)&&!se.boolean(o)&&(l.close=y.createElement(ro,{styles:c.close,handleClick:r})),y.createElement("div",{className:"__floater__container",style:c.container},l.close,l.title,l.content,l.footer)}function xa(e){var t=(0,en.default)(Ra,e.options||{});return{wrapper:{cursor:"help",display:"inline-flex",flexDirection:"column",zIndex:t.zIndex},wrapperPosition:{left:-1e3,position:"absolute",top:-1e3,visibility:"hidden"},floater:{display:"inline-block",filter:"drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))",maxWidth:300,opacity:0,position:"relative",transition:"opacity 0.3s",visibility:"hidden",zIndex:t.zIndex},floaterOpening:{opacity:1,visibility:"visible"},floaterWithAnimation:{opacity:1,transition:"opacity 0.3s, transform 0.2s",visibility:"visible"},floaterWithComponent:{maxWidth:"100%"},floaterClosing:{opacity:0,visibility:"visible"},floaterCentered:{left:"50%",position:"fixed",top:"50%",transform:"translate(-50%, -50%)"},container:{backgroundColor:"#fff",color:"#666",minHeight:60,minWidth:200,padding:20,position:"relative",zIndex:10},title:{borderBottom:"1px solid #555",color:"#555",fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",fontSize:0,height:15,outline:"none",padding:10,position:"absolute",right:0,top:0,width:15,WebkitAppearance:"none"},footer:{borderTop:"1px solid #ccc",fontSize:13,marginTop:10,paddingTop:5},arrow:{color:"#fff",display:"inline-flex",length:16,margin:8,position:"absolute",spread:32},options:t}}function Ne(){var e;return!!(typeof window<"u"&&(e=window.document)!=null&&e.createElement)}function so(e){return e?e.getBoundingClientRect():null}function La(e=!1){let{body:t,documentElement:n}=document;if(!t||!n)return 0;if(e){let r=[t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight].sort((i,a)=>i-a),o=Math.floor(r.length/2);return r.length%2===0?(r[o-1]+r[o])/2:r[o]}return Math.max(t.scrollHeight,t.offsetHeight,n.clientHeight,n.scrollHeight,n.offsetHeight)}function Re(e){if(typeof e=="string")try{return document.querySelector(e)}catch(t){return console.error(t),null}return e}function ja(e){return!e||e.nodeType!==1?null:getComputedStyle(e)}function ct(e,t,n){if(!e)return Be();let r=(0,jr.default)(e);if(r){if(r.isSameNode(Be()))return n?document:Be();if(!(r.scrollHeight>r.offsetHeight)&&!t)return r.style.overflow="initial",Be()}return r}function gt(e,t){if(!e)return!1;let n=ct(e,t);return n?!n.isSameNode(Be()):!1}function Ma(e){return e.offsetParent!==document.body}function Ve(e,t="fixed"){if(!e||!(e instanceof HTMLElement))return!1;let{nodeName:n}=e,r=ja(e);return n==="BODY"||n==="HTML"?!1:r&&r.position===t?!0:e.parentNode?Ve(e.parentNode,t):!1}function Da(e){var t;if(!e)return!1;let n=e;for(;n&&n!==document.body;){if(n instanceof HTMLElement){let{display:r,visibility:o}=getComputedStyle(n);if(r==="none"||o==="hidden")return!1}n=(t=n.parentElement)!=null?t:null}return!0}function Fa(e,t,n){var r,o,i;let a=so(e),s=ct(e,n),c=gt(e,n),l=Ve(e),p=0,u=(r=a?.top)!=null?r:0;if(c&&l){let f=(o=e?.offsetTop)!=null?o:0,h=(i=s?.scrollTop)!=null?i:0;u=f-h}else s instanceof HTMLElement&&(p=s.scrollTop,!c&&!Ve(e)&&(u+=p),s.isSameNode(Be())||(u+=Be().scrollTop));return Math.floor(u-t)}function Ba(e,t,n){var r;if(!e)return 0;let{offsetTop:o=0,scrollTop:i=0}=(r=(0,jr.default)(e))!=null?r:{},a=e.getBoundingClientRect().top+i;o&&(gt(e,n)||Ma(e))&&(a-=o);let s=Math.floor(a-t);return s<0?0:s}function Be(){var e;return(e=document.scrollingElement)!=null?e:document.documentElement}function Wa(e,t){let{duration:n,element:r}=t;return new Promise((o,i)=>{let{scrollTop:a}=r,s=e>a?e-a:a-e;li.default.top(r,e,{duration:s<100?50:n},c=>c&&c.message!=="Element already at target scroll position"?i(c):o())})}function lo(e=navigator.userAgent){let t=e;return typeof window>"u"?t="node":document.documentMode?t="ie":/Edge/.test(e)?t="edge":window.opera||e.includes(" OPR/")?t="opera":typeof window.InstallTrigger<"u"?t="firefox":window.chrome?t="chrome":/(Version\/([\d._]+).*Safari|CriOS|FxiOS| Mobile\/)/.test(e)&&(t="safari"),t}function Rt(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Se(e,t={}){let{defaultValue:n,step:r,steps:o}=t,i=(0,rr.default)(e);if(i)(i.includes("{step}")||i.includes("{steps}"))&&r&&o&&(i=i.replace("{step}",r.toString()).replace("{steps}",o.toString()));else if(Bt(e)&&!Object.values(e.props).length&&Rt(e.type)==="function"){let a=e.type({});i=Se(a,t)}else i=(0,rr.default)(n);return i}function Ha(e,t){return!_.plainObject(e)||!_.array(t)?!1:Object.keys(e).every(n=>t.includes(n))}function Ua(e){let t=/^#?([\da-f])([\da-f])([\da-f])$/i,n=e.replace(t,(o,i,a,s)=>i+i+a+a+s+s),r=/^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(n);return r?[parseInt(r[1],16),parseInt(r[2],16),parseInt(r[3],16)]:[]}function Or(e){return e.disableBeacon||e.placement==="center"}function Sr(){return!["chrome","safari","firefox","opera"].includes(lo())}function Le({data:e,debug:t=!1,title:n,warn:r=!1}){let o=r?console.warn||console.error:console.log;t&&(n&&e?(console.groupCollapsed(`%creact-joyride: ${n}`,"color: #ff0044; font-weight: bold; font-size: 12px;"),Array.isArray(e)?e.forEach(i=>{_.plainObject(i)&&i.key?o.apply(console,[i.key,i.value]):o.apply(console,[i])}):o.apply(console,[e]),console.groupEnd()):console.error("Missing title or data props"))}function za(e){return Object.keys(e)}function co(e,...t){if(!_.plainObject(e))throw new TypeError("Expected an object");let n={};for(let r in e)({}).hasOwnProperty.call(e,r)&&(t.includes(r)||(n[r]=e[r]));return n}function Ga(e,...t){if(!_.plainObject(e))throw new TypeError("Expected an object");if(!t.length)return e;let n={};for(let r in e)({}).hasOwnProperty.call(e,r)&&t.includes(r)&&(n[r]=e[r]);return n}function rn(e,t,n){let r=i=>i.replace("{step}",String(t)).replace("{steps}",String(n));if(Rt(e)==="string")return r(e);if(!Bt(e))return e;let{children:o}=e.props;if(Rt(o)==="string"&&o.includes("{step}"))return Ft(e,{children:r(o)});if(Array.isArray(o))return Ft(e,{children:o.map(i=>typeof i=="string"?r(i):rn(i,t,n))});if(Rt(e.type)==="function"&&!Object.values(e.props).length){let i=e.type({});return rn(i,t,n)}return e}function Ya(e){let{isFirstStep:t,lifecycle:n,previousLifecycle:r,scrollToFirstStep:o,step:i,target:a}=e;return!i.disableScrolling&&(!t||o||n===L.TOOLTIP)&&i.placement!=="center"&&(!i.isFixed||!Ve(a))&&r!==n&&[L.BEACON,L.TOOLTIP].includes(n)}function Ja(e,t){var n,r,o,i,a;let{floaterProps:s,styles:c}=e,l=(0,Ct.default)((n=t.floaterProps)!=null?n:{},s??{}),p=(0,Ct.default)(c??{},(r=t.styles)!=null?r:{}),u=(0,Ct.default)(Ka,p.options||{}),f=t.placement==="center"||t.disableBeacon,{width:h}=u;window.innerWidth>480&&(h=380),"width"in u&&(h=typeof u.width=="number"&&window.innerWidth<u.width?window.innerWidth-30:u.width);let d={bottom:0,left:0,overflow:"hidden",position:"absolute",right:0,top:0,zIndex:u.zIndex},g={beacon:{...st,display:f?"none":"inline-block",height:u.beaconSize,position:"relative",width:u.beaconSize,zIndex:u.zIndex},beaconInner:{animation:"joyride-beacon-inner 1.2s infinite ease-in-out",backgroundColor:u.primaryColor,borderRadius:"50%",display:"block",height:"50%",left:"50%",opacity:.7,position:"absolute",top:"50%",transform:"translate(-50%, -50%)",width:"50%"},beaconOuter:{animation:"joyride-beacon-outer 1.2s infinite ease-in-out",backgroundColor:`rgba(${Ua(u.primaryColor).join(",")}, 0.2)`,border:`2px solid ${u.primaryColor}`,borderRadius:"50%",boxSizing:"border-box",display:"block",height:"100%",left:0,opacity:.9,position:"absolute",top:0,transformOrigin:"center",width:"100%"},tooltip:{backgroundColor:u.backgroundColor,borderRadius:5,boxSizing:"border-box",color:u.textColor,fontSize:16,maxWidth:"100%",padding:15,position:"relative",width:h},tooltipContainer:{lineHeight:1.4,textAlign:"center"},tooltipTitle:{fontSize:18,margin:0},tooltipContent:{padding:"20px 10px"},tooltipFooter:{alignItems:"center",display:"flex",justifyContent:"flex-end",marginTop:15},tooltipFooterSpacer:{flex:1},buttonNext:{...st,backgroundColor:u.primaryColor,borderRadius:4,color:"#fff"},buttonBack:{...st,color:u.primaryColor,marginLeft:"auto",marginRight:5},buttonClose:{...st,color:u.textColor,height:14,padding:15,position:"absolute",right:0,top:0,width:14},buttonSkip:{...st,color:u.textColor,fontSize:14},overlay:{...d,backgroundColor:u.overlayColor,mixBlendMode:"hard-light"},overlayLegacy:{...d},overlayLegacyCenter:{...d,backgroundColor:u.overlayColor},spotlight:{...wr,backgroundColor:"gray"},spotlightLegacy:{...wr,boxShadow:`0 0 0 9999px ${u.overlayColor}, ${u.spotlightShadow}`},floaterStyles:{arrow:{color:(a=(i=(o=l?.styles)==null?void 0:o.arrow)==null?void 0:i.color)!=null?a:u.arrowColor},options:{zIndex:u.zIndex+100}},options:u};return(0,Ct.default)(g,p)}function Xa(e){return Ga(e,"beaconComponent","disableCloseOnEsc","disableOverlay","disableOverlayClose","disableScrolling","disableScrollParentFix","floaterProps","hideBackButton","hideCloseButton","locale","showProgress","showSkipButton","spotlightClicks","spotlightPadding","styles","tooltipComponent")}function Ge(e,t){var n,r,o,i,a,s;let c=t??{},l=It.default.all([$a,Xa(e),c],{isMergeableObject:_.plainObject}),p=Ja(e,l),u=gt(Re(l.target),l.disableScrollParentFix),f=It.default.all([qa,(n=e.floaterProps)!=null?n:{},(r=l.floaterProps)!=null?r:{}]);return f.offset=l.offset,f.styles=(0,It.default)((o=f.styles)!=null?o:{},p.floaterStyles),f.offset+=(a=(i=e.spotlightPadding)!=null?i:l.spotlightPadding)!=null?a:0,l.placementBeacon&&f.wrapperOptions&&(f.wrapperOptions.placement=l.placementBeacon),u&&f.options.preventOverflow&&(f.options.preventOverflow.boundariesElement="window"),{...l,locale:It.default.all([uo,(s=e.locale)!=null?s:{},l.locale||{}]),floaterProps:f,styles:co(p,"floaterStyles")}}function po(e,t=!1){return _.plainObject(e)?e.target?!0:(Le({title:"validateStep",data:"target is missing from the step",warn:!0,debug:t}),!1):(Le({title:"validateStep",data:"step must be an object",warn:!0,debug:t}),!1)}function Tr(e,t=!1){return _.array(e)?e.every(n=>po(n,t)):(Le({title:"validateSteps",data:"steps must be an array",warn:!0,debug:t}),!1)}function Qa(e){return new Za(e)}function es({styles:e}){return V("div",{key:"JoyrideSpotlight",className:"react-joyride__spotlight","data-test-id":"spotlight",style:e})}function as({styles:e,...t}){let{color:n,height:r,width:o,...i}=e;return y.createElement("button",{style:i,type:"button",...t},y.createElement("svg",{height:typeof r=="number"?`${r}px`:r,preserveAspectRatio:"xMidYMid",version:"1.1",viewBox:"0 0 18 18",width:typeof o=="number"?`${o}px`:o,xmlns:"http://www.w3.org/2000/svg"},y.createElement("g",null,y.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:n}))))}function ls(e){let{backProps:t,closeProps:n,index:r,isLastStep:o,primaryProps:i,skipProps:a,step:s,tooltipProps:c}=e,{content:l,hideBackButton:p,hideCloseButton:u,hideFooter:f,showSkipButton:h,styles:d,title:g}=s,S={};return S.primary=V("button",{"data-test-id":"button-primary",style:d.buttonNext,type:"button",...i}),h&&!o&&(S.skip=V("button",{"aria-live":"off","data-test-id":"button-skip",style:d.buttonSkip,type:"button",...a})),!p&&r>0&&(S.back=V("button",{"data-test-id":"button-back",style:d.buttonBack,type:"button",...t})),S.close=!u&&V(ss,{"data-test-id":"button-close",styles:d.buttonClose,...n}),V("div",{key:"JoyrideTooltip","aria-label":Se(g??l),className:"react-joyride__tooltip",style:d.tooltip,...c},V("div",{style:d.tooltipContainer},g&&V("h1",{"aria-label":Se(g),style:d.tooltipTitle},g),V("div",{style:d.tooltipContent},l)),!f&&V("div",{style:d.tooltipFooter},V("div",{style:d.tooltipFooterSpacer},S.skip),S.back,S.primary),S.close)}function ws({step:e,steps:t,onClose:n,onComplete:r}){let[o,i]=pe(null),a=Pn();return ae(()=>{let s;return i(c=>{let l=t.findIndex(({key:p})=>p===e);return l===-1?null:l===c?c:(s=setTimeout(i,500,l),null)}),()=>clearTimeout(s)},[e,t]),o===null?null:y.createElement(fs,{continuous:!0,steps:t,stepIndex:o,spotlightPadding:0,disableCloseOnEsc:!0,disableOverlayClose:!0,disableScrolling:!0,callback:s=>{s.action===$.CLOSE&&n(),s.action===$.NEXT&&s.index===s.size-1&&r()},floaterProps:{disableAnimation:!0,styles:{arrow:{length:20,spread:2},floater:{filter:a.base==="light"?"drop-shadow(0px 5px 5px rgba(0,0,0,0.05)) drop-shadow(0 1px 3px rgba(0,0,0,0.1))":"drop-shadow(#fff5 0px 0px 0.5px) drop-shadow(#fff5 0px 0px 0.5px)"}}},tooltipComponent:Ss,styles:{overlay:{mixBlendMode:"unset",backgroundColor:t[o]?.target==="body"?"rgba(27, 28, 29, 0.2)":"none"},spotlight:{backgroundColor:"none",border:`solid 2px ${a.color.secondary}`,boxShadow:"0px 0px 0px 9999px rgba(27, 28, 29, 0.2)"},tooltip:{width:280,color:a.color.lightest,background:a.color.secondary},options:{zIndex:9998,primaryColor:a.color.secondary,arrowColor:a.color.secondary}}})}function Ds({api:e}){let[t,n]=pe(!0),[r,o]=pe(!1),[i,a]=pe("1:Intro"),[s,c]=pe(),[l,p]=pe(),[u,f]=pe(),[h,d]=pe(),g=tt(x=>{try{let{id:z,refId:ee}=e.getCurrentStoryData()||{};(z!==x||ee!==void 0)&&e.selectStory(x)}catch{}},[e]),S=tt(()=>{let x=new URL(window.location.href),z=decodeURIComponent(x.searchParams.get("path"));x.search=`?path=${z}&onboarding=false`,history.replaceState({},"",x.href),e.setQueryParams({onboarding:"false"}),n(!1)},[e,n]),b=tt(()=>{e.emit(Kn,{step:"6:FinishedOnboarding",type:"telemetry"}),g("configure-your-project--docs"),S()},[e,g,S]);if(ae(()=>{e.setQueryParams({onboarding:"true"}),g("example-button--primary"),e.togglePanel(!0),e.togglePanelPosition("bottom"),e.setSelectedPanel(St)},[e,g]),ae(()=>{let x=new MutationObserver(()=>{c(document.getElementById("control-primary")),p(document.getElementById("save-from-controls")),f(document.getElementById("create-new-story-form"))});return x.observe(document.body,{childList:!0,subtree:!0}),()=>x.disconnect()},[]),ae(()=>{a(x=>["1:Intro","5:StoryCreated","6:FinishedOnboarding"].includes(x)?x:u?"4:CreateStory":l?"3:SaveFromControls":s?"2:Controls":"1:Intro")},[u,s,l]),ae(()=>e.on(En,({payload:x,success:z})=>{!z||!x?.newStoryName||(d(x),o(!0),a("5:StoryCreated"),setTimeout(()=>e.clearNotification("save-story-success")))}),[e]),ae(()=>e.emit(Kn,{step:i,type:"telemetry"}),[e,i]),!t)return null;let R=h?.sourceFileContent,m=R?.lastIndexOf(`export const ${h?.newStoryExportName}`),v=R?.slice(m).trim(),w=R?.slice(0,m).split(`
`).length,U=[{key:"2:Controls",target:"#control-primary",title:"Interactive story playground",content:y.createElement(y.Fragment,null,"See how a story renders with different data and state without touching code. Try it out by toggling this button.",y.createElement(Vn,{targetSelector:"#control-primary",pulsating:!0})),offset:20,placement:"right",disableBeacon:!0,disableOverlay:!0,spotlightClicks:!0,onNextButtonClick:()=>{document.querySelector("#control-primary").click()}},{key:"3:SaveFromControls",target:'button[aria-label="Create new story with these settings"]',title:"Save your changes as a new story",content:y.createElement(y.Fragment,null,"Great! Storybook stories represent the key states of each of your components. After modifying a story, you can save your changes from here or reset it.",y.createElement(Vn,{targetSelector:"button[aria-label='Create new story with these settings']"})),offset:6,placement:"top",disableBeacon:!0,disableOverlay:!0,spotlightClicks:!0,onNextButtonClick:()=>{document.querySelector('button[aria-label="Create new story with these settings"]').click()},styles:{tooltip:{width:400}}},{key:"5:StoryCreated",target:'#storybook-explorer-tree [data-selected="true"]',title:"You just added your first story!",content:y.createElement(y.Fragment,null,"Well done! You just created your first story from the Storybook manager. This automatically added a few lines of code in"," ",y.createElement(Ls,null,h?.sourceFileName),".",v&&y.createElement(zt,{theme:Gt(Cn.dark)},y.createElement(js,null,y.createElement(Tn,{language:"jsx",showLineNumbers:!0,startingLineNumber:w},v)))),offset:12,placement:"right",disableBeacon:!0,disableOverlay:!0,styles:{tooltip:{width:400}}}];return y.createElement(zt,{theme:Ms},r&&y.createElement(qo,null),i==="1:Intro"?y.createElement(ks,{onDismiss:()=>a("2:Controls")}):y.createElement(ws,{step:i,steps:U,onClose:S,onComplete:b}))}var So,Pr,wo,Rr,To,Io,ce,Co,We,Po,Ro,xo,on,_o,xr,Ao,_r,Ar,No,ko,Lo,jo,Nn,Mo,kn,Ln,jn,Mn,Dn,Fn,Bn,Wn,Hn,Un,zn,Fo,Bo,Wo,Gn,Tt,Ee,lt,Ho,Yt,qt,Yn,ze,$t,qn,Nr,$n,Uo,zo,Yo,qo,Kn,$o,Vo,Jn,Xn,Ko,ei,ti,oi,_,li,jr,rr,It,Ct,O,ut,ci,fi,di,or,ir,yi,gi,$e,le,Ai,Jr,Vt,Kt,Gi,Yi,Lt,cr,en,qi,$i,Vi,se,Xi,Zi,ur,pr,Qi,tn,oa,ia,aa,k,ga,ba,va,H,it,to,no,Pa,io,ao,Ra,_a,Aa,dn,Na,ka,A,$,fe,L,B,at,qa,uo,$a,Va,Ka,st,wr,fo,Ir,Za,ts,ns,rs,os,is,ss,cs,us,ps,ho,fs,ds,hs,ms,ys,gs,bs,vs,Es,Os,Ss,Ts,mo,Is,Cs,Ps,Rs,xs,_s,As,Ns,Cr,ks,Ls,js,Ms,go=ue(()=>{X();Z();Q();vt();vt();In();Ht();Ut();Rn();Ot();Ot();An();So=Object.create,Pr=Object.defineProperty,wo=Object.getOwnPropertyDescriptor,Rr=Object.getOwnPropertyNames,To=Object.getPrototypeOf,Io=Object.prototype.hasOwnProperty,ce=(e,t)=>function(){return t||(0,e[Rr(e)[0]])((t={exports:{}}).exports,t),t.exports},Co=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Rr(t))!Io.call(e,o)&&o!==n&&Pr(e,o,{get:()=>t[o],enumerable:!(r=wo(t,o))||r.enumerable});return e},We=(e,t,n)=>(n=e!=null?So(To(e)):{},Co(t||!e||!e.__esModule?Pr(n,"default",{value:e,enumerable:!0}):n,e)),Po=ce({"../../node_modules/scroll/index.js"(e,t){var n=new Error("Element already at target scroll position"),r=new Error("Scroll cancelled"),o=Math.min,i=Date.now;t.exports={left:a("scrollLeft"),top:a("scrollTop")};function a(l){return function(p,u,f,h){f=f||{},typeof f=="function"&&(h=f,f={}),typeof h!="function"&&(h=c);var d=i(),g=p[l],S=f.ease||s,b=isNaN(f.duration)?350:+f.duration,R=!1;return g===u?h(n,p[l]):requestAnimationFrame(v),m;function m(){R=!0}function v(w){if(R)return h(r,p[l]);var U=i(),x=o(1,(U-d)/b),z=S(x);p[l]=z*(u-g)+g,x<1?requestAnimationFrame(v):requestAnimationFrame(function(){h(null,p[l])})}}}function s(l){return .5*(1-Math.cos(Math.PI*l))}function c(){}}}),Ro=ce({"../../node_modules/scrollparent/scrollparent.js"(e,t){(function(n,r){typeof define=="function"&&define.amd?define([],r):typeof t=="object"&&t.exports?t.exports=r():n.Scrollparent=r()})(e,function(){function n(o){var i=getComputedStyle(o,null).getPropertyValue("overflow");return i.indexOf("scroll")>-1||i.indexOf("auto")>-1}function r(o){if(o instanceof HTMLElement||o instanceof SVGElement){for(var i=o.parentNode;i.parentNode;){if(n(i))return i;i=i.parentNode}return document.scrollingElement||document.documentElement}}return r})}}),xo=ce({"../../node_modules/react-innertext/index.js"(e,t){var n=function(i){return Object.prototype.hasOwnProperty.call(i,"props")},r=function(i,a){return i+o(a)},o=function(i){return i===null||typeof i=="boolean"||typeof i>"u"?"":typeof i=="number"?i.toString():typeof i=="string"?i:Array.isArray(i)?i.reduce(r,""):n(i)&&Object.prototype.hasOwnProperty.call(i.props,"children")?o(i.props.children):""};o.default=o,t.exports=o}}),on=ce({"../../node_modules/deepmerge/dist/cjs.js"(e,t){var n=function(m){return r(m)&&!o(m)};function r(m){return!!m&&typeof m=="object"}function o(m){var v=Object.prototype.toString.call(m);return v==="[object RegExp]"||v==="[object Date]"||s(m)}var i=typeof Symbol=="function"&&Symbol.for,a=i?Symbol.for("react.element"):60103;function s(m){return m.$$typeof===a}function c(m){return Array.isArray(m)?[]:{}}function l(m,v){return v.clone!==!1&&v.isMergeableObject(m)?b(c(m),m,v):m}function p(m,v,w){return m.concat(v).map(function(U){return l(U,w)})}function u(m,v){if(!v.customMerge)return b;var w=v.customMerge(m);return typeof w=="function"?w:b}function f(m){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(m).filter(function(v){return Object.propertyIsEnumerable.call(m,v)}):[]}function h(m){return Object.keys(m).concat(f(m))}function d(m,v){try{return v in m}catch{return!1}}function g(m,v){return d(m,v)&&!(Object.hasOwnProperty.call(m,v)&&Object.propertyIsEnumerable.call(m,v))}function S(m,v,w){var U={};return w.isMergeableObject(m)&&h(m).forEach(function(x){U[x]=l(m[x],w)}),h(v).forEach(function(x){g(m,x)||(d(m,x)&&w.isMergeableObject(v[x])?U[x]=u(x,w)(m[x],v[x],w):U[x]=l(v[x],w))}),U}function b(m,v,w){w=w||{},w.arrayMerge=w.arrayMerge||p,w.isMergeableObject=w.isMergeableObject||n,w.cloneUnlessOtherwiseSpecified=l;var U=Array.isArray(v),x=Array.isArray(m),z=U===x;return z?U?w.arrayMerge(m,v,w):S(m,v,w):l(v,w)}b.all=function(m,v){if(!Array.isArray(m))throw new Error("first argument should be an array");return m.reduce(function(w,U){return b(w,U,v)},{})};var R=b;t.exports=R}}),_o=ce({"../../node_modules/react-is/cjs/react-is.development.js"(e){(function(){var t=typeof Symbol=="function"&&Symbol.for,n=t?Symbol.for("react.element"):60103,r=t?Symbol.for("react.portal"):60106,o=t?Symbol.for("react.fragment"):60107,i=t?Symbol.for("react.strict_mode"):60108,a=t?Symbol.for("react.profiler"):60114,s=t?Symbol.for("react.provider"):60109,c=t?Symbol.for("react.context"):60110,l=t?Symbol.for("react.async_mode"):60111,p=t?Symbol.for("react.concurrent_mode"):60111,u=t?Symbol.for("react.forward_ref"):60112,f=t?Symbol.for("react.suspense"):60113,h=t?Symbol.for("react.suspense_list"):60120,d=t?Symbol.for("react.memo"):60115,g=t?Symbol.for("react.lazy"):60116,S=t?Symbol.for("react.block"):60121,b=t?Symbol.for("react.fundamental"):60117,R=t?Symbol.for("react.responder"):60118,m=t?Symbol.for("react.scope"):60119;function v(T){return typeof T=="string"||typeof T=="function"||T===o||T===p||T===a||T===i||T===f||T===h||typeof T=="object"&&T!==null&&(T.$$typeof===g||T.$$typeof===d||T.$$typeof===s||T.$$typeof===c||T.$$typeof===u||T.$$typeof===b||T.$$typeof===R||T.$$typeof===m||T.$$typeof===S)}function w(T){if(typeof T=="object"&&T!==null){var ne=T.$$typeof;switch(ne){case n:var Ce=T.type;switch(Ce){case l:case p:case o:case a:case i:case f:return Ce;default:var hn=Ce&&Ce.$$typeof;switch(hn){case c:case u:case g:case d:case s:return hn;default:return ne}}case r:return ne}}}var U=l,x=p,z=c,ee=s,Ie=n,je=u,Qe=o,Me=g,Ue=d,et=r,xe=a,_e=i,ye=f,Ae=!1;function Dt(T){return Ae||(Ae=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),bt(T)||w(T)===l}function bt(T){return w(T)===p}function E(T){return w(T)===c}function N(T){return w(T)===s}function W(T){return typeof T=="object"&&T!==null&&T.$$typeof===n}function D(T){return w(T)===u}function j(T){return w(T)===o}function G(T){return w(T)===g}function M(T){return w(T)===d}function F(T){return w(T)===r}function Y(T){return w(T)===a}function K(T){return w(T)===i}function q(T){return w(T)===f}e.AsyncMode=U,e.ConcurrentMode=x,e.ContextConsumer=z,e.ContextProvider=ee,e.Element=Ie,e.ForwardRef=je,e.Fragment=Qe,e.Lazy=Me,e.Memo=Ue,e.Portal=et,e.Profiler=xe,e.StrictMode=_e,e.Suspense=ye,e.isAsyncMode=Dt,e.isConcurrentMode=bt,e.isContextConsumer=E,e.isContextProvider=N,e.isElement=W,e.isForwardRef=D,e.isFragment=j,e.isLazy=G,e.isMemo=M,e.isPortal=F,e.isProfiler=Y,e.isStrictMode=K,e.isSuspense=q,e.isValidElementType=v,e.typeOf=w})()}}),xr=ce({"../../node_modules/react-is/index.js"(e,t){t.exports=_o()}}),Ao=ce({"../../node_modules/object-assign/index.js"(e,t){var n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function i(s){if(s==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(s)}function a(){try{if(!Object.assign)return!1;var s=new String("abc");if(s[5]="de",Object.getOwnPropertyNames(s)[0]==="5")return!1;for(var c={},l=0;l<10;l++)c["_"+String.fromCharCode(l)]=l;var p=Object.getOwnPropertyNames(c).map(function(f){return c[f]});if(p.join("")!=="**********")return!1;var u={};return"abcdefghijklmnopqrst".split("").forEach(function(f){u[f]=f}),Object.keys(Object.assign({},u)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}t.exports=a()?Object.assign:function(s,c){for(var l,p=i(s),u,f=1;f<arguments.length;f++){l=Object(arguments[f]);for(var h in l)r.call(l,h)&&(p[h]=l[h]);if(n){u=n(l);for(var d=0;d<u.length;d++)o.call(l,u[d])&&(p[u[d]]=l[u[d]])}}return p}}}),_r=ce({"../../node_modules/prop-types/lib/ReactPropTypesSecret.js"(e,t){var n="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";t.exports=n}}),Ar=ce({"../../node_modules/prop-types/lib/has.js"(e,t){t.exports=Function.call.bind(Object.prototype.hasOwnProperty)}}),No=ce({"../../node_modules/prop-types/checkPropTypes.js"(e,t){var n=function(){};r=_r(),o={},i=Ar(),n=function(s){var c="Warning: "+s;typeof console<"u"&&console.error(c);try{throw new Error(c)}catch{}};var r,o,i;function a(s,c,l,p,u){for(var f in s)if(i(s,f)){var h;try{if(typeof s[f]!="function"){var d=Error((p||"React class")+": "+l+" type `"+f+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof s[f]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw d.name="Invariant Violation",d}h=s[f](c,f,p,l,null,r)}catch(S){h=S}if(h&&!(h instanceof Error)&&n((p||"React class")+": type specification of "+l+" `"+f+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof h+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),h instanceof Error&&!(h.message in o)){o[h.message]=!0;var g=u?u():"";n("Failed "+l+" type: "+h.message+(g??""))}}}a.resetWarningCache=function(){o={}},t.exports=a}}),ko=ce({"../../node_modules/prop-types/factoryWithTypeCheckers.js"(e,t){var n=xr(),r=Ao(),o=_r(),i=Ar(),a=No(),s=function(){};s=function(l){var p="Warning: "+l;typeof console<"u"&&console.error(p);try{throw new Error(p)}catch{}};function c(){return null}t.exports=function(l,p){var u=typeof Symbol=="function"&&Symbol.iterator,f="@@iterator";function h(E){var N=E&&(u&&E[u]||E[f]);if(typeof N=="function")return N}var d="<<anonymous>>",g={array:m("array"),bigint:m("bigint"),bool:m("boolean"),func:m("function"),number:m("number"),object:m("object"),string:m("string"),symbol:m("symbol"),any:v(),arrayOf:w,element:U(),elementType:x(),instanceOf:z,node:Qe(),objectOf:Ie,oneOf:ee,oneOfType:je,shape:Ue,exact:et};function S(E,N){return E===N?E!==0||1/E===1/N:E!==E&&N!==N}function b(E,N){this.message=E,this.data=N&&typeof N=="object"?N:{},this.stack=""}b.prototype=Error.prototype;function R(E){var N={},W=0;function D(G,M,F,Y,K,q,T){if(Y=Y||d,q=q||F,T!==o){if(p){var ne=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw ne.name="Invariant Violation",ne}else if(typeof console<"u"){var Ce=Y+":"+F;!N[Ce]&&W<3&&(s("You are manually calling a React.PropTypes validation function for the `"+q+"` prop on `"+Y+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),N[Ce]=!0,W++)}}return M[F]==null?G?M[F]===null?new b("The "+K+" `"+q+"` is marked as required "+("in `"+Y+"`, but its value is `null`.")):new b("The "+K+" `"+q+"` is marked as required in "+("`"+Y+"`, but its value is `undefined`.")):null:E(M,F,Y,K,q)}var j=D.bind(null,!1);return j.isRequired=D.bind(null,!0),j}function m(E){function N(W,D,j,G,M,F){var Y=W[D],K=ye(Y);if(K!==E){var q=Ae(Y);return new b("Invalid "+G+" `"+M+"` of type "+("`"+q+"` supplied to `"+j+"`, expected ")+("`"+E+"`."),{expectedType:E})}return null}return R(N)}function v(){return R(c)}function w(E){function N(W,D,j,G,M){if(typeof E!="function")return new b("Property `"+M+"` of component `"+j+"` has invalid PropType notation inside arrayOf.");var F=W[D];if(!Array.isArray(F)){var Y=ye(F);return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected an array."))}for(var K=0;K<F.length;K++){var q=E(F,K,j,G,M+"["+K+"]",o);if(q instanceof Error)return q}return null}return R(N)}function U(){function E(N,W,D,j,G){var M=N[W];if(!l(M)){var F=ye(M);return new b("Invalid "+j+" `"+G+"` of type "+("`"+F+"` supplied to `"+D+"`, expected a single ReactElement."))}return null}return R(E)}function x(){function E(N,W,D,j,G){var M=N[W];if(!n.isValidElementType(M)){var F=ye(M);return new b("Invalid "+j+" `"+G+"` of type "+("`"+F+"` supplied to `"+D+"`, expected a single ReactElement type."))}return null}return R(E)}function z(E){function N(W,D,j,G,M){if(!(W[D]instanceof E)){var F=E.name||d,Y=bt(W[D]);return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected ")+("instance of `"+F+"`."))}return null}return R(N)}function ee(E){if(!Array.isArray(E))return arguments.length>1?s("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):s("Invalid argument supplied to oneOf, expected an array."),c;function N(W,D,j,G,M){for(var F=W[D],Y=0;Y<E.length;Y++)if(S(F,E[Y]))return null;var K=JSON.stringify(E,function(q,T){var ne=Ae(T);return ne==="symbol"?String(T):T});return new b("Invalid "+G+" `"+M+"` of value `"+String(F)+"` "+("supplied to `"+j+"`, expected one of "+K+"."))}return R(N)}function Ie(E){function N(W,D,j,G,M){if(typeof E!="function")return new b("Property `"+M+"` of component `"+j+"` has invalid PropType notation inside objectOf.");var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type "+("`"+Y+"` supplied to `"+j+"`, expected an object."));for(var K in F)if(i(F,K)){var q=E(F,K,j,G,M+"."+K,o);if(q instanceof Error)return q}return null}return R(N)}function je(E){if(!Array.isArray(E))return s("Invalid argument supplied to oneOfType, expected an instance of array."),c;for(var N=0;N<E.length;N++){var W=E[N];if(typeof W!="function")return s("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+Dt(W)+" at index "+N+"."),c}function D(j,G,M,F,Y){for(var K=[],q=0;q<E.length;q++){var T=E[q],ne=T(j,G,M,F,Y,o);if(ne==null)return null;ne.data&&i(ne.data,"expectedType")&&K.push(ne.data.expectedType)}var Ce=K.length>0?", expected one of type ["+K.join(", ")+"]":"";return new b("Invalid "+F+" `"+Y+"` supplied to "+("`"+M+"`"+Ce+"."))}return R(D)}function Qe(){function E(N,W,D,j,G){return xe(N[W])?null:new b("Invalid "+j+" `"+G+"` supplied to "+("`"+D+"`, expected a ReactNode."))}return R(E)}function Me(E,N,W,D,j){return new b((E||"React class")+": "+N+" type `"+W+"."+D+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+j+"`.")}function Ue(E){function N(W,D,j,G,M){var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type `"+Y+"` "+("supplied to `"+j+"`, expected `object`."));for(var K in E){var q=E[K];if(typeof q!="function")return Me(j,G,M,K,Ae(q));var T=q(F,K,j,G,M+"."+K,o);if(T)return T}return null}return R(N)}function et(E){function N(W,D,j,G,M){var F=W[D],Y=ye(F);if(Y!=="object")return new b("Invalid "+G+" `"+M+"` of type `"+Y+"` "+("supplied to `"+j+"`, expected `object`."));var K=r({},W[D],E);for(var q in K){var T=E[q];if(i(E,q)&&typeof T!="function")return Me(j,G,M,q,Ae(T));if(!T)return new b("Invalid "+G+" `"+M+"` key `"+q+"` supplied to `"+j+"`.\nBad object: "+JSON.stringify(W[D],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(E),null,"  "));var ne=T(F,q,j,G,M+"."+q,o);if(ne)return ne}return null}return R(N)}function xe(E){switch(typeof E){case"number":case"string":case"undefined":return!0;case"boolean":return!E;case"object":if(Array.isArray(E))return E.every(xe);if(E===null||l(E))return!0;var N=h(E);if(N){var W=N.call(E),D;if(N!==E.entries){for(;!(D=W.next()).done;)if(!xe(D.value))return!1}else for(;!(D=W.next()).done;){var j=D.value;if(j&&!xe(j[1]))return!1}}else return!1;return!0;default:return!1}}function _e(E,N){return E==="symbol"?!0:N?N["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&N instanceof Symbol:!1}function ye(E){var N=typeof E;return Array.isArray(E)?"array":E instanceof RegExp?"object":_e(N,E)?"symbol":N}function Ae(E){if(typeof E>"u"||E===null)return""+E;var N=ye(E);if(N==="object"){if(E instanceof Date)return"date";if(E instanceof RegExp)return"regexp"}return N}function Dt(E){var N=Ae(E);switch(N){case"array":case"object":return"an "+N;case"boolean":case"date":case"regexp":return"a "+N;default:return N}}function bt(E){return!E.constructor||!E.constructor.name?d:E.constructor.name}return g.checkPropTypes=a,g.resetWarningCache=a.resetWarningCache,g.PropTypes=g,g}}}),Lo=ce({"../../node_modules/prop-types/index.js"(e,t){n=xr(),r=!0,t.exports=ko()(n.isElement,r);var n,r}}),jo='@keyframes Bc2PgW_ya{to{translate:0 var(--sh)}}@keyframes Bc2PgW_xa{to{translate:var(--xlp)0}}@keyframes Bc2PgW_r{50%{rotate:var(--hr)180deg}to{rotate:var(--r)360deg}}.Bc2PgW_c{z-index:1200;width:0;height:0;position:relative;overflow:visible}.Bc2PgW_p{animation:xa var(--dc)forwards cubic-bezier(var(--x1),var(--x2),var(--x3),var(--x4));animation-name:Bc2PgW_xa}.Bc2PgW_p>div{animation:ya var(--dc)forwards cubic-bezier(var(--y1),var(--y2),var(--y3),var(--y4));width:var(--w);height:var(--h);animation-name:Bc2PgW_ya;position:absolute;top:0;left:0}.Bc2PgW_p>div:before{content:"";background-color:var(--bgc);animation:r var(--rd)infinite linear;border-radius:var(--br);width:100%;height:100%;animation-name:Bc2PgW_r;display:block}',Nn="Bc2PgW_p",Mo="Bc2PgW_c",kn=["#FFC700","#FF0000","#2E3191","#41BBC7"],Ln=3500,jn=.5,Mn=150,Dn="mix",Fn=12,Bn="",Wn=!0,Hn=800,Un=1600;zn=200,Fo=800,Bo=.1,Wo=.3,Gn=.5,Tt=Math.abs,Ee=Math.random,lt=Math.round,Ho=Math.max,Yt=e=>document.createElement(e),qt=(e,t)=>e.appendChild(t),Yn=(e,t)=>Array.from({length:e},(n,r)=>({color:t[r%t.length],degree:360*r/e})),ze=(e,t=2)=>lt((e+Number.EPSILON)*10**t)/10**t,$t=(e,t,n,r,o)=>(e-t)*(o-r)/(n-t)+r,qn=(e,t)=>e+t>360?e+t-360:e+t,Nr=()=>Ee()>.5,$n=Object.entries,Uo=6,zo=e=>e!==1&&Nr();Yo=te.div({zIndex:9999,position:"fixed",top:0,left:"50%",width:"50%",height:"100%"}),qo=y.memo(function({timeToFade:e=5e3,colors:t=["#CA90FF","#FC521F","#66BF3C","#FF4785","#FFAE00","#1EA7FD"],...n}){return y.createElement(Yo,null,y.createElement(Go,{colors:t,particleCount:200,duration:5e3,stageHeight:window.innerHeight,stageWidth:window.innerWidth,destroyAfterDone:!0,...n}))});Kn="STORYBOOK_ADDON_ONBOARDING_CHANNEL";$o=kr("function"),Vo=e=>e===null,Jn=e=>Object.prototype.toString.call(e).slice(8,-1)==="RegExp",Xn=e=>!Ko(e)&&!Vo(e)&&($o(e)||typeof e=="object"),Ko=kr("undefined");ei=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],ti=["bigint","boolean","null","number","string","symbol","undefined"];oi=["innerHTML","ownerDocument","style","attributes","nodeValue"];I.array=Array.isArray;I.arrayOf=(e,t)=>!I.array(e)&&!I.function(t)?!1:e.every(n=>t(n));I.asyncGeneratorFunction=e=>kt(e)==="AsyncGeneratorFunction";I.asyncFunction=de("AsyncFunction");I.bigint=Ke("bigint");I.boolean=e=>e===!0||e===!1;I.date=de("Date");I.defined=e=>!I.undefined(e);I.domElement=e=>I.object(e)&&!I.plainObject(e)&&e.nodeType===1&&I.string(e.nodeName)&&oi.every(t=>t in e);I.empty=e=>I.string(e)&&e.length===0||I.array(e)&&e.length===0||I.object(e)&&!I.map(e)&&!I.set(e)&&Object.keys(e).length===0||I.set(e)&&e.size===0||I.map(e)&&e.size===0;I.error=de("Error");I.function=Ke("function");I.generator=e=>I.iterable(e)&&I.function(e.next)&&I.function(e.throw);I.generatorFunction=de("GeneratorFunction");I.instanceOf=(e,t)=>!e||!t?!1:Object.getPrototypeOf(e)===t.prototype;I.iterable=e=>!I.nullOrUndefined(e)&&I.function(e[Symbol.iterator]);I.map=de("Map");I.nan=e=>Number.isNaN(e);I.null=e=>e===null;I.nullOrUndefined=e=>I.null(e)||I.undefined(e);I.number=e=>Ke("number")(e)&&!I.nan(e);I.numericString=e=>I.string(e)&&e.length>0&&!Number.isNaN(Number(e));I.object=e=>!I.nullOrUndefined(e)&&(I.function(e)||typeof e=="object");I.oneOf=(e,t)=>I.array(e)?e.indexOf(t)>-1:!1;I.plainFunction=de("Function");I.plainObject=e=>{if(kt(e)!=="Object")return!1;let t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};I.primitive=e=>I.null(e)||ri(typeof e);I.promise=de("Promise");I.propertyOf=(e,t,n)=>{if(!I.object(e)||!t)return!1;let r=e[t];return I.function(n)?n(r):I.defined(r)};I.regexp=de("RegExp");I.set=de("Set");I.string=Ke("string");I.symbol=Ke("symbol");I.undefined=Ke("undefined");I.weakMap=de("WeakMap");I.weakSet=de("WeakSet");_=I;li=We(Po(),1),jr=We(Ro(),1),rr=We(xo(),1),It=We(on(),1),Ct=We(on(),1),O=We(Lo()),ut=typeof window<"u"&&typeof document<"u"&&typeof navigator<"u",ci=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(ut&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}();fi=ut&&window.Promise,di=fi?ui:pi;or=ut&&!!(window.MSInputMethodContext&&document.documentMode),ir=ut&&/MSIE 10/.test(navigator.userAgent);yi=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},gi=function(){function e(t,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),$e=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},le=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};Ai=ut&&/Firefox/i.test(navigator.userAgent);Jr=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Vt=Jr.slice(3);Kt={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"};Gi={shift:{order:100,enabled:!0,fn:Hi},offset:{order:200,enabled:!0,fn:Bi,offset:0},preventOverflow:{order:300,enabled:!0,fn:Wi,priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:Mi},arrow:{order:500,enabled:!0,fn:ki,element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:ji,behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:zi},hide:{order:800,enabled:!0,fn:Ui},computeStyle:{order:850,enabled:!0,fn:Ni,gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:Ri,onLoad:xi,gpuAcceleration:void 0}},Yi={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:Gi},Lt=function(){function e(t,n){var r=this,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};yi(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(r.update)},this.update=di(this.update.bind(this)),this.options=le({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(le({},e.Defaults.modifiers,o.modifiers)).forEach(function(a){r.options.modifiers[a]=le({},e.Defaults.modifiers[a]||{},o.modifiers?o.modifiers[a]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(a){return le({name:a},r.options.modifiers[a])}).sort(function(a,s){return a.order-s.order}),this.modifiers.forEach(function(a){a.enabled&&Mr(a.onLoad)&&a.onLoad(r.reference,r.popper,r.options,a,r.state)}),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return gi(e,[{key:"update",value:function(){return Oi.call(this)}},{key:"destroy",value:function(){return Si.call(this)}},{key:"enableEventListeners",value:function(){return Ti.call(this)}},{key:"disableEventListeners",value:function(){return Ci.call(this)}}]),e}();Lt.Utils=window.PopperUtils;Lt.placements=Jr;Lt.Defaults=Yi;cr=Lt,en=We(on()),qi=["innerHTML","ownerDocument","style","attributes","nodeValue"],$i=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],Vi=["bigint","boolean","null","number","string","symbol","undefined"];C.array=Array.isArray;C.arrayOf=function(e,t){return!C.array(e)&&!C.function(t)?!1:e.every(function(n){return t(n)})};C.asyncGeneratorFunction=function(e){return jt(e)==="AsyncGeneratorFunction"};C.asyncFunction=he("AsyncFunction");C.bigint=Xe("bigint");C.boolean=function(e){return e===!0||e===!1};C.date=he("Date");C.defined=function(e){return!C.undefined(e)};C.domElement=function(e){return C.object(e)&&!C.plainObject(e)&&e.nodeType===1&&C.string(e.nodeName)&&qi.every(function(t){return t in e})};C.empty=function(e){return C.string(e)&&e.length===0||C.array(e)&&e.length===0||C.object(e)&&!C.map(e)&&!C.set(e)&&Object.keys(e).length===0||C.set(e)&&e.size===0||C.map(e)&&e.size===0};C.error=he("Error");C.function=Xe("function");C.generator=function(e){return C.iterable(e)&&C.function(e.next)&&C.function(e.throw)};C.generatorFunction=he("GeneratorFunction");C.instanceOf=function(e,t){return!e||!t?!1:Object.getPrototypeOf(e)===t.prototype};C.iterable=function(e){return!C.nullOrUndefined(e)&&C.function(e[Symbol.iterator])};C.map=he("Map");C.nan=function(e){return Number.isNaN(e)};C.null=function(e){return e===null};C.nullOrUndefined=function(e){return C.null(e)||C.undefined(e)};C.number=function(e){return Xe("number")(e)&&!C.nan(e)};C.numericString=function(e){return C.string(e)&&e.length>0&&!Number.isNaN(Number(e))};C.object=function(e){return!C.nullOrUndefined(e)&&(C.function(e)||typeof e=="object")};C.oneOf=function(e,t){return C.array(e)?e.indexOf(t)>-1:!1};C.plainFunction=he("Function");C.plainObject=function(e){if(jt(e)!=="Object")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};C.primitive=function(e){return C.null(e)||Ji(typeof e)};C.promise=he("Promise");C.propertyOf=function(e,t,n){if(!C.object(e)||!t)return!1;var r=e[t];return C.function(n)?n(r):C.defined(r)};C.regexp=he("RegExp");C.set=he("Set");C.string=Xe("string");C.symbol=Xe("symbol");C.undefined=Xe("undefined");C.weakMap=he("WeakMap");C.weakSet=he("WeakSet");se=C;Xi=Xr("function"),Zi=function(e){return e===null},ur=function(e){return Object.prototype.toString.call(e).slice(8,-1)==="RegExp"},pr=function(e){return!Qi(e)&&!Zi(e)&&(Xi(e)||typeof e=="object")},Qi=Xr("undefined"),tn=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};oa=["innerHTML","ownerDocument","style","attributes","nodeValue"],ia=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],aa=["bigint","boolean","null","number","string","symbol","undefined"];P.array=Array.isArray;P.arrayOf=function(e,t){return!P.array(e)&&!P.function(t)?!1:e.every(function(n){return t(n)})};P.asyncGeneratorFunction=function(e){return Mt(e)==="AsyncGeneratorFunction"};P.asyncFunction=me("AsyncFunction");P.bigint=Ze("bigint");P.boolean=function(e){return e===!0||e===!1};P.date=me("Date");P.defined=function(e){return!P.undefined(e)};P.domElement=function(e){return P.object(e)&&!P.plainObject(e)&&e.nodeType===1&&P.string(e.nodeName)&&oa.every(function(t){return t in e})};P.empty=function(e){return P.string(e)&&e.length===0||P.array(e)&&e.length===0||P.object(e)&&!P.map(e)&&!P.set(e)&&Object.keys(e).length===0||P.set(e)&&e.size===0||P.map(e)&&e.size===0};P.error=me("Error");P.function=Ze("function");P.generator=function(e){return P.iterable(e)&&P.function(e.next)&&P.function(e.throw)};P.generatorFunction=me("GeneratorFunction");P.instanceOf=function(e,t){return!e||!t?!1:Object.getPrototypeOf(e)===t.prototype};P.iterable=function(e){return!P.nullOrUndefined(e)&&P.function(e[Symbol.iterator])};P.map=me("Map");P.nan=function(e){return Number.isNaN(e)};P.null=function(e){return e===null};P.nullOrUndefined=function(e){return P.null(e)||P.undefined(e)};P.number=function(e){return Ze("number")(e)&&!P.nan(e)};P.numericString=function(e){return P.string(e)&&e.length>0&&!Number.isNaN(Number(e))};P.object=function(e){return!P.nullOrUndefined(e)&&(P.function(e)||typeof e=="object")};P.oneOf=function(e,t){return P.array(e)?e.indexOf(t)>-1:!1};P.plainFunction=me("Function");P.plainObject=function(e){if(Mt(e)!=="Object")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};P.primitive=function(e){return P.null(e)||la(typeof e)};P.promise=me("Promise");P.propertyOf=function(e,t,n){if(!P.object(e)||!t)return!1;var r=e[t];return P.function(n)?n(r):P.defined(r)};P.regexp=me("RegExp");P.set=me("Set");P.string=Ze("string");P.symbol=Ze("symbol");P.undefined=Ze("undefined");P.weakMap=me("WeakMap");P.weakSet=me("WeakSet");k=P;ga={flip:{padding:20},preventOverflow:{padding:10}},ba="The typeValidator argument must be a function with the signature function(props, propName, componentName).",va="The error message is optional, but must be a string if provided.";H={INIT:"init",IDLE:"idle",OPENING:"opening",OPEN:"open",CLOSING:"closing",ERROR:"error"},it=Fe.createPortal!==void 0;to=function(e){mt(n,e);var t=yt(n);function n(){return dt(this,n),t.apply(this,arguments)}return ht(n,[{key:"componentDidMount",value:function(){Oe()&&(this.node||this.appendNode(),it||this.renderPortal())}},{key:"componentDidUpdate",value:function(){Oe()&&(it||this.renderPortal())}},{key:"componentWillUnmount",value:function(){!Oe()||!this.node||(it||Fe.unmountComponentAtNode(this.node),this.node&&this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=void 0))}},{key:"appendNode",value:function(){var r=this.props,o=r.id,i=r.zIndex;this.node||(this.node=document.createElement("div"),o&&(this.node.id=o),i&&(this.node.style.zIndex=i),document.body.appendChild(this.node))}},{key:"renderPortal",value:function(){if(!Oe())return null;var r=this.props,o=r.children,i=r.setRef;if(this.node||this.appendNode(),it)return Fe.createPortal(o,this.node);var a=Fe.unstable_renderSubtreeIntoContainer(this,o.length>1?y.createElement("div",null,o):o[0],this.node);return i(a),null}},{key:"renderReact16",value:function(){var r=this.props,o=r.hasChildren,i=r.placement,a=r.target;return o?this.renderPortal():a||i==="center"?this.renderPortal():null}},{key:"render",value:function(){return it?this.renderReact16():null}}]),n}(y.Component);re(to,"propTypes",{children:O.default.oneOfType([O.default.element,O.default.array]),hasChildren:O.default.bool,id:O.default.oneOfType([O.default.string,O.default.number]),placement:O.default.string,setRef:O.default.func.isRequired,target:O.default.oneOfType([O.default.object,O.default.string]),zIndex:O.default.number});no=function(e){mt(n,e);var t=yt(n);function n(){return dt(this,n),t.apply(this,arguments)}return ht(n,[{key:"parentStyle",get:function(){var r=this.props,o=r.placement,i=r.styles,a=i.arrow.length,s={pointerEvents:"none",position:"absolute",width:"100%"};return o.startsWith("top")?(s.bottom=0,s.left=0,s.right=0,s.height=a):o.startsWith("bottom")?(s.left=0,s.right=0,s.top=0,s.height=a):o.startsWith("left")?(s.right=0,s.top=0,s.bottom=0):o.startsWith("right")&&(s.left=0,s.top=0),s}},{key:"render",value:function(){var r=this.props,o=r.placement,i=r.setArrowRef,a=r.styles,s=a.arrow,c=s.color,l=s.display,p=s.length,u=s.margin,f=s.position,h=s.spread,d={display:l,position:f},g,S=h,b=p;return o.startsWith("top")?(g="0,0 ".concat(S/2,",").concat(b," ").concat(S,",0"),d.bottom=0,d.marginLeft=u,d.marginRight=u):o.startsWith("bottom")?(g="".concat(S,",").concat(b," ").concat(S/2,",0 0,").concat(b),d.top=0,d.marginLeft=u,d.marginRight=u):o.startsWith("left")?(b=h,S=p,g="0,0 ".concat(S,",").concat(b/2," 0,").concat(b),d.right=0,d.marginTop=u,d.marginBottom=u):o.startsWith("right")&&(b=h,S=p,g="".concat(S,",").concat(b," ").concat(S,",0 0,").concat(b/2),d.left=0,d.marginTop=u,d.marginBottom=u),y.createElement("div",{className:"__floater__arrow",style:this.parentStyle},y.createElement("span",{ref:i,style:d},y.createElement("svg",{width:S,height:b,version:"1.1",xmlns:"http://www.w3.org/2000/svg"},y.createElement("polygon",{points:g,fill:c}))))}}]),n}(y.Component);re(no,"propTypes",{placement:O.default.string.isRequired,setArrowRef:O.default.func.isRequired,styles:O.default.object.isRequired});Pa=["color","height","width"];ro.propTypes={handleClick:O.default.func.isRequired,styles:O.default.object.isRequired};oo.propTypes={content:O.default.node.isRequired,footer:O.default.node,handleClick:O.default.func.isRequired,open:O.default.bool,positionWrapper:O.default.bool.isRequired,showCloseButton:O.default.bool.isRequired,styles:O.default.object.isRequired,title:O.default.node};io=function(e){mt(n,e);var t=yt(n);function n(){return dt(this,n),t.apply(this,arguments)}return ht(n,[{key:"style",get:function(){var r=this.props,o=r.disableAnimation,i=r.component,a=r.placement,s=r.hideArrow,c=r.status,l=r.styles,p=l.arrow.length,u=l.floater,f=l.floaterCentered,h=l.floaterClosing,d=l.floaterOpening,g=l.floaterWithAnimation,S=l.floaterWithComponent,b={};return s||(a.startsWith("top")?b.padding="0 0 ".concat(p,"px"):a.startsWith("bottom")?b.padding="".concat(p,"px 0 0"):a.startsWith("left")?b.padding="0 ".concat(p,"px 0 0"):a.startsWith("right")&&(b.padding="0 0 0 ".concat(p,"px"))),[H.OPENING,H.OPEN].indexOf(c)!==-1&&(b=J(J({},b),d)),c===H.CLOSING&&(b=J(J({},b),h)),c===H.OPEN&&!o&&(b=J(J({},b),g)),a==="center"&&(b=J(J({},b),f)),i&&(b=J(J({},b),S)),J(J({},u),b)}},{key:"render",value:function(){var r=this.props,o=r.component,i=r.handleClick,a=r.hideArrow,s=r.setFloaterRef,c=r.status,l={},p=["__floater"];return o?y.isValidElement(o)?l.content=y.cloneElement(o,{closeFn:i}):l.content=o({closeFn:i}):l.content=y.createElement(oo,this.props),c===H.OPEN&&p.push("__floater__open"),a||(l.arrow=y.createElement(no,this.props)),y.createElement("div",{ref:s,className:p.join(" "),style:this.style},y.createElement("div",{className:"__floater__body"},l.content,l.arrow))}}]),n}(y.Component);re(io,"propTypes",{component:O.default.oneOfType([O.default.func,O.default.element]),content:O.default.node,disableAnimation:O.default.bool.isRequired,footer:O.default.node,handleClick:O.default.func.isRequired,hideArrow:O.default.bool.isRequired,open:O.default.bool,placement:O.default.string.isRequired,positionWrapper:O.default.bool.isRequired,setArrowRef:O.default.func.isRequired,setFloaterRef:O.default.func.isRequired,showCloseButton:O.default.bool,status:O.default.string.isRequired,styles:O.default.object.isRequired,title:O.default.node});ao=function(e){mt(n,e);var t=yt(n);function n(){return dt(this,n),t.apply(this,arguments)}return ht(n,[{key:"render",value:function(){var r=this.props,o=r.children,i=r.handleClick,a=r.handleMouseEnter,s=r.handleMouseLeave,c=r.setChildRef,l=r.setWrapperRef,p=r.style,u=r.styles,f;if(o)if(y.Children.count(o)===1)if(!y.isValidElement(o))f=y.createElement("span",null,o);else{var h=se.function(o.type)?"innerRef":"ref";f=y.cloneElement(y.Children.only(o),re({},h,c))}else f=o;return f?y.createElement("span",{ref:l,style:J(J({},u),p),onClick:i,onMouseEnter:a,onMouseLeave:s},f):null}}]),n}(y.Component);re(ao,"propTypes",{children:O.default.node,handleClick:O.default.func.isRequired,handleMouseEnter:O.default.func.isRequired,handleMouseLeave:O.default.func.isRequired,setChildRef:O.default.func.isRequired,setWrapperRef:O.default.func.isRequired,style:O.default.object,styles:O.default.object.isRequired});Ra={zIndex:100};_a=["arrow","flip","offset"],Aa=["position","top","right","bottom","left"],dn=function(e){mt(n,e);var t=yt(n);function n(r){var o;return dt(this,n),o=t.call(this,r),re(Pe(o),"setArrowRef",function(i){o.arrowRef=i}),re(Pe(o),"setChildRef",function(i){o.childRef=i}),re(Pe(o),"setFloaterRef",function(i){o.floaterRef=i}),re(Pe(o),"setWrapperRef",function(i){o.wrapperRef=i}),re(Pe(o),"handleTransitionEnd",function(){var i=o.state.status,a=o.props.callback;o.wrapperPopper&&o.wrapperPopper.instance.update(),o.setState({status:i===H.OPENING?H.OPEN:H.IDLE},function(){var s=o.state.status;a(s===H.OPEN?"open":"close",o.props)})}),re(Pe(o),"handleClick",function(){var i=o.props,a=i.event,s=i.open;if(!se.boolean(s)){var c=o.state,l=c.positionWrapper,p=c.status;(o.event==="click"||o.event==="hover"&&l)&&(Pt({title:"click",data:[{event:a,status:p===H.OPEN?"closing":"opening"}],debug:o.debug}),o.toggle())}}),re(Pe(o),"handleMouseEnter",function(){var i=o.props,a=i.event,s=i.open;if(!(se.boolean(s)||Jt())){var c=o.state.status;o.event==="hover"&&c===H.IDLE&&(Pt({title:"mouseEnter",data:[{key:"originalEvent",value:a}],debug:o.debug}),clearTimeout(o.eventDelayTimeout),o.toggle())}}),re(Pe(o),"handleMouseLeave",function(){var i=o.props,a=i.event,s=i.eventDelay,c=i.open;if(!(se.boolean(c)||Jt())){var l=o.state,p=l.status,u=l.positionWrapper;o.event==="hover"&&(Pt({title:"mouseLeave",data:[{key:"originalEvent",value:a}],debug:o.debug}),s?[H.OPENING,H.OPEN].indexOf(p)!==-1&&!u&&!o.eventDelayTimeout&&(o.eventDelayTimeout=setTimeout(function(){delete o.eventDelayTimeout,o.toggle()},s*1e3)):o.toggle(H.IDLE))}}),o.state={currentPlacement:r.placement,needsUpdate:!1,positionWrapper:r.wrapperOptions.position&&!!r.target,status:H.INIT,statusWrapper:H.INIT},o._isMounted=!1,o.hasMounted=!1,Oe()&&window.addEventListener("load",function(){o.popper&&o.popper.instance.update(),o.wrapperPopper&&o.wrapperPopper.instance.update()}),o}return ht(n,[{key:"componentDidMount",value:function(){if(Oe()){var r=this.state.positionWrapper,o=this.props,i=o.children,a=o.open,s=o.target;this._isMounted=!0,Pt({title:"init",data:{hasChildren:!!i,hasTarget:!!s,isControlled:se.boolean(a),positionWrapper:r,target:this.target,floater:this.floaterRef},debug:this.debug}),this.hasMounted||(this.initPopper(),this.hasMounted=!0),!i&&s&&se.boolean(a)}}},{key:"componentDidUpdate",value:function(r,o){if(Oe()){var i=this.props,a=i.autoOpen,s=i.open,c=i.target,l=i.wrapperOptions,p=fa(o,this.state),u=p.changedFrom,f=p.changed;if(r.open!==s){var h;se.boolean(s)&&(h=s?H.OPENING:H.CLOSING),this.toggle(h)}(r.wrapperOptions.position!==l.position||r.target!==c)&&this.changeWrapperPosition(this.props),f("status",H.IDLE)&&s?this.toggle(H.OPEN):u("status",H.INIT,H.IDLE)&&a&&this.toggle(H.OPEN),this.popper&&f("status",H.OPENING)&&this.popper.instance.update(),this.floaterRef&&(f("status",H.OPENING)||f("status",H.CLOSING))&&Ca(this.floaterRef,"transitionend",this.handleTransitionEnd),f("needsUpdate",!0)&&this.rebuildPopper()}}},{key:"componentWillUnmount",value:function(){Oe()&&(this._isMounted=!1,this.popper&&this.popper.instance.destroy(),this.wrapperPopper&&this.wrapperPopper.instance.destroy())}},{key:"initPopper",value:function(){var r=this,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.target,i=this.state.positionWrapper,a=this.props,s=a.disableFlip,c=a.getPopper,l=a.hideArrow,p=a.offset,u=a.placement,f=a.wrapperOptions,h=u==="top"||u==="bottom"?"flip":["right","bottom-end","top-end","left","top-start","bottom-start"];if(u==="center")this.setState({status:H.IDLE});else if(o&&this.floaterRef){var d=this.options,g=d.arrow,S=d.flip,b=d.offset,R=Qr(d,_a);new cr(o,this.floaterRef,{placement:u,modifiers:J({arrow:J({enabled:!l,element:this.arrowRef},g),flip:J({enabled:!s,behavior:h},S),offset:J({offset:"0, ".concat(p,"px")},b)},R),onCreate:function(v){var w;if(r.popper=v,!((w=r.floaterRef)!==null&&w!==void 0&&w.isConnected)){r.setState({needsUpdate:!0});return}c(v,"floater"),r._isMounted&&r.setState({currentPlacement:v.placement,status:H.IDLE}),u!==v.placement&&setTimeout(function(){v.instance.update()},1)},onUpdate:function(v){r.popper=v;var w=r.state.currentPlacement;r._isMounted&&v.placement!==w&&r.setState({currentPlacement:v.placement})}})}if(i){var m=se.undefined(f.offset)?0:f.offset;new cr(this.target,this.wrapperRef,{placement:f.placement||u,modifiers:{arrow:{enabled:!1},offset:{offset:"0, ".concat(m,"px")},flip:{enabled:!1}},onCreate:function(v){r.wrapperPopper=v,r._isMounted&&r.setState({statusWrapper:H.IDLE}),c(v,"wrapper"),u!==v.placement&&setTimeout(function(){v.instance.update()},1)}})}}},{key:"rebuildPopper",value:function(){var r=this;this.floaterRefInterval=setInterval(function(){var o;(o=r.floaterRef)!==null&&o!==void 0&&o.isConnected&&(clearInterval(r.floaterRefInterval),r.setState({needsUpdate:!1}),r.initPopper())},50)}},{key:"changeWrapperPosition",value:function(r){var o=r.target,i=r.wrapperOptions;this.setState({positionWrapper:i.position&&!!o})}},{key:"toggle",value:function(r){var o=this.state.status,i=o===H.OPEN?H.CLOSING:H.OPENING;se.undefined(r)||(i=r),this.setState({status:i})}},{key:"debug",get:function(){var r=this.props.debug;return r||Oe()&&"ReactFloaterDebug"in window&&!!window.ReactFloaterDebug}},{key:"event",get:function(){var r=this.props,o=r.disableHoverToClick,i=r.event;return i==="hover"&&Jt()&&!o?"click":i}},{key:"options",get:function(){var r=this.props.options;return(0,en.default)(ga,r||{})}},{key:"styles",get:function(){var r=this,o=this.state,i=o.status,a=o.positionWrapper,s=o.statusWrapper,c=this.props.styles,l=(0,en.default)(xa(c),c);if(a){var p;[H.IDLE].indexOf(i)===-1||[H.IDLE].indexOf(s)===-1?p=l.wrapperPosition:p=this.wrapperPopper.styles,l.wrapper=J(J({},l.wrapper),p)}if(this.target){var u=window.getComputedStyle(this.target);this.wrapperStyles?l.wrapper=J(J({},l.wrapper),this.wrapperStyles):["relative","static"].indexOf(u.position)===-1&&(this.wrapperStyles={},a||(Aa.forEach(function(f){r.wrapperStyles[f]=u[f]}),l.wrapper=J(J({},l.wrapper),this.wrapperStyles),this.target.style.position="relative",this.target.style.top="auto",this.target.style.right="auto",this.target.style.bottom="auto",this.target.style.left="auto"))}return l}},{key:"target",get:function(){if(!Oe())return null;var r=this.props.target;return r?se.domElement(r)?r:document.querySelector(r):this.childRef||this.wrapperRef}},{key:"render",value:function(){var r=this.state,o=r.currentPlacement,i=r.positionWrapper,a=r.status,s=this.props,c=s.children,l=s.component,p=s.content,u=s.disableAnimation,f=s.footer,h=s.hideArrow,d=s.id,g=s.open,S=s.showCloseButton,b=s.style,R=s.target,m=s.title,v=y.createElement(ao,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:b,styles:this.styles.wrapper},c),w={};return i?w.wrapperInPortal=v:w.wrapperAsChildren=v,y.createElement("span",null,y.createElement(to,{hasChildren:!!c,id:d,placement:o,setRef:this.setFloaterRef,target:R,zIndex:this.styles.options.zIndex},y.createElement(io,{component:l,content:p,disableAnimation:u,footer:f,handleClick:this.handleClick,hideArrow:h||o==="center",open:g,placement:o,positionWrapper:i,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:S,status:a,styles:this.styles,title:m}),w.wrapperInPortal),w.wrapperAsChildren)}}]),n}(y.Component);re(dn,"propTypes",{autoOpen:O.default.bool,callback:O.default.func,children:O.default.node,component:vr(O.default.oneOfType([O.default.func,O.default.element]),function(e){return!e.content}),content:vr(O.default.node,function(e){return!e.component}),debug:O.default.bool,disableAnimation:O.default.bool,disableFlip:O.default.bool,disableHoverToClick:O.default.bool,event:O.default.oneOf(["hover","click"]),eventDelay:O.default.number,footer:O.default.node,getPopper:O.default.func,hideArrow:O.default.bool,id:O.default.oneOfType([O.default.string,O.default.number]),offset:O.default.number,open:O.default.bool,options:O.default.object,placement:O.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto","center"]),showCloseButton:O.default.bool,style:O.default.object,styles:O.default.object,target:O.default.oneOfType([O.default.object,O.default.string]),title:O.default.node,wrapperOptions:O.default.shape({offset:O.default.number,placement:O.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto"]),position:O.default.bool})});re(dn,"defaultProps",{autoOpen:!1,callback:Er,debug:!1,disableAnimation:!1,disableFlip:!1,disableHoverToClick:!1,event:"click",eventDelay:.4,getPopper:Er,hideArrow:!1,offset:15,placement:"bottom",showCloseButton:!1,styles:{},target:null,wrapperOptions:{position:!1}});Na=Object.defineProperty,ka=(e,t,n)=>t in e?Na(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,A=(e,t,n)=>ka(e,typeof t!="symbol"?t+"":t,n),$={INIT:"init",START:"start",STOP:"stop",RESET:"reset",PREV:"prev",NEXT:"next",GO:"go",CLOSE:"close",SKIP:"skip",UPDATE:"update"},fe={TOUR_START:"tour:start",STEP_BEFORE:"step:before",BEACON:"beacon",TOOLTIP:"tooltip",STEP_AFTER:"step:after",TOUR_END:"tour:end",TOUR_STATUS:"tour:status",TARGET_NOT_FOUND:"error:target_not_found",ERROR:"error"},L={INIT:"init",READY:"ready",BEACON:"beacon",TOOLTIP:"tooltip",COMPLETE:"complete",ERROR:"error"},B={IDLE:"idle",READY:"ready",WAITING:"waiting",RUNNING:"running",PAUSED:"paused",SKIPPED:"skipped",FINISHED:"finished",ERROR:"error"};at=Et!==void 0;qa={options:{preventOverflow:{boundariesElement:"scrollParent"}},wrapperOptions:{offset:-18,position:!0}},uo={back:"Back",close:"Close",last:"Last",next:"Next",nextLabelWithProgress:"Next (Step {step} of {steps})",open:"Open the dialog",skip:"Skip"},$a={event:"click",placement:"bottom",offset:10,disableBeacon:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrollParentFix:!1,disableScrolling:!1,hideBackButton:!1,hideCloseButton:!1,hideFooter:!1,isFixed:!1,locale:uo,showProgress:!1,showSkipButton:!1,spotlightClicks:!1,spotlightPadding:10},Va={continuous:!1,debug:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrolling:!1,disableScrollParentFix:!1,getHelpers:void 0,hideBackButton:!1,run:!0,scrollOffset:20,scrollDuration:300,scrollToFirstStep:!1,showSkipButton:!1,showProgress:!1,spotlightClicks:!1,spotlightPadding:10,steps:[]},Ka={arrowColor:"#fff",backgroundColor:"#fff",beaconSize:36,overlayColor:"rgba(0, 0, 0, 0.5)",primaryColor:"#f04",spotlightShadow:"0 0 15px rgba(0, 0, 0, 0.5)",textColor:"#333",width:380,zIndex:100},st={backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",cursor:"pointer",fontSize:16,lineHeight:1,padding:8,WebkitAppearance:"none"},wr={borderRadius:4,position:"absolute"};fo={action:"init",controlled:!1,index:0,lifecycle:L.INIT,origin:null,size:0,status:B.IDLE},Ir=za(co(fo,"controlled","size")),Za=class{constructor(e){A(this,"beaconPopper"),A(this,"tooltipPopper"),A(this,"data",new Map),A(this,"listener"),A(this,"store",new Map),A(this,"addListener",o=>{this.listener=o}),A(this,"setSteps",o=>{let{size:i,status:a}=this.getState(),s={size:o.length,status:a};this.data.set("steps",o),a===B.WAITING&&!i&&o.length&&(s.status=B.RUNNING),this.setState(s)}),A(this,"getPopper",o=>o==="beacon"?this.beaconPopper:this.tooltipPopper),A(this,"setPopper",(o,i)=>{o==="beacon"?this.beaconPopper=i:this.tooltipPopper=i}),A(this,"cleanupPoppers",()=>{this.beaconPopper=null,this.tooltipPopper=null}),A(this,"close",(o=null)=>{let{index:i,status:a}=this.getState();a===B.RUNNING&&this.setState({...this.getNextState({action:$.CLOSE,index:i+1,origin:o})})}),A(this,"go",o=>{let{controlled:i,status:a}=this.getState();if(i||a!==B.RUNNING)return;let s=this.getSteps()[o];this.setState({...this.getNextState({action:$.GO,index:o}),status:s?a:B.FINISHED})}),A(this,"info",()=>this.getState()),A(this,"next",()=>{let{index:o,status:i}=this.getState();i===B.RUNNING&&this.setState(this.getNextState({action:$.NEXT,index:o+1}))}),A(this,"open",()=>{let{status:o}=this.getState();o===B.RUNNING&&this.setState({...this.getNextState({action:$.UPDATE,lifecycle:L.TOOLTIP})})}),A(this,"prev",()=>{let{index:o,status:i}=this.getState();i===B.RUNNING&&this.setState({...this.getNextState({action:$.PREV,index:o-1})})}),A(this,"reset",(o=!1)=>{let{controlled:i}=this.getState();i||this.setState({...this.getNextState({action:$.RESET,index:0}),status:o?B.RUNNING:B.READY})}),A(this,"skip",()=>{let{status:o}=this.getState();o===B.RUNNING&&this.setState({action:$.SKIP,lifecycle:L.INIT,status:B.SKIPPED})}),A(this,"start",o=>{let{index:i,size:a}=this.getState();this.setState({...this.getNextState({action:$.START,index:_.number(o)?o:i},!0),status:a?B.RUNNING:B.WAITING})}),A(this,"stop",(o=!1)=>{let{index:i,status:a}=this.getState();[B.FINISHED,B.SKIPPED].includes(a)||this.setState({...this.getNextState({action:$.STOP,index:i+(o?1:0)}),status:B.PAUSED})}),A(this,"update",o=>{var i,a;if(!Ha(o,Ir))throw new Error(`State is not valid. Valid keys: ${Ir.join(", ")}`);this.setState({...this.getNextState({...this.getState(),...o,action:(i=o.action)!=null?i:$.UPDATE,origin:(a=o.origin)!=null?a:null},!0)})});let{continuous:t=!1,stepIndex:n,steps:r=[]}=e??{};this.setState({action:$.INIT,controlled:_.number(n),continuous:t,index:_.number(n)?n:0,lifecycle:L.INIT,origin:null,status:r.length?B.READY:B.IDLE},!0),this.beaconPopper=null,this.tooltipPopper=null,this.listener=null,this.setSteps(r)}getState(){return this.store.size?{action:this.store.get("action")||"",controlled:this.store.get("controlled")||!1,index:parseInt(this.store.get("index"),10),lifecycle:this.store.get("lifecycle")||"",origin:this.store.get("origin")||null,size:this.store.get("size")||0,status:this.store.get("status")||""}:{...fo}}getNextState(e,t=!1){var n,r,o,i,a;let{action:s,controlled:c,index:l,size:p,status:u}=this.getState(),f=_.number(e.index)?e.index:l,h=c&&!t?l:Math.min(Math.max(f,0),p);return{action:(n=e.action)!=null?n:s,controlled:c,index:h,lifecycle:(r=e.lifecycle)!=null?r:L.INIT,origin:(o=e.origin)!=null?o:null,size:(i=e.size)!=null?i:p,status:h===p?B.FINISHED:(a=e.status)!=null?a:u}}getSteps(){let e=this.data.get("steps");return Array.isArray(e)?e:[]}hasUpdatedState(e){let t=JSON.stringify(e),n=JSON.stringify(this.getState());return t!==n}setState(e,t=!1){let n=this.getState(),{action:r,index:o,lifecycle:i,origin:a=null,size:s,status:c}={...n,...e};this.store.set("action",r),this.store.set("index",o),this.store.set("lifecycle",i),this.store.set("origin",a),this.store.set("size",s),this.store.set("status",c),t&&(this.store.set("controlled",e.controlled),this.store.set("continuous",e.continuous)),this.listener&&this.hasUpdatedState(n)&&this.listener(this.getState())}getHelpers(){return{close:this.close,go:this.go,info:this.info,next:this.next,open:this.open,prev:this.prev,reset:this.reset,skip:this.skip}}};ts=es,ns=class extends De{constructor(){super(...arguments),A(this,"isActive",!1),A(this,"resizeTimeout"),A(this,"scrollTimeout"),A(this,"scrollParent"),A(this,"state",{isScrolling:!1,mouseOverSpotlight:!1,showSpotlight:!0}),A(this,"hideSpotlight",()=>{let{continuous:e,disableOverlay:t,lifecycle:n}=this.props,r=[L.INIT,L.BEACON,L.COMPLETE,L.ERROR];return t||(e?r.includes(n):n!==L.TOOLTIP)}),A(this,"handleMouseMove",e=>{let{mouseOverSpotlight:t}=this.state,{height:n,left:r,position:o,top:i,width:a}=this.spotlightStyles,s=o==="fixed"?e.clientY:e.pageY,c=o==="fixed"?e.clientX:e.pageX,l=s>=i&&s<=i+n,p=c>=r&&c<=r+a&&l;p!==t&&this.updateState({mouseOverSpotlight:p})}),A(this,"handleScroll",()=>{let{target:e}=this.props,t=Re(e);if(this.scrollParent!==document){let{isScrolling:n}=this.state;n||this.updateState({isScrolling:!0,showSpotlight:!1}),clearTimeout(this.scrollTimeout),this.scrollTimeout=window.setTimeout(()=>{this.updateState({isScrolling:!1,showSpotlight:!0})},50)}else Ve(t,"sticky")&&this.updateState({})}),A(this,"handleResize",()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(()=>{this.isActive&&this.forceUpdate()},100)})}componentDidMount(){let{debug:e,disableScrolling:t,disableScrollParentFix:n=!1,target:r}=this.props,o=Re(r);this.scrollParent=ct(o??document.body,n,!0),this.isActive=!0,!t&&gt(o,!0)&&Le({title:"step has a custom scroll parent and can cause trouble with scrolling",data:[{key:"parent",value:this.scrollParent}],debug:e}),window.addEventListener("resize",this.handleResize)}componentDidUpdate(e){var t;let{disableScrollParentFix:n,lifecycle:r,spotlightClicks:o,target:i}=this.props,{changed:a}=xt(e,this.props);if(a("target")||a("disableScrollParentFix")){let s=Re(i);this.scrollParent=ct(s??document.body,n,!0)}a("lifecycle",L.TOOLTIP)&&((t=this.scrollParent)==null||t.addEventListener("scroll",this.handleScroll,{passive:!0}),setTimeout(()=>{let{isScrolling:s}=this.state;s||this.updateState({showSpotlight:!0})},100)),(a("spotlightClicks")||a("disableOverlay")||a("lifecycle"))&&(o&&r===L.TOOLTIP?window.addEventListener("mousemove",this.handleMouseMove,!1):r!==L.TOOLTIP&&window.removeEventListener("mousemove",this.handleMouseMove))}componentWillUnmount(){var e;this.isActive=!1,window.removeEventListener("mousemove",this.handleMouseMove),window.removeEventListener("resize",this.handleResize),clearTimeout(this.resizeTimeout),clearTimeout(this.scrollTimeout),(e=this.scrollParent)==null||e.removeEventListener("scroll",this.handleScroll)}get overlayStyles(){let{mouseOverSpotlight:e}=this.state,{disableOverlayClose:t,placement:n,styles:r}=this.props,o=r.overlay;return Sr()&&(o=n==="center"?r.overlayLegacyCenter:r.overlayLegacy),{cursor:t?"default":"pointer",height:La(),pointerEvents:e?"none":"auto",...o}}get spotlightStyles(){var e,t,n;let{showSpotlight:r}=this.state,{disableScrollParentFix:o=!1,spotlightClicks:i,spotlightPadding:a=0,styles:s,target:c}=this.props,l=Re(c),p=so(l),u=Ve(l),f=Fa(l,a,o);return{...Sr()?s.spotlightLegacy:s.spotlight,height:Math.round(((e=p?.height)!=null?e:0)+a*2),left:Math.round(((t=p?.left)!=null?t:0)-a),opacity:r?1:0,pointerEvents:i?"none":"auto",position:u?"fixed":"absolute",top:f,transition:"opacity 0.2s",width:Math.round(((n=p?.width)!=null?n:0)+a*2)}}updateState(e){this.isActive&&this.setState(t=>({...t,...e}))}render(){let{showSpotlight:e}=this.state,{onClickOverlay:t,placement:n}=this.props,{hideSpotlight:r,overlayStyles:o,spotlightStyles:i}=this;if(r())return null;let a=n!=="center"&&e&&V(ts,{styles:i});if(lo()==="safari"){let{mixBlendMode:s,zIndex:c,...l}=o;a=V("div",{style:{...l}},a),delete o.backgroundColor}return V("div",{className:"react-joyride__overlay","data-test-id":"overlay",onClick:t,role:"presentation",style:o},a)}},rs=class extends De{constructor(){super(...arguments),A(this,"node",null)}componentDidMount(){let{id:e}=this.props;Ne()&&(this.node=document.createElement("div"),this.node.id=e,document.body.appendChild(this.node),at||this.renderReact15())}componentDidUpdate(){Ne()&&(at||this.renderReact15())}componentWillUnmount(){!Ne()||!this.node||(at||bn(this.node),this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=null))}renderReact15(){if(!Ne())return;let{children:e}=this.props;this.node&&vn(this,e,this.node)}renderReact16(){if(!Ne()||!at)return null;let{children:e}=this.props;return this.node?Et(e,this.node):null}render(){return at?this.renderReact16():null}},os=class{constructor(e,t){if(A(this,"element"),A(this,"options"),A(this,"canBeTabbed",n=>{let{tabIndex:r}=n;return r===null||r<0?!1:this.canHaveFocus(n)}),A(this,"canHaveFocus",n=>{let r=/input|select|textarea|button|object/,o=n.nodeName.toLowerCase();return(r.test(o)&&!n.getAttribute("disabled")||o==="a"&&!!n.getAttribute("href"))&&this.isVisible(n)}),A(this,"findValidTabElements",()=>[].slice.call(this.element.querySelectorAll("*"),0).filter(this.canBeTabbed)),A(this,"handleKeyDown",n=>{let{code:r="Tab"}=this.options;n.code===r&&this.interceptTab(n)}),A(this,"interceptTab",n=>{n.preventDefault();let r=this.findValidTabElements(),{shiftKey:o}=n;if(!r.length)return;let i=document.activeElement?r.indexOf(document.activeElement):0;i===-1||!o&&i+1===r.length?i=0:o&&i===0?i=r.length-1:i+=o?-1:1,r[i].focus()}),A(this,"isHidden",n=>{let r=n.offsetWidth<=0&&n.offsetHeight<=0,o=window.getComputedStyle(n);return r&&!n.innerHTML?!0:r&&o.getPropertyValue("overflow")!=="visible"||o.getPropertyValue("display")==="none"}),A(this,"isVisible",n=>{let r=n;for(;r;)if(r instanceof HTMLElement){if(r===document.body)break;if(this.isHidden(r))return!1;r=r.parentNode}return!0}),A(this,"removeScope",()=>{window.removeEventListener("keydown",this.handleKeyDown)}),A(this,"checkFocus",n=>{document.activeElement!==n&&(n.focus(),window.requestAnimationFrame(()=>this.checkFocus(n)))}),A(this,"setFocus",()=>{let{selector:n}=this.options;if(!n)return;let r=this.element.querySelector(n);r&&window.requestAnimationFrame(()=>this.checkFocus(r))}),!(e instanceof HTMLElement))throw new TypeError("Invalid parameter: element must be an HTMLElement");this.element=e,this.options=t,window.addEventListener("keydown",this.handleKeyDown,!1),this.setFocus()}},is=class extends De{constructor(e){if(super(e),A(this,"beacon",null),A(this,"setBeaconRef",r=>{this.beacon=r}),e.beaconComponent)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.id="joyride-beacon-animation",e.nonce&&n.setAttribute("nonce",e.nonce),n.appendChild(document.createTextNode(`
        @keyframes joyride-beacon-inner {
          20% {
            opacity: 0.9;
          }
        
          90% {
            opacity: 0.7;
          }
        }
        
        @keyframes joyride-beacon-outer {
          0% {
            transform: scale(1);
          }
        
          45% {
            opacity: 0.7;
            transform: scale(0.75);
          }
        
          100% {
            opacity: 0.9;
            transform: scale(1);
          }
        }
      `)),t.appendChild(n)}componentDidMount(){let{shouldFocus:e}=this.props;_.domElement(this.beacon)||console.warn("beacon is not a valid DOM element"),setTimeout(()=>{_.domElement(this.beacon)&&e&&this.beacon.focus()},0)}componentWillUnmount(){let e=document.getElementById("joyride-beacon-animation");e?.parentNode&&e.parentNode.removeChild(e)}render(){let{beaconComponent:e,continuous:t,index:n,isLastStep:r,locale:o,onClickOrHover:i,size:a,step:s,styles:c}=this.props,l=Se(o.open),p={"aria-label":l,onClick:i,onMouseEnter:i,ref:this.setBeaconRef,title:l},u;return e?u=V(e,{continuous:t,index:n,isLastStep:r,size:a,step:s,...p}):u=V("button",{key:"JoyrideBeacon",className:"react-joyride__beacon","data-test-id":"button-beacon",style:c.beacon,type:"button",...p},V("span",{style:c.beaconInner}),V("span",{style:c.beaconOuter})),u}};ss=as;cs=ls,us=class extends De{constructor(){super(...arguments),A(this,"handleClickBack",e=>{e.preventDefault();let{helpers:t}=this.props;t.prev()}),A(this,"handleClickClose",e=>{e.preventDefault();let{helpers:t}=this.props;t.close("button_close")}),A(this,"handleClickPrimary",e=>{e.preventDefault();let{continuous:t,helpers:n}=this.props;if(!t){n.close("button_primary");return}n.next()}),A(this,"handleClickSkip",e=>{e.preventDefault();let{helpers:t}=this.props;t.skip()}),A(this,"getElementsProps",()=>{let{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:o,step:i}=this.props,{back:a,close:s,last:c,next:l,nextLabelWithProgress:p,skip:u}=i.locale,f=Se(a),h=Se(s),d=Se(c),g=Se(l),S=Se(u),b=s,R=h;if(e){if(b=l,R=g,i.showProgress&&!n){let m=Se(p,{step:t+1,steps:o});b=rn(p,t+1,o),R=m}n&&(b=c,R=d)}return{backProps:{"aria-label":f,children:a,"data-action":"back",onClick:this.handleClickBack,role:"button",title:f},closeProps:{"aria-label":h,children:s,"data-action":"close",onClick:this.handleClickClose,role:"button",title:h},primaryProps:{"aria-label":R,children:b,"data-action":"primary",onClick:this.handleClickPrimary,role:"button",title:R},skipProps:{"aria-label":S,children:u,"data-action":"skip",onClick:this.handleClickSkip,role:"button",title:S},tooltipProps:{"aria-modal":!0,ref:r,role:"alertdialog"}}})}render(){let{continuous:e,index:t,isLastStep:n,setTooltipRef:r,size:o,step:i}=this.props,{beaconComponent:a,tooltipComponent:s,...c}=i,l;if(s){let p={...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:o,step:c,setTooltipRef:r};l=V(s,{...p})}else l=V(cs,{...this.getElementsProps(),continuous:e,index:t,isLastStep:n,size:o,step:i});return l}},ps=class extends De{constructor(){super(...arguments),A(this,"scope",null),A(this,"tooltip",null),A(this,"handleClickHoverBeacon",e=>{let{step:t,store:n}=this.props;e.type==="mouseenter"&&t.event!=="hover"||n.update({lifecycle:L.TOOLTIP})}),A(this,"setTooltipRef",e=>{this.tooltip=e}),A(this,"setPopper",(e,t)=>{var n;let{action:r,lifecycle:o,step:i,store:a}=this.props;t==="wrapper"?a.setPopper("beacon",e):a.setPopper("tooltip",e),a.getPopper("beacon")&&(a.getPopper("tooltip")||i.placement==="center")&&o===L.INIT&&a.update({action:r,lifecycle:L.READY}),(n=i.floaterProps)!=null&&n.getPopper&&i.floaterProps.getPopper(e,t)}),A(this,"renderTooltip",e=>{let{continuous:t,helpers:n,index:r,size:o,step:i}=this.props;return V(us,{continuous:t,helpers:n,index:r,isLastStep:r+1===o,setTooltipRef:this.setTooltipRef,size:o,step:i,...e})})}componentDidMount(){let{debug:e,index:t}=this.props;Le({title:`step:${t}`,data:[{key:"props",value:this.props}],debug:e})}componentDidUpdate(e){var t;let{action:n,callback:r,continuous:o,controlled:i,debug:a,helpers:s,index:c,lifecycle:l,shouldScroll:p,status:u,step:f,store:h}=this.props,{changed:d,changedFrom:g}=xt(e,this.props),S=s.info(),b=o&&n!==$.CLOSE&&(c>0||n===$.PREV),R=d("action")||d("index")||d("lifecycle")||d("status"),m=g("lifecycle",[L.TOOLTIP,L.INIT],L.INIT),v=d("action",[$.NEXT,$.PREV,$.SKIP,$.CLOSE]),w=i&&c===e.index;if(v&&(m||w)&&r({...S,index:e.index,lifecycle:L.COMPLETE,step:e.step,type:fe.STEP_AFTER}),f.placement==="center"&&u===B.RUNNING&&d("index")&&n!==$.START&&l===L.INIT&&h.update({lifecycle:L.READY}),R){let U=Re(f.target),x=!!U;x&&Da(U)?(g("status",B.READY,B.RUNNING)||g("lifecycle",L.INIT,L.READY))&&r({...S,step:f,type:fe.STEP_BEFORE}):(console.warn(x?"Target not visible":"Target not mounted",f),r({...S,type:fe.TARGET_NOT_FOUND,step:f}),i||h.update({index:c+(n===$.PREV?-1:1)}))}g("lifecycle",L.INIT,L.READY)&&h.update({lifecycle:Or(f)||b?L.TOOLTIP:L.BEACON}),d("index")&&Le({title:`step:${l}`,data:[{key:"props",value:this.props}],debug:a}),d("lifecycle",L.BEACON)&&r({...S,step:f,type:fe.BEACON}),d("lifecycle",L.TOOLTIP)&&(r({...S,step:f,type:fe.TOOLTIP}),p&&this.tooltip&&(this.scope=new os(this.tooltip,{selector:"[data-action=primary]"}),this.scope.setFocus())),g("lifecycle",[L.TOOLTIP,L.INIT],L.INIT)&&((t=this.scope)==null||t.removeScope(),h.cleanupPoppers())}componentWillUnmount(){var e;(e=this.scope)==null||e.removeScope()}get open(){let{lifecycle:e,step:t}=this.props;return Or(t)||e===L.TOOLTIP}render(){let{continuous:e,debug:t,index:n,nonce:r,shouldScroll:o,size:i,step:a}=this.props,s=Re(a.target);return!po(a)||!_.domElement(s)?null:V("div",{key:`JoyrideStep-${n}`,className:"react-joyride__step"},V(dn,{...a.floaterProps,component:this.renderTooltip,debug:t,getPopper:this.setPopper,id:`react-joyride-step-${n}`,open:this.open,placement:a.placement,target:a.target},V(is,{beaconComponent:a.beaconComponent,continuous:e,index:n,isLastStep:n+1===i,locale:a.locale,nonce:r,onClickOrHover:this.handleClickHoverBeacon,shouldFocus:o,size:i,step:a,styles:a.styles})))}},ho=class extends De{constructor(e){super(e),A(this,"helpers"),A(this,"store"),A(this,"callback",a=>{let{callback:s}=this.props;_.function(s)&&s(a)}),A(this,"handleKeyboard",a=>{let{index:s,lifecycle:c}=this.state,{steps:l}=this.props,p=l[s];c===L.TOOLTIP&&a.code==="Escape"&&p&&!p.disableCloseOnEsc&&this.store.close("keyboard")}),A(this,"handleClickOverlay",()=>{let{index:a}=this.state,{steps:s}=this.props;Ge(this.props,s[a]).disableOverlayClose||this.helpers.close("overlay")}),A(this,"syncState",a=>{this.setState(a)});let{debug:t,getHelpers:n,run:r=!0,stepIndex:o}=e;this.store=Qa({...e,controlled:r&&_.number(o)}),this.helpers=this.store.getHelpers();let{addListener:i}=this.store;Le({title:"init",data:[{key:"props",value:this.props},{key:"state",value:this.state}],debug:t}),i(this.syncState),n&&n(this.helpers),this.state=this.store.getState()}componentDidMount(){if(!Ne())return;let{debug:e,disableCloseOnEsc:t,run:n,steps:r}=this.props,{start:o}=this.store;Tr(r,e)&&n&&o(),t||document.body.addEventListener("keydown",this.handleKeyboard,{passive:!0})}componentDidUpdate(e,t){if(!Ne())return;let{action:n,controlled:r,index:o,status:i}=this.state,{debug:a,run:s,stepIndex:c,steps:l}=this.props,{stepIndex:p,steps:u}=e,{reset:f,setSteps:h,start:d,stop:g,update:S}=this.store,{changed:b}=xt(e,this.props),{changed:R,changedFrom:m}=xt(t,this.state),v=Ge(this.props,l[o]),w=!oe(u,l),U=_.number(c)&&b("stepIndex"),x=Re(v.target);if(w&&(Tr(l,a)?h(l):console.warn("Steps are not valid",l)),b("run")&&(s?d(c):g()),U){let ee=_.number(p)&&p<c?$.NEXT:$.PREV;n===$.STOP&&(ee=$.START),[B.FINISHED,B.SKIPPED].includes(i)||S({action:n===$.CLOSE?$.CLOSE:ee,index:c,lifecycle:L.INIT})}!r&&i===B.RUNNING&&o===0&&!x&&(this.store.update({index:o+1}),this.callback({...this.state,type:fe.TARGET_NOT_FOUND,step:v}));let z={...this.state,index:o,step:v};if(R("action",[$.NEXT,$.PREV,$.SKIP,$.CLOSE])&&R("status",B.PAUSED)){let ee=Ge(this.props,l[t.index]);this.callback({...z,index:t.index,lifecycle:L.COMPLETE,step:ee,type:fe.STEP_AFTER})}if(R("status",[B.FINISHED,B.SKIPPED])){let ee=Ge(this.props,l[t.index]);r||this.callback({...z,index:t.index,lifecycle:L.COMPLETE,step:ee,type:fe.STEP_AFTER}),this.callback({...z,type:fe.TOUR_END,step:ee,index:t.index}),f()}else m("status",[B.IDLE,B.READY],B.RUNNING)?this.callback({...z,type:fe.TOUR_START}):(R("status")||R("action",$.RESET))&&this.callback({...z,type:fe.TOUR_STATUS});this.scrollToStep(t)}componentWillUnmount(){let{disableCloseOnEsc:e}=this.props;e||document.body.removeEventListener("keydown",this.handleKeyboard)}scrollToStep(e){let{index:t,lifecycle:n,status:r}=this.state,{debug:o,disableScrollParentFix:i=!1,scrollDuration:a,scrollOffset:s=20,scrollToFirstStep:c=!1,steps:l}=this.props,p=Ge(this.props,l[t]),u=Re(p.target),f=Ya({isFirstStep:t===0,lifecycle:n,previousLifecycle:e.lifecycle,scrollToFirstStep:c,step:p,target:u});if(r===B.RUNNING&&f){let h=gt(u,i),d=ct(u,i),g=Math.floor(Ba(u,s,i))||0;Le({title:"scrollToStep",data:[{key:"index",value:t},{key:"lifecycle",value:n},{key:"status",value:r}],debug:o});let S=this.store.getPopper("beacon"),b=this.store.getPopper("tooltip");if(n===L.BEACON&&S){let{offsets:R,placement:m}=S;!["bottom"].includes(m)&&!h&&(g=Math.floor(R.popper.top-s))}else if(n===L.TOOLTIP&&b){let{flipped:R,offsets:m,placement:v}=b;["top","right","left"].includes(v)&&!R&&!h?g=Math.floor(m.popper.top-s):g-=p.spotlightPadding}g=g>=0?g:0,r===B.RUNNING&&Wa(g,{element:d,duration:a}).then(()=>{setTimeout(()=>{var R;(R=this.store.getPopper("tooltip"))==null||R.instance.update()},10)})}}render(){if(!Ne())return null;let{index:e,lifecycle:t,status:n}=this.state,{continuous:r=!1,debug:o=!1,nonce:i,scrollToFirstStep:a=!1,steps:s}=this.props,c=n===B.RUNNING,l={};if(c&&s[e]){let p=Ge(this.props,s[e]);l.step=V(ps,{...this.state,callback:this.callback,continuous:r,debug:o,helpers:this.helpers,nonce:i,shouldScroll:!p.disableScrolling&&(e!==0||a),step:p,store:this.store}),l.overlay=V(rs,{id:"react-joyride-portal"},V(ns,{...p,continuous:r,debug:o,lifecycle:t,onClickOverlay:this.handleClickOverlay}))}return V("div",{className:"react-joyride"},l.step,l.overlay)}};A(ho,"defaultProps",Va);fs=ho,ds=te.button`
  all: unset;
  box-sizing: border-box;
  border: 0;
  border-radius: 0.25rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  background: ${({theme:e,variant:t})=>t==="primary"?e.color.secondary:t==="secondary"?e.color.lighter:t==="outline"?"transparent":t==="white"?e.color.lightest:e.color.secondary};
  color: ${({theme:e,variant:t})=>t==="primary"?e.color.lightest:t==="secondary"||t==="outline"?e.darkest:t==="white"?e.color.secondary:e.color.lightest};
  box-shadow: ${({variant:e})=>e==="secondary"||e==="outline"?"#D9E8F2 0 0 0 1px inset":"none"};
  height: 32px;
  font-size: 0.8125rem;
  font-weight: 700;
  font-family: ${({theme:e})=>e.typography.fonts.base};
  transition: background-color, box-shadow, color, opacity;
  transition-duration: 0.16s;
  transition-timing-function: ease-in-out;
  text-decoration: none;

  &:hover {
    background-color: ${({theme:e,variant:t})=>t==="primary"?"#0b94eb":t==="secondary"?"#eef4f9":t==="outline"?"transparent":t==="white"?e.color.lightest:"#0b94eb"};
    color: ${({theme:e,variant:t})=>t==="primary"?e.color.lightest:t==="secondary"||t==="outline"?e.darkest:t==="white"?e.color.darkest:e.color.lightest};
  }

  &:focus {
    box-shadow: ${({variant:e})=>e==="primary"?"inset 0 0 0 1px rgba(0, 0, 0, 0.2)":e==="secondary"||e==="outline"?"inset 0 0 0 1px #0b94eb":e==="white"?"none":"inset 0 0 0 2px rgba(0, 0, 0, 0.1)"};
  }
`,hs=yn(function({children:e,onClick:t,variant:n="primary",...r},o){return y.createElement(ds,{ref:o,onClick:t,variant:n,...r},e)}),ms=te.div`
  padding: 15px;
  border-radius: 5px;
`,ys=te.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`,gs=te.div`
  display: flex;
  align-items: center;
  align-self: stretch;
  justify-content: space-between;
  margin: -5px -5px 5px 0;
`,bs=te.div`
  line-height: 18px;
  font-weight: 700;
  font-size: 14px;
  margin: 5px 5px 5px 0;
`,vs=te.p`
  font-size: 14px;
  line-height: 18px;
  text-align: start;
  text-wrap: balance;
  margin: 0;
  margin-top: 5px;
`,Es=te.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
`,Os=te.span`
  font-size: 13px;
`,Ss=({index:e,size:t,step:n,closeProps:r,primaryProps:o,tooltipProps:i})=>(ae(()=>{let a=document.createElement("style");return a.id="#sb-onboarding-arrow-style",a.innerHTML=`
      .__floater__arrow { container-type: size; }
      .__floater__arrow span { background: ${wt.secondary}; }
      .__floater__arrow span::before, .__floater__arrow span::after {
        content: '';
        display: block;
        width: 2px;
        height: 2px;
        background: ${wt.secondary};
        box-shadow: 0 0 0 2px ${wt.secondary};
        border-radius: 3px;
        flex: 0 0 2px;
      }
      @container (min-height: 1px) {
        .__floater__arrow span { flex-direction: column; }
      }
    `,document.head.appendChild(a),()=>{let s=document.querySelector("#sb-onboarding-arrow-style");s&&s.remove()}},[]),y.createElement(ms,{...i,style:n.styles?.tooltip},y.createElement(ys,null,y.createElement(gs,null,n.title&&y.createElement(bs,null,n.title),y.createElement(wn,{...r,onClick:r.onClick,variant:"solid"},y.createElement(_n,null))),y.createElement(vs,null,n.content)),y.createElement(Es,{id:"buttonNext"},y.createElement(Os,null,e+1," of ",t),!n.hideNextButton&&y.createElement(hs,{...o,onClick:n.onNextButtonClick||o.onClick,variant:"white"},e+1===t?"Done":"Next"))));Ts=nt({from:{opacity:0},to:{opacity:1}}),mo=nt({from:{transform:"translate(0, 20px)",opacity:0},to:{transform:"translate(0, 0)",opacity:1}}),Is=nt({from:{opacity:0,transform:"scale(0.8)"},to:{opacity:1,transform:"scale(1)"}}),Cs=nt({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}),Ps=te.div(({visible:e})=>({position:"fixed",top:0,left:0,right:0,bottom:0,display:"flex",opacity:e?1:0,alignItems:"center",justifyContent:"center",zIndex:1e3,transition:"opacity 1s 0.5s"})),Rs=te.div({position:"absolute",top:0,left:0,right:0,bottom:0,animation:`${Ts} 2s`,background:`
    radial-gradient(90% 90%, #ff4785 0%, #db5698 30%, #1ea7fdcc 100%),
    radial-gradient(circle, #ff4785 0%, transparent 80%),
    radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 20%, transparent 40%),
    radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 18%, transparent 30%)`,"&::before":{opacity:.5,background:`
      radial-gradient(circle at 30% 40%, #fc521f99 0%, #fc521f66 10%, transparent 20%),
      radial-gradient(circle at 75% 75%, #fc521f99 0%, #fc521f77 8%, transparent 20%)`,content:'""',position:"absolute",top:"-50vw",left:"-50vh",transform:"translate(-50%, -50%)",width:"calc(100vw + 100vh)",height:"calc(100vw + 100vh)",animation:`${Cs} 12s linear infinite`}}),xs=te.div(({visible:e})=>({position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:"white",textAlign:"center",width:"90vw",minWidth:290,maxWidth:410,opacity:e?1:0,transition:"opacity 0.5s",h1:{fontSize:45,fontWeight:"bold",animation:`${mo} 1.5s 1s backwards`}})),_s=te.div({display:"flex",marginTop:40,div:{display:"flex",flexBasis:"33.33%",flexDirection:"column",alignItems:"center",animation:`${mo} 1s backwards`,"&:nth-child(1)":{animationDelay:"2s"},"&:nth-child(2)":{animationDelay:"2.5s"},"&:nth-child(3)":{animationDelay:"3s"}},svg:{marginBottom:10}}),As=te.button({display:"inline-flex",position:"relative",alignItems:"center",justifyContent:"center",marginTop:40,width:48,height:48,padding:0,borderRadius:"50%",border:0,outline:"none",background:"rgba(255, 255, 255, 0.3)",cursor:"pointer",transition:"background 0.2s",animation:`${Is} 1.5s 4s backwards`,"&:hover, &:focus":{background:"rgba(255, 255, 255, 0.4)"}}),Ns=te(xn)({width:30,color:"white"}),Cr=te.svg(({progress:e})=>({position:"absolute",top:-1,left:-1,width:"50px!important",height:"50px!important",transform:"rotate(-90deg)",color:"white",circle:{r:"24",cx:"25",cy:"25",fill:"transparent",stroke:e?"currentColor":"transparent",strokeWidth:"1",strokeLinecap:"round",strokeDasharray:Math.PI*48}})),ks=({onDismiss:e,duration:t=6e3})=>{let[n,r]=pe(-4e5/t),[o,i]=pe(!0),a=n>=100,s=tt(()=>{i(!1);let c=setTimeout(e,1500);return()=>clearTimeout(c)},[e]);return ae(()=>{if(!t)return;let c=1e3/50,l=100/(t/c),p=setInterval(()=>r(u=>u+l),c);return()=>clearInterval(p)},[t]),ae(()=>{a&&s()},[a,s]),y.createElement(Ps,{visible:o},y.createElement(Rs,null),y.createElement(xs,{visible:o},y.createElement("h1",null,"Meet your new frontend workshop"),y.createElement(_s,null,y.createElement("div",null,y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"33",height:"32"},y.createElement("path",{d:"M4.06 0H32.5v28.44h-3.56V32H.5V3.56h3.56V0Zm21.33 7.11H4.06v21.33h21.33V7.11Z",fill:"currentColor"})),"Development"),y.createElement("div",null,y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32"},y.createElement("path",{d:"M15.95 32c-1.85 0-3.1-1.55-3.1-3.54 0-1.1.45-2.78 1.35-5.03.9-2.3 1.35-4.51 1.35-6.81a22.21 22.21 0 0 0-5.1 3.67c-2.5 2.47-4.95 4.9-7.55 4.9-1.6 0-2.9-1.1-2.9-2.43 0-1.46 1.35-2.91 4.3-3.62 1.45-.36 3.1-.75 4.95-1.06 1.8-.31 3.8-1.02 5.9-2.08a23.77 23.77 0 0 0-6.1-2.12C5.3 13.18 2.3 12.6 1 11.28.35 10.6 0 9.9 0 9.14 0 7.82 1.2 6.8 2.95 6.8c2.65 0 5.75 3.1 7.95 5.3 1.1 1.1 2.65 2.21 4.65 3.27v-.57c0-1.77-.15-3.23-.55-4.3-.8-2.11-2.05-5.43-2.05-6.97 0-2.04 1.3-3.54 3.1-3.54 1.75 0 3.1 1.41 3.1 3.54 0 1.06-.45 2.78-1.35 5.12-.9 2.35-1.35 4.6-1.35 6.72 2.85-1.59 2.5-1.41 4.95-3.5 2.35-2.29 4-3.7 4.9-4.23.95-.58 1.9-.84 2.9-.84 1.6 0 2.8.97 2.8 2.34 0 1.5-1.25 2.78-4.15 3.62-1.4.4-3.05.75-4.9 1.1-1.9.36-3.9 1.07-6.1 2.13a23.3 23.3 0 0 0 5.95 2.08c3.65.7 6.75 1.32 8.15 2.6.7.67 1.05 1.33 1.05 2.08 0 1.33-1.2 2.43-2.95 2.43-2.95 0-6.75-4.15-8.2-5.61-.7-.7-2.2-1.72-4.4-2.96v.57c0 1.9.45 4.03 1.3 6.32.85 2.3 1.3 3.94 1.3 4.95 0 2.08-1.35 3.54-3.1 3.54Z",fill:"currentColor"})),"Testing"),y.createElement("div",null,y.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"33",height:"32"},y.createElement("path",{d:"M.5 16a16 16 0 1 1 32 0 16 16 0 0 1-32 0Zm16 12.44A12.44 12.44 0 0 1 4.3 13.53a8 8 0 1 0 9.73-9.73 12.44 12.44 0 1 1 2.47 24.64ZM12.06 16a4.44 4.44 0 1 1 0-8.89 4.44 4.44 0 0 1 0 8.89Z",fill:"currentColor",fillRule:"evenodd"})),"Documentation")),y.createElement(As,{onClick:s},y.createElement(Ns,null),y.createElement(Cr,{xmlns:"http://www.w3.org/2000/svg"},y.createElement("circle",null)),y.createElement(Cr,{xmlns:"http://www.w3.org/2000/svg",progress:!0},y.createElement("circle",{strokeDashoffset:Math.PI*48*(1-Math.max(0,Math.min(n,100))/100)})))))},Ls=te.span(({theme:e})=>({display:"inline-flex",borderRadius:3,padding:"0 5px",marginBottom:-2,opacity:.8,fontFamily:e.typography.fonts.mono,fontSize:11,border:e.base==="dark"?e.color.darkest:e.color.lightest,color:e.base==="dark"?e.color.lightest:e.color.darkest,backgroundColor:e.base==="dark"?"black":e.color.light,boxSizing:"border-box",lineHeight:"17px"})),js=te.div(({theme:e})=>({background:e.background.content,borderRadius:3,marginTop:15,padding:10,fontSize:e.typography.size.s1,".linenumber":{opacity:.5}})),Ms=Gt()});X();Z();Q();X();Z();Q();vt();Ot();Ht();Ut();X();Z();Q();var Uc=__STORYBOOK_API__,{ActiveTabs:zc,Consumer:Gc,ManagerContext:Yc,Provider:qc,RequestResponseError:$c,addons:Sn,combineParameters:Vc,controlOrMetaKey:Kc,controlOrMetaSymbol:Jc,eventMatchesShortcut:Xc,eventToShortcut:Zc,experimental_MockUniversalStore:Qc,experimental_UniversalStore:eu,experimental_getStatusStore:tu,experimental_getTestProviderStore:nu,experimental_requestResponse:ru,experimental_useStatusStore:ou,experimental_useTestProviderStore:iu,experimental_useUniversalStore:au,internal_fullStatusStore:su,internal_fullTestProviderStore:lu,internal_universalStatusStore:cu,internal_universalTestProviderStore:uu,isMacLike:pu,isShortcutTaken:fu,keyToSymbol:du,merge:hu,mockChannel:mu,optionOrAltSymbol:yu,shortcutMatchesShortcut:gu,shortcutToHumanString:bu,types:vu,useAddonState:Eu,useArgTypes:Ou,useArgs:Su,useChannel:wu,useGlobalTypes:Tu,useGlobals:Iu,useParameter:Cu,useSharedState:Pu,useStoryPrepared:Ru,useStorybookApi:xu,useStorybookState:_u}=__STORYBOOK_API__;var Fs=gn(()=>Promise.resolve().then(()=>(go(),yo)));Sn.register("@storybook/addon-onboarding",async e=>{let t=e.getUrlState(),n=t.path==="/onboarding"||t.queryParams.onboarding==="true";e.once(On,()=>{if(!(e.getData("example-button--primary")||document.getElementById("example-button--primary"))){console.warn("[@storybook/addon-onboarding] It seems like you have finished the onboarding experience in Storybook! Therefore this addon is not necessary anymore and will not be loaded. You are free to remove it from your project. More info: https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding#uninstalling");return}if(!n||window.innerWidth<730)return;e.togglePanel(!0),e.togglePanelPosition("bottom"),e.setSelectedPanel(St);let r=document.createElement("div");r.id="storybook-addon-onboarding",document.body.appendChild(r),Fe.render(y.createElement(mn,{fallback:y.createElement("div",null)},y.createElement(Fs,{api:e})),r)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
