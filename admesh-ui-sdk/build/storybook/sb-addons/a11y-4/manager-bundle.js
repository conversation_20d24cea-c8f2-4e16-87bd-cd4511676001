try{
(()=>{var kn=Object.defineProperty;var Se=(e,t)=>()=>(e&&(t=e(e=0)),t);var Nn=(e,t)=>{for(var a in t)kn(e,a,{get:t[a],enumerable:!0})};var O=Se(()=>{});var w=Se(()=>{});var k=Se(()=>{});var mt={};Nn(mt,{A:()=>Mn,ActionBar:()=>Ln,AddonPanel:()=>Fn,Badge:()=>le,Bar:()=>Hn,Blockquote:()=>Un,Button:()=>re,Checkbox:()=>Gn,ClipboardCode:()=>Bn,Code:()=>zn,DL:()=>Wn,Div:()=>jn,DocumentWrapper:()=>Yn,EmptyTabContent:()=>Ce,ErrorFormatter:()=>Vn,FlexBar:()=>qn,Form:()=>Kn,H1:()=>Qn,H2:()=>Xn,H3:()=>Zn,H4:()=>Jn,H5:()=>ea,H6:()=>ta,HR:()=>na,IconButton:()=>ie,Img:()=>aa,LI:()=>ra,Link:()=>Ie,ListItem:()=>oa,Loader:()=>la,Modal:()=>ia,OL:()=>ca,P:()=>sa,Placeholder:()=>ua,Pre:()=>da,ProgressSpinner:()=>ma,ResetWrapper:()=>fa,ScrollArea:()=>Oe,Separator:()=>pa,Spaced:()=>ha,Span:()=>ya,StorybookIcon:()=>ba,StorybookLogo:()=>ga,SyntaxHighlighter:()=>we,TT:()=>Sa,TabBar:()=>va,TabButton:()=>Ea,TabWrapper:()=>Ia,Table:()=>xa,Tabs:()=>Ra,TabsState:()=>Aa,TooltipLinkList:()=>ke,TooltipMessage:()=>$a,TooltipNote:()=>pe,UL:()=>Ta,WithTooltip:()=>de,WithTooltipPure:()=>_a,Zoom:()=>Ca,codeCommon:()=>Oa,components:()=>wa,createCopyToClipboardFunction:()=>ka,default:()=>Dn,getStoryHref:()=>Na,interleaveSeparators:()=>Pa,nameSpaceClassNames:()=>Da,resetComponents:()=>Ma,withReset:()=>La});var Dn,Mn,Ln,Fn,le,Hn,Un,re,Gn,Bn,zn,Wn,jn,Yn,Ce,Vn,qn,Kn,Qn,Xn,Zn,Jn,ea,ta,na,ie,aa,ra,Ie,oa,la,ia,ca,sa,ua,da,ma,fa,Oe,pa,ha,ya,ba,ga,we,Sa,va,Ea,Ia,xa,Ra,Aa,ke,$a,pe,Ta,de,_a,Ca,Oa,wa,ka,Na,Pa,Da,Ma,La,Ne=Se(()=>{O();w();k();Dn=__STORYBOOK_COMPONENTS__,{A:Mn,ActionBar:Ln,AddonPanel:Fn,Badge:le,Bar:Hn,Blockquote:Un,Button:re,Checkbox:Gn,ClipboardCode:Bn,Code:zn,DL:Wn,Div:jn,DocumentWrapper:Yn,EmptyTabContent:Ce,ErrorFormatter:Vn,FlexBar:qn,Form:Kn,H1:Qn,H2:Xn,H3:Zn,H4:Jn,H5:ea,H6:ta,HR:na,IconButton:ie,Img:aa,LI:ra,Link:Ie,ListItem:oa,Loader:la,Modal:ia,OL:ca,P:sa,Placeholder:ua,Pre:da,ProgressSpinner:ma,ResetWrapper:fa,ScrollArea:Oe,Separator:pa,Spaced:ha,Span:ya,StorybookIcon:ba,StorybookLogo:ga,SyntaxHighlighter:we,TT:Sa,TabBar:va,TabButton:Ea,TabWrapper:Ia,Table:xa,Tabs:Ra,TabsState:Aa,TooltipLinkList:ke,TooltipMessage:$a,TooltipNote:pe,UL:Ta,WithTooltip:de,WithTooltipPure:_a,Zoom:Ca,codeCommon:Oa,components:wa,createCopyToClipboardFunction:ka,getStoryHref:Na,interleaveSeparators:Pa,nameSpaceClassNames:Da,resetComponents:Ma,withReset:La}=__STORYBOOK_COMPONENTS__});O();w();k();O();w();k();O();w();k();var o=__REACT__,{Children:P,Component:oi,Fragment:me,Profiler:li,PureComponent:ct,StrictMode:ii,Suspense:ci,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:si,act:ui,cloneElement:V,createContext:fe,createElement:m,createFactory:di,createRef:st,forwardRef:L,isValidElement:U,lazy:mi,memo:fi,startTransition:pi,unstable_act:hi,useCallback:R,useContext:ve,useDebugValue:yi,useDeferredValue:bi,useEffect:G,useId:ut,useImperativeHandle:gi,useInsertionEffect:Si,useLayoutEffect:Ee,useMemo:j,useReducer:dt,useRef:z,useState:H,useSyncExternalStore:vi,useTransition:Ei,version:Ii}=__REACT__;Ne();O();w();k();var Ci=__STORYBOOK_API__,{ActiveTabs:Oi,Consumer:wi,ManagerContext:ki,Provider:Ni,RequestResponseError:Pi,addons:xe,combineParameters:Di,controlOrMetaKey:Mi,controlOrMetaSymbol:Li,eventMatchesShortcut:Fi,eventToShortcut:Hi,experimental_MockUniversalStore:Ui,experimental_UniversalStore:Gi,experimental_getStatusStore:ft,experimental_getTestProviderStore:Bi,experimental_requestResponse:zi,experimental_useStatusStore:pt,experimental_useTestProviderStore:Wi,experimental_useUniversalStore:ji,internal_fullStatusStore:Yi,internal_fullTestProviderStore:Vi,internal_universalStatusStore:qi,internal_universalTestProviderStore:Ki,isMacLike:Qi,isShortcutTaken:Xi,keyToSymbol:Zi,merge:Ji,mockChannel:ec,optionOrAltSymbol:tc,shortcutMatchesShortcut:nc,shortcutToHumanString:ac,types:Pe,useAddonState:De,useArgTypes:rc,useArgs:oc,useChannel:ht,useGlobalTypes:lc,useGlobals:yt,useParameter:bt,useSharedState:ic,useStoryPrepared:cc,useStorybookApi:Re,useStorybookState:gt}=__STORYBOOK_API__;O();w();k();var fc=__STORYBOOK_ICONS__,{AccessibilityAltIcon:pc,AccessibilityIcon:St,AccessibilityIgnoredIcon:hc,AddIcon:yc,AdminIcon:bc,AlertAltIcon:gc,AlertIcon:Sc,AlignLeftIcon:vc,AlignRightIcon:Ec,AppleIcon:Ic,ArrowBottomLeftIcon:xc,ArrowBottomRightIcon:Rc,ArrowDownIcon:Ac,ArrowLeftIcon:$c,ArrowRightIcon:Tc,ArrowSolidDownIcon:_c,ArrowSolidLeftIcon:Cc,ArrowSolidRightIcon:Oc,ArrowSolidUpIcon:wc,ArrowTopLeftIcon:kc,ArrowTopRightIcon:Nc,ArrowUpIcon:Pc,AzureDevOpsIcon:Dc,BackIcon:Mc,BasketIcon:Lc,BatchAcceptIcon:Fc,BatchDenyIcon:Hc,BeakerIcon:Uc,BellIcon:Gc,BitbucketIcon:Bc,BoldIcon:zc,BookIcon:Wc,BookmarkHollowIcon:jc,BookmarkIcon:Yc,BottomBarIcon:Vc,BottomBarToggleIcon:qc,BoxIcon:Kc,BranchIcon:Qc,BrowserIcon:Xc,ButtonIcon:Zc,CPUIcon:Jc,CalendarIcon:es,CameraIcon:ts,CameraStabilizeIcon:ns,CategoryIcon:as,CertificateIcon:rs,ChangedIcon:os,ChatIcon:ls,CheckIcon:vt,ChevronDownIcon:is,ChevronLeftIcon:cs,ChevronRightIcon:ss,ChevronSmallDownIcon:Et,ChevronSmallLeftIcon:us,ChevronSmallRightIcon:ds,ChevronSmallUpIcon:ms,ChevronUpIcon:fs,ChromaticIcon:ps,ChromeIcon:hs,CircleHollowIcon:ys,CircleIcon:bs,ClearIcon:gs,CloseAltIcon:Ss,CloseIcon:vs,CloudHollowIcon:Es,CloudIcon:Is,CogIcon:xs,CollapseIcon:It,CommandIcon:Rs,CommentAddIcon:As,CommentIcon:$s,CommentsIcon:Ts,CommitIcon:_s,CompassIcon:Cs,ComponentDrivenIcon:Os,ComponentIcon:ws,ContrastIcon:ks,ContrastIgnoredIcon:Ns,ControlsIcon:Ps,CopyIcon:xt,CreditIcon:Ds,CrossIcon:Ms,DashboardIcon:Ls,DatabaseIcon:Fs,DeleteIcon:Hs,DiamondIcon:Us,DirectionIcon:Gs,DiscordIcon:Bs,DocChartIcon:zs,DocListIcon:Ws,DocumentIcon:js,DownloadIcon:Ys,DragIcon:Vs,EditIcon:qs,EllipsisIcon:Ks,EmailIcon:Qs,ExpandAltIcon:Rt,ExpandIcon:Xs,EyeCloseIcon:At,EyeIcon:$t,FaceHappyIcon:Zs,FaceNeutralIcon:Js,FaceSadIcon:eu,FacebookIcon:tu,FailedIcon:nu,FastForwardIcon:au,FigmaIcon:ru,FilterIcon:ou,FlagIcon:lu,FolderIcon:iu,FormIcon:cu,GDriveIcon:su,GithubIcon:uu,GitlabIcon:du,GlobeIcon:mu,GoogleIcon:fu,GraphBarIcon:pu,GraphLineIcon:hu,GraphqlIcon:yu,GridAltIcon:bu,GridIcon:gu,GrowIcon:Su,HeartHollowIcon:vu,HeartIcon:Eu,HomeIcon:Iu,HourglassIcon:xu,InfoIcon:Ru,ItalicIcon:Au,JumpToIcon:$u,KeyIcon:Tu,LightningIcon:_u,LightningOffIcon:Cu,LinkBrokenIcon:Ou,LinkIcon:wu,LinkedinIcon:ku,LinuxIcon:Nu,ListOrderedIcon:Pu,ListUnorderedIcon:Du,LocationIcon:Tt,LockIcon:Mu,MarkdownIcon:Lu,MarkupIcon:Fu,MediumIcon:Hu,MemoryIcon:Uu,MenuIcon:Gu,MergeIcon:Bu,MirrorIcon:zu,MobileIcon:Wu,MoonIcon:ju,NutIcon:Yu,OutboxIcon:Vu,OutlineIcon:qu,PaintBrushIcon:Ku,PaperClipIcon:Qu,ParagraphIcon:Xu,PassedIcon:Zu,PhoneIcon:Ju,PhotoDragIcon:ed,PhotoIcon:td,PhotoStabilizeIcon:nd,PinAltIcon:ad,PinIcon:rd,PlayAllHollowIcon:od,PlayBackIcon:ld,PlayHollowIcon:id,PlayIcon:cd,PlayNextIcon:sd,PlusIcon:ud,PointerDefaultIcon:dd,PointerHandIcon:md,PowerIcon:fd,PrintIcon:pd,ProceedIcon:hd,ProfileIcon:yd,PullRequestIcon:bd,QuestionIcon:gd,RSSIcon:Sd,RedirectIcon:vd,ReduxIcon:Ed,RefreshIcon:Id,ReplyIcon:xd,RepoIcon:Rd,RequestChangeIcon:Ad,RewindIcon:$d,RulerIcon:Td,SaveIcon:_d,SearchIcon:Cd,ShareAltIcon:Od,ShareIcon:wd,ShieldIcon:kd,SideBySideIcon:Nd,SidebarAltIcon:Pd,SidebarAltToggleIcon:Dd,SidebarIcon:Md,SidebarToggleIcon:Ld,SpeakerIcon:Fd,StackedIcon:Hd,StarHollowIcon:Ud,StarIcon:Gd,StatusFailIcon:Bd,StatusIcon:zd,StatusPassIcon:Wd,StatusWarnIcon:jd,StickerIcon:Yd,StopAltHollowIcon:Vd,StopAltIcon:qd,StopIcon:Kd,StorybookIcon:Qd,StructureIcon:Xd,SubtractIcon:Zd,SunIcon:Jd,SupportIcon:em,SweepIcon:tm,SwitchAltIcon:nm,SyncIcon:Me,TabletIcon:am,ThumbsUpIcon:rm,TimeIcon:om,TimerIcon:lm,TransferIcon:im,TrashIcon:cm,TwitterIcon:sm,TypeIcon:um,UbuntuIcon:dm,UndoIcon:mm,UnfoldIcon:fm,UnlockIcon:pm,UnpinIcon:hm,UploadIcon:ym,UserAddIcon:bm,UserAltIcon:gm,UserIcon:Sm,UsersIcon:vm,VSCodeIcon:Em,VerifiedIcon:Im,VideoIcon:xm,WandIcon:Rm,WatchIcon:Am,WindowsIcon:$m,WrenchIcon:Tm,XIcon:_m,YoutubeIcon:Cm,ZoomIcon:Om,ZoomOutIcon:wm,ZoomResetIcon:km,iconList:Nm}=__STORYBOOK_ICONS__;O();w();k();var Fm=__STORYBOOK_THEMING__,{CacheProvider:Hm,ClassNames:Um,Global:_t,ThemeProvider:Gm,background:Bm,color:zm,convert:Ct,create:Wm,createCache:jm,createGlobal:Ym,createReset:Vm,css:qm,darken:Km,ensure:Qm,ignoreSsrWarning:Xm,isPropValid:Zm,jsx:Jm,keyframes:ef,lighten:tf,styled:E,themes:Ot,typography:nf,useTheme:af,withTheme:rf}=__STORYBOOK_THEMING__;O();w();k();var uf=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:df,ARGTYPES_INFO_RESPONSE:mf,CHANNEL_CREATED:ff,CHANNEL_WS_DISCONNECT:pf,CONFIG_ERROR:hf,CREATE_NEW_STORYFILE_REQUEST:yf,CREATE_NEW_STORYFILE_RESPONSE:bf,CURRENT_STORY_WAS_SET:gf,DOCS_PREPARED:Sf,DOCS_RENDERED:vf,FILE_COMPONENT_SEARCH_REQUEST:Ef,FILE_COMPONENT_SEARCH_RESPONSE:If,FORCE_REMOUNT:xf,FORCE_RE_RENDER:Rf,GLOBALS_UPDATED:Af,NAVIGATE_URL:$f,PLAY_FUNCTION_THREW_EXCEPTION:Tf,PRELOAD_ENTRIES:_f,PREVIEW_BUILDER_PROGRESS:Cf,PREVIEW_KEYDOWN:Of,REGISTER_SUBSCRIPTION:wf,REQUEST_WHATS_NEW_DATA:kf,RESET_STORY_ARGS:Nf,RESULT_WHATS_NEW_DATA:Pf,SAVE_STORY_REQUEST:Df,SAVE_STORY_RESPONSE:Mf,SELECT_STORY:Lf,SET_CONFIG:Ff,SET_CURRENT_STORY:Hf,SET_FILTER:Uf,SET_GLOBALS:Gf,SET_INDEX:Bf,SET_STORIES:zf,SET_WHATS_NEW_CACHE:Wf,SHARED_STATE_CHANGED:jf,SHARED_STATE_SET:Yf,STORIES_COLLAPSE_ALL:Vf,STORIES_EXPAND_ALL:qf,STORY_ARGS_UPDATED:Kf,STORY_CHANGED:wt,STORY_ERRORED:Qf,STORY_FINISHED:kt,STORY_HOT_UPDATED:Nt,STORY_INDEX_INVALIDATED:Xf,STORY_MISSING:Zf,STORY_PREPARED:Jf,STORY_RENDERED:ep,STORY_RENDER_PHASE_CHANGED:Pt,STORY_SPECIFIED:tp,STORY_THREW_EXCEPTION:np,STORY_UNCHANGED:ap,TELEMETRY_ERROR:rp,TOGGLE_WHATS_NEW_NOTIFICATIONS:op,UNHANDLED_ERRORS_WHILE_PLAYING:lp,UPDATE_GLOBALS:ip,UPDATE_QUERY_PARAMS:cp,UPDATE_STORY_ARGS:sp}=__STORYBOOK_CORE_EVENTS__;O();w();k();var Ae="storybook/highlight",Le=`${Ae}/add`,Fe=`${Ae}/remove`,pp=`${Ae}/reset`,Dt=`${Ae}/scroll-into-view`;O();w();k();var Sp=__REACT_DOM__,{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:vp,createPortal:Ep,createRoot:Ip,findDOMNode:Mt,flushSync:Lt,hydrate:xp,hydrateRoot:Rp,render:Ap,unmountComponentAtNode:$p,unstable_batchedUpdates:Tp,unstable_renderSubtreeIntoContainer:_p,version:Cp}=__REACT_DOM__;var W={VIOLATION:"violations",PASS:"passes",INCOMPLETION:"incomplete"},Fa={"area-alt":{title:"<area> alt text",axeSummary:"Ensure <area> elements of image maps have alternative text",friendlySummary:"Add alt text to all <area> elements of image maps."},"aria-allowed-attr":{title:"Supported ARIA attributes",axeSummary:"Ensure an element's role supports its ARIA attributes",friendlySummary:"Only use ARIA attributes that are permitted for the element's role."},"aria-braille-equivalent":{title:"Braille equivalent",axeSummary:"Ensure aria-braillelabel and aria-brailleroledescription have a non-braille equivalent",friendlySummary:"If you use braille ARIA labels, also provide a matching non-braille label."},"aria-command-name":{title:"ARIA command name",axeSummary:"Ensure every ARIA button, link and menuitem has an accessible name",friendlySummary:"Every ARIA button, link, or menuitem needs a label or accessible name."},"aria-conditional-attr":{title:"ARIA attribute valid for role",axeSummary:"Ensure ARIA attributes are used as described in the specification of the element's role",friendlySummary:"Follow the element role's specification when using ARIA attributes."},"aria-deprecated-role":{title:"Deprecated ARIA role",axeSummary:"Ensure elements do not use deprecated roles",friendlySummary:"Don't use deprecated ARIA roles on elements."},"aria-hidden-body":{title:"Hidden body",axeSummary:'Ensure aria-hidden="true" is not present on the document <body>',friendlySummary:'Never set aria-hidden="true" on the <body> element.'},"aria-hidden-focus":{title:"Hidden element focus",axeSummary:"Ensure aria-hidden elements are not focusable nor contain focusable elements",friendlySummary:"Elements marked hidden (aria-hidden) should not be focusable or contain focusable items."},"aria-input-field-name":{title:"ARIA input field name",axeSummary:"Ensure every ARIA input field has an accessible name",friendlySummary:"Give each ARIA text input or field a label or accessible name."},"aria-meter-name":{title:"ARIA meter name",axeSummary:"Ensure every ARIA meter node has an accessible name",friendlySummary:'Give each element with role="meter" a label or accessible name.'},"aria-progressbar-name":{title:"ARIA progressbar name",axeSummary:"Ensure every ARIA progressbar node has an accessible name",friendlySummary:'Give each element with role="progressbar" a label or accessible name.'},"aria-prohibited-attr":{title:"ARIA prohibited attributes",axeSummary:"Ensure ARIA attributes are not prohibited for an element's role",friendlySummary:"Don't use ARIA attributes that are forbidden for that element's role."},"aria-required-attr":{title:"ARIA required attributes",axeSummary:"Ensure elements with ARIA roles have all required ARIA attributes",friendlySummary:"Include all required ARIA attributes for elements with that ARIA role."},"aria-required-children":{title:"ARIA required children",axeSummary:"Ensure elements with an ARIA role that require child roles contain them",friendlySummary:"If an ARIA role requires specific child roles, include those child elements."},"aria-required-parent":{title:"ARIA required parent",axeSummary:"Ensure elements with an ARIA role that require parent roles are contained by them",friendlySummary:"Place elements with certain ARIA roles inside the required parent role element."},"aria-roles":{title:"ARIA role value",axeSummary:"Ensure all elements with a role attribute use a valid value",friendlySummary:"Use only valid values in the role attribute (no typos or invalid roles)."},"aria-toggle-field-name":{title:"ARIA toggle field name",axeSummary:"Ensure every ARIA toggle field has an accessible name",friendlySummary:"Every ARIA toggle field (elements with the checkbox, radio, or switch roles) needs an accessible name."},"aria-tooltip-name":{title:"ARIA tooltip name",axeSummary:"Ensure every ARIA tooltip node has an accessible name",friendlySummary:'Give each element with role="tooltip" a descriptive accessible name.'},"aria-valid-attr-value":{title:"ARIA attribute values valid",axeSummary:"Ensure all ARIA attributes have valid values",friendlySummary:"Use only valid values for ARIA attributes (no typos or invalid values)."},"aria-valid-attr":{title:"ARIA attribute valid",axeSummary:"Ensure attributes that begin with aria- are valid ARIA attributes",friendlySummary:"Use only valid aria-* attributes (make sure the attribute name is correct)."},blink:{title:"<blink> element",axeSummary:"Ensure <blink> elements are not used",friendlySummary:"Don't use the deprecated <blink> element."},"button-name":{title:"Button name",axeSummary:"Ensure buttons have discernible text",friendlySummary:"Every <button> needs a visible label or accessible name."},bypass:{title:"Navigation bypass",axeSummary:"Ensure each page has at least one mechanism to bypass navigation and jump to content",friendlySummary:'Provide a way to skip repetitive navigation (e.g. a "Skip to content" link).'},"color-contrast":{title:"Color contrast",axeSummary:"Ensure the contrast between foreground and background text meets WCAG 2 AA minimum thresholds",friendlySummary:"The color contrast between text and its background meets WCAG AA contrast ratio."},"definition-list":{title:"Definition list structure",axeSummary:"Ensure <dl> elements are structured correctly",friendlySummary:"Definition lists (<dl>) should directly contain <dt> and <dd> elements in order."},dlitem:{title:"Definition list items",axeSummary:"Ensure <dt> and <dd> elements are contained by a <dl>",friendlySummary:"Ensure <dt> and <dd> elements are contained by a <dl>"},"document-title":{title:"Document title",axeSummary:"Ensure each HTML document contains a non-empty <title> element",friendlySummary:"Include a non-empty <title> element for every page."},"duplicate-id-aria":{title:"Unique id",axeSummary:"Ensure every id attribute value used in ARIA and in labels is unique",friendlySummary:"Every id used for ARIA or form labels should be unique on the page."},"form-field-multiple-labels":{title:"Multiple form field labels",axeSummary:"Ensure a form field does not have multiple <label> elements",friendlySummary:"Don't give a single form field more than one <label>."},"frame-focusable-content":{title:"Focusable frames",axeSummary:'Ensure <frame> and <iframe> with focusable content do not have tabindex="-1"',friendlySummary:`Don't set tabindex="-1" on a <frame> or <iframe> that contains focusable elements.`},"frame-title-unique":{title:"Unique frame title",axeSummary:"Ensure <iframe> and <frame> elements contain a unique title attribute",friendlySummary:"Use a unique title attribute for each <frame> or <iframe> on the page."},"frame-title":{title:"Frame title",axeSummary:"Ensure <iframe> and <frame> elements have an accessible name",friendlySummary:"Every <frame> and <iframe> needs a title or accessible name."},"html-has-lang":{title:"<html> has lang",axeSummary:"Ensure every HTML document has a lang attribute",friendlySummary:"Add a lang attribute to the <html> element."},"html-lang-valid":{title:"<html> lang valid",axeSummary:"Ensure the <html lang> attribute has a valid value",friendlySummary:"Use a valid language code in the <html lang> attribute."},"html-xml-lang-mismatch":{title:"HTML and XML lang mismatch",axeSummary:"Ensure that HTML elements with both lang and xml:lang agree on the page's language",friendlySummary:"If using both lang and xml:lang on <html>, make sure they are the same language."},"image-alt":{title:"Image alt text",axeSummary:"Ensure <img> elements have alternative text or a role of none/presentation",friendlySummary:'Give every image alt text or mark it as decorative with alt="".'},"input-button-name":{title:"Input button name",axeSummary:"Ensure input buttons have discernible text",friendlySummary:'Give each <input type="button"> or similar a clear label (text or aria-label).'},"input-image-alt":{title:"Input image alt",axeSummary:'Ensure <input type="image"> elements have alternative text',friendlySummary:'<input type="image"> must have alt text describing its image.'},label:{title:"Form label",axeSummary:"Ensure every form element has a label",friendlySummary:"Every form field needs an associated label."},"link-in-text-block":{title:"Identifiable links",axeSummary:"Ensure links are distinguishable from surrounding text without relying on color",friendlySummary:"Make sure links are obviously identifiable without relying only on color."},"link-name":{title:"Link name",axeSummary:"Ensure links have discernible text",friendlySummary:"Give each link meaningful text or an aria-label so its purpose is clear."},list:{title:"List structure",axeSummary:"Ensure that lists are structured correctly",friendlySummary:"Use proper list structure. Only use <li> inside <ul> or <ol>."},listitem:{title:"List item",axeSummary:"Ensure <li> elements are used semantically",friendlySummary:"Only use <li> tags inside <ul> or <ol> lists."},marquee:{title:"<marquee> element",axeSummary:"Ensure <marquee> elements are not used",friendlySummary:"Don't use the deprecated <marquee> element."},"meta-refresh":{title:"<meta> refresh",axeSummary:'Ensure <meta http-equiv="refresh"> is not used for delayed refresh',friendlySummary:'Avoid auto-refreshing or redirecting pages using <meta http-equiv="refresh">.'},"meta-viewport":{title:"<meta> viewport scaling",axeSummary:'Ensure <meta name="viewport"> does not disable text scaling and zooming',friendlySummary:`Don't disable user zooming in <meta name="viewport"> to allow scaling.`},"nested-interactive":{title:"Nested interactive controls",axeSummary:"Ensure interactive controls are not nested (nesting causes screen reader/focus issues)",friendlySummary:"Do not nest interactive elements; it can confuse screen readers and keyboard focus."},"no-autoplay-audio":{title:"Autoplaying video",axeSummary:"Ensure <video> or <audio> do not autoplay audio > 3 seconds without a control to stop/mute",friendlySummary:"Don't autoplay audio for more than 3 seconds without providing a way to stop or mute it."},"object-alt":{title:"<object> alt text",axeSummary:"Ensure <object> elements have alternative text",friendlySummary:"Provide alternative text or content for <object> elements."},"role-img-alt":{title:'role="img" alt text',axeSummary:'Ensure elements with role="img" have alternative text',friendlySummary:'Any element with role="img" needs alt text.'},"scrollable-region-focusable":{title:"Scrollable element focusable",axeSummary:"Ensure elements with scrollable content are keyboard-accessible",friendlySummary:"If an area can scroll, ensure it can be focused and scrolled via keyboard."},"select-name":{title:"<select> name",axeSummary:"Ensure <select> elements have an accessible name",friendlySummary:"Give each <select> field a label or other accessible name."},"server-side-image-map":{title:"Server-side image map",axeSummary:"Ensure that server-side image maps are not used",friendlySummary:"Don't use server-side image maps."},"svg-img-alt":{title:"SVG image alt text",axeSummary:"Ensure <svg> images/graphics have accessible text",friendlySummary:'SVG images with role="img" or similar need a text description.'},"td-headers-attr":{title:"Table headers attribute",axeSummary:"Ensure each cell in a table using headers only refers to <th> in that table",friendlySummary:"In tables using the headers attribute, only reference other cells in the same table."},"th-has-data-cells":{title:"<th> has data cell",axeSummary:"Ensure <th> (or header role) elements have data cells they describe",friendlySummary:"Every table header (<th> or header role) should correspond to at least one data cell."},"valid-lang":{title:"Valid lang",axeSummary:"Ensure lang attributes have valid values",friendlySummary:"Use valid language codes in all lang attributes."},"video-caption":{title:"<video> captions",axeSummary:"Ensure <video> elements have captions",friendlySummary:"Provide captions for all <video> content."}},Ha={"autocomplete-valid":{title:"autocomplete attribute valid",axeSummary:"Ensure the autocomplete attribute is correct and suitable for the form field",friendlySummary:"Use valid autocomplete values that match the form field's purpose."},"avoid-inline-spacing":{title:"Forced inline spacing",axeSummary:"Ensure that text spacing set via inline styles can be adjusted with custom CSS",friendlySummary:"Don't lock in text spacing with forced (!important) inline styles\u2014allow user CSS to adjust text spacing."}},Ua={"target-size":{title:"Touch target size",axeSummary:"Ensure touch targets have sufficient size and space",friendlySummary:"Make sure interactive elements are big enough and not too close together for touch."}},Ga={accesskeys:{title:"Unique accesskey",axeSummary:"Ensure every accesskey attribute value is unique",friendlySummary:"Use unique values for all accesskey attributes."},"aria-allowed-role":{title:"Appropriate role value",axeSummary:"Ensure the role attribute has an appropriate value for the element",friendlySummary:"ARIA roles should have a valid value for the element."},"aria-dialog-name":{title:"ARIA dialog name",axeSummary:"Ensure every ARIA dialog and alertdialog has an accessible name",friendlySummary:"Give each ARIA dialog or alertdialog a title or accessible name."},"aria-text":{title:'ARIA role="text"',axeSummary:'Ensure role="text" is used on elements with no focusable descendants',friendlySummary:`Only use role="text" on elements that don't contain focusable elements.`},"aria-treeitem-name":{title:"ARIA treeitem name",axeSummary:"Ensure every ARIA treeitem node has an accessible name",friendlySummary:"Give each ARIA treeitem a label or accessible name."},"empty-heading":{title:"Empty heading",axeSummary:"Ensure headings have discernible text",friendlySummary:"Don't leave heading elements empty or hide them."},"empty-table-header":{title:"Empty table header",axeSummary:"Ensure table headers have discernible text",friendlySummary:"Make sure table header cells have visible text."},"frame-tested":{title:"Test all frames",axeSummary:"Ensure <iframe> and <frame> elements contain the axe-core script",friendlySummary:"Make sure axe-core is injected into all frames or iframes so they are tested."},"heading-order":{title:"Heading order",axeSummary:"Ensure the order of headings is semantically correct (no skipping levels)",friendlySummary:"Use proper heading order (don't skip heading levels)."},"image-redundant-alt":{title:"Redundant image alt text",axeSummary:"Ensure image alternative text is not repeated as nearby text",friendlySummary:"Avoid repeating the same information in both an image's alt text and nearby text."},"label-title-only":{title:"Visible form element label",axeSummary:"Ensure each form element has a visible label (not only title/ARIA)",friendlySummary:"Every form input needs a visible label (not only a title attribute or hidden text)."},"landmark-banner-is-top-level":{title:"Top-level landmark banner",axeSummary:"Ensure the banner landmark is at top level (not nested)",friendlySummary:"Use the banner landmark (e.g. site header) only at the top level of the page, not inside another landmark."},"landmark-complementary-is-top-level":{title:"Top-level <aside>",axeSummary:"Ensure the complementary landmark (<aside>) is top level",friendlySummary:'The complementary landmark <aside> or role="complementary" should be a top-level region, not nested in another landmark.'},"landmark-contentinfo-is-top-level":{title:"Top-level contentinfo",axeSummary:"Ensure the contentinfo landmark (footer) is top level",friendlySummary:"Make sure the contentinfo landmark (footer) is at the top level of the page and not contained in another landmark."},"landmark-main-is-top-level":{title:"Top-level main",axeSummary:"Ensure the main landmark is at top level",friendlySummary:"The main landmark should be a top-level element and not nested inside another landmark."},"landmark-no-duplicate-banner":{title:"Duplicate banner landmark",axeSummary:"Ensure the document has at most one banner landmark",friendlySummary:'Have only one role="banner" or <header> on a page.'},"landmark-no-duplicate-contentinfo":{title:"Duplicate contentinfo",axeSummary:"Ensure the document has at most one contentinfo landmark",friendlySummary:'Have only one role="contentinfo" or <footer> on a page.'},"landmark-no-duplicate-main":{title:"Duplicate main",axeSummary:"Ensure the document has at most one main landmark",friendlySummary:'Have only one role="main" or <main> on a page.'},"landmark-one-main":{title:"main landmark",axeSummary:"Ensure the document has a main landmark",friendlySummary:'Include a main landmark on each page using a <main> region or role="main".'},"landmark-unique":{title:"Unique landmark",axeSummary:"Ensure landmarks have a unique role or role/label combination",friendlySummary:"If you use multiple landmarks of the same type, give them unique labels (names)."},"meta-viewport-large":{title:"Significant viewport scaling",axeSummary:'Ensure <meta name="viewport"> can scale a significant amount (e.g. 500%)',friendlySummary:'<meta name="viewport"> should allow users to significantly scale content.'},"page-has-heading-one":{title:"Has <h1>",axeSummary:"Ensure the page (or at least one frame) contains a level-one heading",friendlySummary:"Every page or frame should have at least one <h1> heading."},"presentation-role-conflict":{title:"Presentational content",axeSummary:'Ensure elements with role="presentation"/"none" have no ARIA or tabindex',friendlySummary:`Don't give elements with role="none"/"presentation" any ARIA attributes or a tabindex.`},region:{title:"Landmark regions",axeSummary:"Ensure all page content is contained by landmarks",friendlySummary:"Wrap all page content in appropriate landmark regions (<header>, <main>, <footer>, etc.)."},"scope-attr-valid":{title:"scope attribute",axeSummary:"Ensure the scope attribute is used correctly on tables",friendlySummary:"Use the scope attribute only on <th> elements, with proper values (col, row, etc.)."},"skip-link":{title:"Skip link",axeSummary:'Ensure all "skip" links have a focusable target',friendlySummary:'Make sure any "skip to content" link targets an existing, focusable element.'},tabindex:{title:"tabindex values",axeSummary:"Ensure tabindex attribute values are not greater than 0",friendlySummary:"Don't use tabindex values greater than 0."},"table-duplicate-name":{title:"Duplicate names for table",axeSummary:"Ensure the <caption> does not duplicate the summary attribute text",friendlySummary:"Don't use the same text in both a table's <caption> and its summary attribute."}},Ba={"color-contrast-enhanced":{title:"Enhanced color contrast",axeSummary:"Ensure contrast between text and background meets WCAG 2 AAA enhanced contrast thresholds",friendlySummary:"Use extra-high contrast for text and background to meet WCAG AAA level."},"identical-links-same-purpose":{title:"Same link name, same purpose",axeSummary:"Ensure links with the same accessible name serve a similar purpose",friendlySummary:"If two links have the same text, they should do the same thing (lead to the same content)."},"meta-refresh-no-exceptions":{title:'No <meta http-equiv="refresh">',axeSummary:'Ensure <meta http-equiv="refresh"> is not used for delayed refresh (no exceptions)',friendlySummary:`Don't auto-refresh or redirect pages using <meta http-equiv="refresh"> even with a delay.`}},za={"css-orientation-lock":{title:"CSS orientation lock",axeSummary:"Ensure content is not locked to a specific display orientation (works in all orientations)",friendlySummary:"Don't lock content to one screen orientation; support both portrait and landscape modes."},"focus-order-semantics":{title:"Focus order semantic role",axeSummary:"Ensure elements in the tab order have a role appropriate for interactive content",friendlySummary:"Ensure elements in the tab order have a role appropriate for interactive content"},"hidden-content":{title:"Hidden content",axeSummary:"Informs users about hidden content",friendlySummary:"Display hidden content on the page for test analysis."},"label-content-name-mismatch":{title:"Content name mismatch",axeSummary:"Ensure elements labeled by their content include that text in their accessible name",friendlySummary:"If an element's visible text serves as its label, include that text in its accessible name."},"p-as-heading":{title:"No <p> headings",axeSummary:"Ensure <p> elements aren't styled to look like headings (use real headings)",friendlySummary:"Don't just style a <p> to look like a heading \u2013 use an actual heading tag for headings."},"table-fake-caption":{title:"Table caption",axeSummary:"Ensure that tables with a caption use the <caption> element",friendlySummary:"Use a <caption> element for table captions instead of just styled text."},"td-has-header":{title:"<td> has header",axeSummary:"Ensure each non-empty data cell in large tables (3\xD73+) has one or more headers",friendlySummary:"Every data cell in large tables should be associated with at least one header cell."}},Wa={"aria-roledescription":{title:"aria-roledescription",axeSummary:"Ensure aria-roledescription is only used on elements with an implicit or explicit role",friendlySummary:"Only use aria-roledescription on elements that already have a defined role."}},en={...Fa,...Ha,...Ua,...Ba,...Ga,...za,...Wa},je=e=>en[e.id]?.title||e.id,Ye=e=>en[e.id]?.friendlySummary||e.description,B="storybook/a11y",Ve=`${B}/panel`,ja="a11y",Ya=`${B}/result`,Va=`${B}/request`,qa=`${B}/running`,Ka=`${B}/error`,Qa=`${B}/manual`,Xa=`${B}/select`,Za="writing-tests/accessibility-testing",Ja=`${Za}#why-are-my-tests-failing-in-different-environments`,ce={RESULT:Ya,REQUEST:Va,RUNNING:qa,ERROR:Ka,MANUAL:Qa,SELECT:Xa},Ft="storybook/component-test",er="storybook/a11y",tr=["html","body","main"],He=Ct(Ot.light),Ue={[W.VIOLATION]:He.color.negative,[W.PASS]:He.color.positive,[W.INCOMPLETION]:He.color.warning},tn=fe({parameters:{},results:void 0,highlighted:!1,toggleHighlight:()=>{},tab:W.VIOLATION,handleCopyLink:()=>{},setTab:()=>{},setStatus:()=>{},status:"initial",error:void 0,handleManual:()=>{},discrepancy:null,selectedItems:new Map,allExpanded:!1,toggleOpen:()=>{},handleCollapseAll:()=>{},handleExpandAll:()=>{},handleJumpToElement:()=>{},handleSelectionChange:()=>{}}),nr=e=>{let t=bt("a11y",{}),[a]=yt()??[],n=Re(),r=R((y=!1)=>y?"manual":"initial",[]),i=j(()=>a?.a11y?.manual??!1,[a?.a11y?.manual]),l=j(()=>{let y=n.getQueryParam("a11ySelection");return y&&n.setQueryParams({a11ySelection:""}),y},[n]),[c,d]=De(B),[s,f]=H(()=>{let[y]=l?.split(".")??[];return y&&Object.values(W).includes(y)?y:W.VIOLATION}),[u,p]=H(void 0),[b,h]=H(r(i)),[I,v]=H(!!l),{storyId:g}=gt(),A=pt(y=>y[g]?.[er]?.value);G(()=>ft("storybook/component-test").onAllStatusChange((y,T)=>{let S=y[g]?.[Ft],_=T[g]?.[Ft];S?.value==="status-value:error"&&_?.value!=="status-value:error"&&h("component-test-error")}),[g]);let Y=R(()=>v(y=>!y),[]),[C,D]=H(()=>{let y=new Map;if(l&&/^[a-z]+.[a-z-]+.[0-9]+$/.test(l)){let[T,S]=l.split(".");y.set(`${T}.${S}`,l)}return y}),$=j(()=>c?.[s]?.every(y=>C.has(`${s}.${y.id}`))??!1,[c,C,s]),x=R((y,T,S)=>{y.stopPropagation();let _=`${T}.${S.id}`;D(M=>new Map(M.delete(_)?M:M.set(_,`${_}.1`)))},[]),F=R(()=>{D(new Map)},[]),q=R(()=>{D(y=>new Map(c?.[s]?.map(T=>{let S=`${s}.${T.id}`;return[S,y.get(S)??`${S}.1`]})??[]))},[c,s]),te=R(y=>{let[T,S]=y.split(".");D(_=>new Map(_.set(`${T}.${S}`,y)))},[]),ne=R(y=>{h("error"),p(y)},[]),ae=R((y,T)=>{g===T&&(h("ran"),d(y),setTimeout(()=>{if(b==="ran"&&h("ready"),C.size===1){let[S]=C.values();document.getElementById(S)?.scrollIntoView({behavior:"smooth",block:"center"})}},900))},[d,b,g,C]),se=R((y,T)=>{let[S,_]=y.split("."),{helpUrl:M,nodes:ye}=c?.[S]?.find(ue=>ue.id===_)||{},be=M&&window.open(M,"_blank","noopener,noreferrer");if(ye&&!be){let ue=ye.findIndex(ge=>T.selectors.some(wn=>wn===String(ge.target)))??-1;if(ue!==-1){let ge=`${S}.${_}.${ue+1}`;D(new Map([[`${S}.${_}`,ge]])),setTimeout(()=>{document.getElementById(ge)?.scrollIntoView({behavior:"smooth",block:"center"})},100)}}},[c]),K=R(({reporters:y})=>{let T=y.find(S=>S.type==="a11y");T&&("error"in T.result?ne(T.result.error):ae(T.result,g))},[ne,ae,g]),it=R(({newPhase:y})=>{y==="loading"&&(d(void 0),h(i?"manual":"initial")),y==="afterEach"&&!i&&h("running")},[i,d]),Q=ht({[ce.RESULT]:ae,[ce.ERROR]:ne,[ce.SELECT]:se,[wt]:()=>D(new Map),[Pt]:it,[kt]:K,[Nt]:()=>{h("running"),Q(ce.MANUAL,g,t)}},[it,K,se,ne,ae,t,g]),Tn=R(()=>{h("running"),Q(ce.MANUAL,g,t)},[Q,t,g]),_n=R(async y=>{let{createCopyToClipboardFunction:T}=await Promise.resolve().then(()=>(Ne(),mt));await T()(`${window.location.origin}${y}`)},[]),Cn=R(y=>Q(Dt,y),[Q]);G(()=>{h(r(i))},[r,i]),G(()=>{if(Q(Fe,`${B}/selected`),Q(Fe,`${B}/others`),!I)return;let y=Array.from(C.values()).flatMap(S=>{let[_,M,ye]=S.split(".");if(_!==s)return[];let be=c?.[_]?.find(ue=>ue.id===M)?.nodes[Number(ye)-1]?.target;return be?[String(be)]:[]});Q(Le,{id:`${B}/selected`,priority:1,selectors:y,styles:{outline:`1px solid color-mix(in srgb, ${Ue[s]}, transparent 30%)`,backgroundColor:"transparent"},hoverStyles:{outlineWidth:"2px"},focusStyles:{backgroundColor:"transparent"},menu:c?.[s].map(S=>{let _=S.nodes.flatMap(M=>M.target).map(String).filter(M=>y.includes(M));return[{id:`${s}.${S.id}:info`,title:je(S),description:Ye(S),selectors:_},{id:`${s}.${S.id}`,iconLeft:"info",iconRight:"shareAlt",title:"Learn how to resolve this violation",clickEvent:ce.SELECT,selectors:_}]})});let T=c?.[s].flatMap(S=>S.nodes.flatMap(_=>_.target).map(String)).filter(S=>![...tr,...y].includes(S));Q(Le,{id:`${B}/others`,selectors:T,styles:{outline:`1px solid color-mix(in srgb, ${Ue[s]}, transparent 30%)`,backgroundColor:`color-mix(in srgb, ${Ue[s]}, transparent 60%)`},hoverStyles:{outlineWidth:"2px"},focusStyles:{backgroundColor:"transparent"},menu:c?.[s].map(S=>{let _=S.nodes.flatMap(M=>M.target).map(String).filter(M=>!y.includes(M));return[{id:`${s}.${S.id}:info`,title:je(S),description:Ye(S),selectors:_},{id:`${s}.${S.id}`,iconLeft:"info",iconRight:"shareAlt",title:"Learn how to resolve this violation",clickEvent:ce.SELECT,selectors:_}]})})},[Q,I,c,s,C]);let On=j(()=>{if(!A)return null;if(A==="status-value:success"&&c?.violations.length)return"cliPassedBrowserFailed";if(A==="status-value:error"&&!c?.violations.length){if(b==="ready"||b==="ran")return"browserPassedCliFailed";if(b==="manual")return"cliFailedButModeManual"}return null},[c?.violations.length,b,A]);return o.createElement(tn.Provider,{value:{parameters:t,results:c,highlighted:I,toggleHighlight:Y,tab:s,setTab:f,handleCopyLink:_n,status:b,setStatus:h,error:u,handleManual:Tn,discrepancy:On,selectedItems:C,toggleOpen:x,allExpanded:$,handleCollapseAll:F,handleExpandAll:q,handleJumpToElement:Cn,handleSelectionChange:te},...e})},nt=()=>ve(tn);function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},N.apply(null,arguments)}function ee(e,t,{checkForDefaultPrevented:a=!0}={}){return function(n){if(e?.(n),a===!1||!n.defaultPrevented)return t?.(n)}}function at(e,t=[]){let a=[];function n(i,l){let c=fe(l),d=a.length;a=[...a,l];function s(u){let{scope:p,children:b,...h}=u,I=p?.[e][d]||c,v=j(()=>h,Object.values(h));return m(I.Provider,{value:v},b)}function f(u,p){let b=p?.[e][d]||c,h=ve(b);if(h)return h;if(l!==void 0)return l;throw new Error(`\`${u}\` must be used within \`${i}\``)}return s.displayName=i+"Provider",[s,f]}let r=()=>{let i=a.map(l=>fe(l));return function(l){let c=l?.[e]||i;return j(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return r.scopeName=e,[n,ar(r,...t)]}function ar(...e){let t=e[0];if(e.length===1)return t;let a=()=>{let n=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(r){let i=n.reduce((l,{useScope:c,scopeName:d})=>{let s=c(r)[`__scope${d}`];return{...l,...s}},{});return j(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return a.scopeName=t.scopeName,a}function rr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function nn(...e){return t=>e.forEach(a=>rr(a,t))}function Ht(...e){return R(nn(...e),e)}var qe=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(lr);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Ke,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Ke,N({},n,{ref:t}),a)});qe.displayName="Slot";var Ke=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...ir(n,a.props),ref:t?nn(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Ke.displayName="SlotClone";var or=({children:e})=>m(me,null,e);function lr(e){return U(e)&&e.type===or}function ir(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}function cr(e){let t=e+"CollectionProvider",[a,n]=at(t),[r,i]=a(t,{collectionRef:{current:null},itemMap:new Map}),l=b=>{let{scope:h,children:I}=b,v=o.useRef(null),g=o.useRef(new Map).current;return o.createElement(r,{scope:h,itemMap:g,collectionRef:v},I)},c=e+"CollectionSlot",d=o.forwardRef((b,h)=>{let{scope:I,children:v}=b,g=i(c,I),A=Ht(h,g.collectionRef);return o.createElement(qe,{ref:A},v)}),s=e+"CollectionItemSlot",f="data-radix-collection-item",u=o.forwardRef((b,h)=>{let{scope:I,children:v,...g}=b,A=o.useRef(null),Y=Ht(h,A),C=i(s,I);return o.useEffect(()=>(C.itemMap.set(A,{ref:A,...g}),()=>void C.itemMap.delete(A))),o.createElement(qe,{[f]:"",ref:Y},v)});function p(b){let h=i(e+"CollectionConsumer",b);return o.useCallback(()=>{let I=h.collectionRef.current;if(!I)return[];let v=Array.from(I.querySelectorAll(`[${f}]`));return Array.from(h.itemMap.values()).sort((g,A)=>v.indexOf(g.ref.current)-v.indexOf(A.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:l,Slot:d,ItemSlot:u},p,n]}function sr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function an(...e){return t=>e.forEach(a=>sr(a,t))}function ur(...e){return R(an(...e),e)}var dr=globalThis?.document?Ee:()=>{},mr=ut||(()=>{}),fr=0;function rn(e){let[t,a]=H(mr());return dr(()=>{e||a(n=>n??String(fr++))},[e]),e||(t?`radix-${t}`:"")}var on=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(hr);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Qe,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Qe,N({},n,{ref:t}),a)});on.displayName="Slot";var Qe=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...yr(n,a.props),ref:t?an(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Qe.displayName="SlotClone";var pr=({children:e})=>m(me,null,e);function hr(e){return U(e)&&e.type===pr}function yr(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}var br=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ln=br.reduce((e,t)=>{let a=L((n,r)=>{let{asChild:i,...l}=n,c=i?on:t;return G(()=>{window[Symbol.for("radix-ui")]=!0},[]),m(c,N({},l,{ref:r}))});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function gr(e){let t=z(e);return G(()=>{t.current=e}),j(()=>(...a)=>{var n;return(n=t.current)===null||n===void 0?void 0:n.call(t,...a)},[])}function cn(e){let t=z(e);return G(()=>{t.current=e}),j(()=>(...a)=>{var n;return(n=t.current)===null||n===void 0?void 0:n.call(t,...a)},[])}function sn({prop:e,defaultProp:t,onChange:a=()=>{}}){let[n,r]=Sr({defaultProp:t,onChange:a}),i=e!==void 0,l=i?e:n,c=cn(a),d=R(s=>{if(i){let f=typeof s=="function"?s(e):s;f!==e&&c(f)}else r(s)},[i,e,r,c]);return[l,d]}function Sr({defaultProp:e,onChange:t}){let a=H(e),[n]=a,r=z(n),i=cn(t);return G(()=>{r.current!==n&&(i(n),r.current=n)},[n,r,i]),a}var vr=fe(void 0);function un(e){let t=ve(vr);return e||t||"ltr"}var Ge="rovingFocusGroup.onEntryFocus",Er={bubbles:!1,cancelable:!0},rt="RovingFocusGroup",[Xe,dn,Ir]=cr(rt),[xr,mn]=at(rt,[Ir]),[Rr,Ar]=xr(rt),$r=L((e,t)=>m(Xe.Provider,{scope:e.__scopeRovingFocusGroup},m(Xe.Slot,{scope:e.__scopeRovingFocusGroup},m(Tr,N({},e,{ref:t}))))),Tr=L((e,t)=>{let{__scopeRovingFocusGroup:a,orientation:n,loop:r=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:s,...f}=e,u=z(null),p=ur(t,u),b=un(i),[h=null,I]=sn({prop:l,defaultProp:c,onChange:d}),[v,g]=H(!1),A=gr(s),Y=dn(a),C=z(!1),[D,$]=H(0);return G(()=>{let x=u.current;if(x)return x.addEventListener(Ge,A),()=>x.removeEventListener(Ge,A)},[A]),m(Rr,{scope:a,orientation:n,dir:b,loop:r,currentTabStopId:h,onItemFocus:R(x=>I(x),[I]),onItemShiftTab:R(()=>g(!0),[]),onFocusableItemAdd:R(()=>$(x=>x+1),[]),onFocusableItemRemove:R(()=>$(x=>x-1),[])},m(ln.div,N({tabIndex:v||D===0?-1:0,"data-orientation":n},f,{ref:p,style:{outline:"none",...e.style},onMouseDown:ee(e.onMouseDown,()=>{C.current=!0}),onFocus:ee(e.onFocus,x=>{let F=!C.current;if(x.target===x.currentTarget&&F&&!v){let q=new CustomEvent(Ge,Er);if(x.currentTarget.dispatchEvent(q),!q.defaultPrevented){let te=Y().filter(K=>K.focusable),ne=te.find(K=>K.active),ae=te.find(K=>K.id===h),se=[ne,ae,...te].filter(Boolean).map(K=>K.ref.current);fn(se)}}C.current=!1}),onBlur:ee(e.onBlur,()=>g(!1))})))}),_r="RovingFocusGroupItem",Cr=L((e,t)=>{let{__scopeRovingFocusGroup:a,focusable:n=!0,active:r=!1,tabStopId:i,...l}=e,c=rn(),d=i||c,s=Ar(_r,a),f=s.currentTabStopId===d,u=dn(a),{onFocusableItemAdd:p,onFocusableItemRemove:b}=s;return G(()=>{if(n)return p(),()=>b()},[n,p,b]),m(Xe.ItemSlot,{scope:a,id:d,focusable:n,active:r},m(ln.span,N({tabIndex:f?0:-1,"data-orientation":s.orientation},l,{ref:t,onMouseDown:ee(e.onMouseDown,h=>{n?s.onItemFocus(d):h.preventDefault()}),onFocus:ee(e.onFocus,()=>s.onItemFocus(d)),onKeyDown:ee(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){s.onItemShiftTab();return}if(h.target!==h.currentTarget)return;let I=kr(h,s.orientation,s.dir);if(I!==void 0){h.preventDefault();let v=u().filter(g=>g.focusable).map(g=>g.ref.current);if(I==="last")v.reverse();else if(I==="prev"||I==="next"){I==="prev"&&v.reverse();let g=v.indexOf(h.currentTarget);v=s.loop?Nr(v,g+1):v.slice(g+1)}setTimeout(()=>fn(v))}})})))}),Or={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function wr(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function kr(e,t,a){let n=wr(e.key,a);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return Or[n]}function fn(e){let t=document.activeElement;for(let a of e)if(a===t||(a.focus(),document.activeElement!==t))return}function Nr(e,t){return e.map((a,n)=>e[(t+n)%e.length])}var Pr=$r,Dr=Cr;function Mr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Lr(...e){return t=>e.forEach(a=>Mr(a,t))}function Fr(...e){return R(Lr(...e),e)}var Ut=globalThis?.document?Ee:()=>{};function Hr(e,t){return dt((a,n)=>t[a][n]??a,e)}var pn=e=>{let{present:t,children:a}=e,n=Ur(t),r=typeof a=="function"?a({present:n.isPresent}):P.only(a),i=Fr(n.ref,r.ref);return typeof a=="function"||n.isPresent?V(r,{ref:i}):null};pn.displayName="Presence";function Ur(e){let[t,a]=H(),n=z({}),r=z(e),i=z("none"),l=e?"mounted":"unmounted",[c,d]=Hr(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return G(()=>{let s=$e(n.current);i.current=c==="mounted"?s:"none"},[c]),Ut(()=>{let s=n.current,f=r.current;if(f!==e){let u=i.current,p=$e(s);e?d("MOUNT"):p==="none"||s?.display==="none"?d("UNMOUNT"):d(f&&u!==p?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,d]),Ut(()=>{if(t){let s=u=>{let p=$e(n.current).includes(u.animationName);u.target===t&&p&&Lt(()=>d("ANIMATION_END"))},f=u=>{u.target===t&&(i.current=$e(n.current))};return t.addEventListener("animationstart",f),t.addEventListener("animationcancel",s),t.addEventListener("animationend",s),()=>{t.removeEventListener("animationstart",f),t.removeEventListener("animationcancel",s),t.removeEventListener("animationend",s)}}else d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:R(s=>{s&&(n.current=getComputedStyle(s)),a(s)},[])}}function $e(e){return e?.animationName||"none"}function Gr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Br(...e){return t=>e.forEach(a=>Gr(a,t))}var hn=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(Wr);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Ze,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Ze,N({},n,{ref:t}),a)});hn.displayName="Slot";var Ze=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...jr(n,a.props),ref:t?Br(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Ze.displayName="SlotClone";var zr=({children:e})=>m(me,null,e);function Wr(e){return U(e)&&e.type===zr}function jr(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}var Yr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],_e=Yr.reduce((e,t)=>{let a=L((n,r)=>{let{asChild:i,...l}=n,c=i?hn:t;return G(()=>{window[Symbol.for("radix-ui")]=!0},[]),m(c,N({},l,{ref:r}))});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),yn="Tabs",[Vr,Bp]=at(yn,[mn]),bn=mn(),[qr,ot]=Vr(yn),Kr=L((e,t)=>{let{__scopeTabs:a,value:n,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:c,activationMode:d="automatic",...s}=e,f=un(c),[u,p]=sn({prop:n,onChange:r,defaultProp:i});return m(qr,{scope:a,baseId:rn(),value:u,onValueChange:p,orientation:l,dir:f,activationMode:d},m(_e.div,N({dir:f,"data-orientation":l},s,{ref:t})))}),Qr="TabsList",Xr=L((e,t)=>{let{__scopeTabs:a,loop:n=!0,...r}=e,i=ot(Qr,a),l=bn(a);return m(Pr,N({asChild:!0},l,{orientation:i.orientation,dir:i.dir,loop:n}),m(_e.div,N({role:"tablist","aria-orientation":i.orientation},r,{ref:t})))}),Zr="TabsTrigger",Jr=L((e,t)=>{let{__scopeTabs:a,value:n,disabled:r=!1,...i}=e,l=ot(Zr,a),c=bn(a),d=gn(l.baseId,n),s=Sn(l.baseId,n),f=n===l.value;return m(Dr,N({asChild:!0},c,{focusable:!r,active:f}),m(_e.button,N({type:"button",role:"tab","aria-selected":f,"aria-controls":s,"data-state":f?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:d},i,{ref:t,onMouseDown:ee(e.onMouseDown,u=>{!r&&u.button===0&&u.ctrlKey===!1?l.onValueChange(n):u.preventDefault()}),onKeyDown:ee(e.onKeyDown,u=>{[" ","Enter"].includes(u.key)&&l.onValueChange(n)}),onFocus:ee(e.onFocus,()=>{let u=l.activationMode!=="manual";!f&&!r&&u&&l.onValueChange(n)})})))}),eo="TabsContent",to=L((e,t)=>{let{__scopeTabs:a,value:n,forceMount:r,children:i,...l}=e,c=ot(eo,a),d=gn(c.baseId,n),s=Sn(c.baseId,n),f=n===c.value,u=z(f);return G(()=>{let p=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(p)},[]),m(pn,{present:r||f},({present:p})=>m(_e.div,N({"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!p,id:s,tabIndex:0},l,{ref:t,style:{...e.style,animationDuration:u.current?"0s":void 0}}),p&&i))});function gn(e,t){return`${e}-trigger-${t}`}function Sn(e,t){return`${e}-content-${t}`}var no=Kr,ao=Xr,ro=Jr,Gt=to,Bt=E(we)(({theme:e})=>({fontSize:e.typography.size.s1}),({language:e})=>e==="css"&&{".selector ~ span:nth-last-of-type(-n+3)":{display:"none"}}),oo=E.div({display:"flex",flexDirection:"column"}),lo=E.div(({theme:e})=>({display:"block",color:e.textMutedColor,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,marginTop:-8,marginBottom:12,"@container (min-width: 800px)":{display:"none"}})),io=E.p({margin:0}),co=E.div({display:"flex",flexDirection:"column",padding:"0 15px 20px 15px",gap:20}),so=E.div({gap:15,"@container (min-width: 800px)":{display:"grid",gridTemplateColumns:"50% 50%"}}),zt=E.div(({theme:e,side:t})=>({display:t==="left"?"flex":"none",flexDirection:"column",gap:15,margin:t==="left"?"15px 0":0,padding:t==="left"?"0 15px":0,borderLeft:t==="left"?`1px solid ${e.color.border}`:"none","&:focus-visible":{outline:"none",borderRadius:4,boxShadow:`0 0 0 1px inset ${e.color.secondary}`},"@container (min-width: 800px)":{display:t==="left"?"none":"flex"}})),uo=E(re)(({theme:e})=>({fontFamily:e.typography.fonts.mono,fontWeight:e.typography.weight.regular,color:e.textMutedColor,height:40,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block",width:"100%",textAlign:"left",padding:"0 12px",'&[data-state="active"]':{color:e.color.secondary,backgroundColor:e.background.hoverable}})),mo=E.div({display:"flex",flexDirection:"column",gap:10}),fo=E.div({display:"flex",gap:10}),po=({onClick:e})=>{let[t,a]=H(!1),n=R(()=>{e(),a(!0);let r=setTimeout(()=>a(!1),2e3);return()=>clearTimeout(r)},[e]);return o.createElement(re,{onClick:n},t?o.createElement(vt,null):o.createElement(xt,null)," ",t?"Copied":"Copy link")},ho=({id:e,item:t,type:a,selection:n,handleSelectionChange:r})=>o.createElement(co,{id:e},o.createElement(oo,null,o.createElement(lo,null,t.id),o.createElement(io,null,Ye(t)," ",o.createElement(Ie,{href:t.helpUrl,target:"_blank",rel:"noopener noreferrer",withArrow:!0},"Learn how to resolve this violation"))),o.createElement(no,{defaultValue:n,orientation:"vertical",value:n,onValueChange:r,asChild:!0},o.createElement(so,null,o.createElement(ao,{"aria-label":a},t.nodes.map((i,l)=>{let c=`${a}.${t.id}.${l+1}`;return o.createElement(me,{key:c},o.createElement(ro,{value:c,asChild:!0},o.createElement(uo,{variant:"ghost",size:"medium",id:c},l+1,". ",i.html)),o.createElement(Gt,{value:c,asChild:!0},o.createElement(zt,{side:"left"},Wt(i))))})),t.nodes.map((i,l)=>{let c=`${a}.${t.id}.${l+1}`;return o.createElement(Gt,{key:c,value:c,asChild:!0},o.createElement(zt,{side:"right"},Wt(i)))}))));function Wt(e){let{handleCopyLink:t,handleJumpToElement:a}=nt(),{any:n,all:r,none:i,html:l,target:c}=e,d=[...n,...r,...i];return o.createElement(o.Fragment,null,o.createElement(mo,null,d.map(s=>o.createElement("div",{key:s.id},`${s.message}${/(\.|: [^.]+\.*)$/.test(s.message)?"":"."}`))),o.createElement(fo,null,o.createElement(re,{onClick:()=>a(e.target.toString())},o.createElement(Tt,null)," Jump to element"),o.createElement(po,{onClick:()=>t(e.linkPath)})),o.createElement(Bt,{language:"jsx",wrapLongLines:!0},`/* element */
${l}`),o.createElement(Bt,{language:"css",wrapLongLines:!0},`/* selector */
${c} {}`))}var yo={minor:"neutral",moderate:"warning",serious:"negative",critical:"critical"},bo={minor:"Minor",moderate:"Moderate",serious:"Serious",critical:"Critical"},go=E.div(({theme:e})=>({display:"flex",flexDirection:"column",width:"100%",borderBottom:`1px solid ${e.appBorderColor}`,containerType:"inline-size",fontSize:e.typography.size.s2})),So=E(Et)({transition:"transform 0.1s ease-in-out"}),vo=E.div(({theme:e})=>({display:"flex",justifyContent:"space-between",alignItems:"center",gap:6,padding:"6px 10px 6px 15px",minHeight:40,background:"none",color:"inherit",textAlign:"left",cursor:"pointer",width:"100%","&:hover":{color:e.color.secondary}})),Eo=E.div(({theme:e})=>({display:"flex",alignItems:"baseline",flexGrow:1,fontSize:e.typography.size.s2,gap:8})),Io=E.div(({theme:e})=>({display:"none",color:e.textMutedColor,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,"@container (min-width: 800px)":{display:"block"}})),xo=E.div(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",color:e.textMutedColor,width:28,height:28})),Be=({items:e,empty:t,type:a,handleSelectionChange:n,selectedItems:r,toggleOpen:i})=>o.createElement(o.Fragment,null,e&&e.length?e.map(l=>{let c=`${a}.${l.id}`,d=`details:${c}`,s=r.get(c),f=je(l);return o.createElement(go,{key:c},o.createElement(vo,{onClick:u=>i(u,a,l),"data-active":!!s},o.createElement(Eo,null,o.createElement("strong",null,f),o.createElement(Io,null,l.id)),l.impact&&o.createElement(le,{status:a===W.PASS?"neutral":yo[l.impact]},bo[l.impact]),o.createElement(xo,null,l.nodes.length),o.createElement(ie,{onClick:u=>i(u,a,l),"aria-label":`${s?"Collapse":"Expand"} details for ${f}`,"aria-expanded":!!s,"aria-controls":d},o.createElement(So,{style:{transform:`rotate(${s?-180:0}deg)`}}))),s?o.createElement(ho,{id:d,item:l,type:a,selection:s,handleSelectionChange:n}):o.createElement("div",{id:d}))}):o.createElement(Ce,{title:t})),Je=function(e,t){return Je=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,n){a.__proto__=n}||function(a,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(a[r]=n[r])},Je(e,t)};function Ro(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Je(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}var et=function(){return et=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},et.apply(this,arguments)};function Ao(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}var Te=typeof globalThis<"u"?globalThis:typeof window<"u"||typeof window<"u"?window:typeof self<"u"?self:{};function $o(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var lt=$o,To=typeof Te=="object"&&Te&&Te.Object===Object&&Te,_o=To,Co=_o,Oo=typeof self=="object"&&self&&self.Object===Object&&self,wo=Co||Oo||Function("return this")(),vn=wo,ko=vn,No=function(){return ko.Date.now()},Po=No,Do=/\s/;function Mo(e){for(var t=e.length;t--&&Do.test(e.charAt(t)););return t}var Lo=Mo,Fo=Lo,Ho=/^\s+/;function Uo(e){return e&&e.slice(0,Fo(e)+1).replace(Ho,"")}var Go=Uo,Bo=vn,zo=Bo.Symbol,En=zo,jt=En,In=Object.prototype,Wo=In.hasOwnProperty,jo=In.toString,he=jt?jt.toStringTag:void 0;function Yo(e){var t=Wo.call(e,he),a=e[he];try{e[he]=void 0;var n=!0}catch{}var r=jo.call(e);return n&&(t?e[he]=a:delete e[he]),r}var Vo=Yo,qo=Object.prototype,Ko=qo.toString;function Qo(e){return Ko.call(e)}var Xo=Qo,Yt=En,Zo=Vo,Jo=Xo,el="[object Null]",tl="[object Undefined]",Vt=Yt?Yt.toStringTag:void 0;function nl(e){return e==null?e===void 0?tl:el:Vt&&Vt in Object(e)?Zo(e):Jo(e)}var al=nl;function rl(e){return e!=null&&typeof e=="object"}var ol=rl,ll=al,il=ol,cl="[object Symbol]";function sl(e){return typeof e=="symbol"||il(e)&&ll(e)==cl}var ul=sl,dl=Go,qt=lt,ml=ul,Kt=NaN,fl=/^[-+]0x[0-9a-f]+$/i,pl=/^0b[01]+$/i,hl=/^0o[0-7]+$/i,yl=parseInt;function bl(e){if(typeof e=="number")return e;if(ml(e))return Kt;if(qt(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=qt(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=dl(e);var a=pl.test(e);return a||hl.test(e)?yl(e.slice(2),a?2:8):fl.test(e)?Kt:+e}var gl=bl,Sl=lt,ze=Po,Qt=gl,vl="Expected a function",El=Math.max,Il=Math.min;function xl(e,t,a){var n,r,i,l,c,d,s=0,f=!1,u=!1,p=!0;if(typeof e!="function")throw new TypeError(vl);t=Qt(t)||0,Sl(a)&&(f=!!a.leading,u="maxWait"in a,i=u?El(Qt(a.maxWait)||0,t):i,p="trailing"in a?!!a.trailing:p);function b($){var x=n,F=r;return n=r=void 0,s=$,l=e.apply(F,x),l}function h($){return s=$,c=setTimeout(g,t),f?b($):l}function I($){var x=$-d,F=$-s,q=t-x;return u?Il(q,i-F):q}function v($){var x=$-d,F=$-s;return d===void 0||x>=t||x<0||u&&F>=i}function g(){var $=ze();if(v($))return A($);c=setTimeout(g,I($))}function A($){return c=void 0,p&&n?b($):(n=r=void 0,l)}function Y(){c!==void 0&&clearTimeout(c),s=0,n=d=r=c=void 0}function C(){return c===void 0?l:A(ze())}function D(){var $=ze(),x=v($);if(n=arguments,r=this,d=$,x){if(c===void 0)return h(d);if(u)return clearTimeout(c),c=setTimeout(g,t),b(d)}return c===void 0&&(c=setTimeout(g,t)),l}return D.cancel=Y,D.flush=C,D}var xn=xl,Rl=xn,Al=lt,$l="Expected a function";function Tl(e,t,a){var n=!0,r=!0;if(typeof e!="function")throw new TypeError($l);return Al(a)&&(n="leading"in a?!!a.leading:n,r="trailing"in a?!!a.trailing:r),Rl(e,t,{leading:n,maxWait:t,trailing:r})}var _l=Tl,Rn=function(e,t,a,n){switch(t){case"debounce":return xn(e,a,n);case"throttle":return _l(e,a,n);default:return e}},tt=function(e){return typeof e=="function"},oe=function(){return typeof window>"u"},Xt=function(e){return e instanceof Element||e instanceof HTMLDocument},An=function(e,t,a,n){return function(r){var i=r.width,l=r.height;t(function(c){return c.width===i&&c.height===l||c.width===i&&!n||c.height===l&&!a?c:(e&&tt(e)&&e(i,l),{width:i,height:l})})}};(function(e){Ro(t,e);function t(a){var n=e.call(this,a)||this;n.cancelHandler=function(){n.resizeHandler&&n.resizeHandler.cancel&&(n.resizeHandler.cancel(),n.resizeHandler=null)},n.attachObserver=function(){var s=n.props,f=s.targetRef,u=s.observerOptions;if(!oe()){f&&f.current&&(n.targetRef.current=f.current);var p=n.getElement();p&&(n.observableElement&&n.observableElement===p||(n.observableElement=p,n.resizeObserver.observe(p,u)))}},n.getElement=function(){var s=n.props,f=s.querySelector,u=s.targetDomEl;if(oe())return null;if(f)return document.querySelector(f);if(u&&Xt(u))return u;if(n.targetRef&&Xt(n.targetRef.current))return n.targetRef.current;var p=Mt(n);if(!p)return null;var b=n.getRenderType();switch(b){case"renderProp":return p;case"childFunction":return p;case"child":return p;case"childArray":return p;default:return p.parentElement}},n.createResizeHandler=function(s){var f=n.props,u=f.handleWidth,p=u===void 0?!0:u,b=f.handleHeight,h=b===void 0?!0:b,I=f.onResize;if(!(!p&&!h)){var v=An(I,n.setState.bind(n),p,h);s.forEach(function(g){var A=g&&g.contentRect||{},Y=A.width,C=A.height,D=!n.skipOnMount&&!oe();D&&v({width:Y,height:C}),n.skipOnMount=!1})}},n.getRenderType=function(){var s=n.props,f=s.render,u=s.children;return tt(f)?"renderProp":tt(u)?"childFunction":U(u)?"child":Array.isArray(u)?"childArray":"parent"};var r=a.skipOnMount,i=a.refreshMode,l=a.refreshRate,c=l===void 0?1e3:l,d=a.refreshOptions;return n.state={width:void 0,height:void 0},n.skipOnMount=r,n.targetRef=st(),n.observableElement=null,oe()||(n.resizeHandler=Rn(n.createResizeHandler,i,c,d),n.resizeObserver=new window.ResizeObserver(n.resizeHandler)),n}return t.prototype.componentDidMount=function(){this.attachObserver()},t.prototype.componentDidUpdate=function(){this.attachObserver()},t.prototype.componentWillUnmount=function(){oe()||(this.observableElement=null,this.resizeObserver.disconnect(),this.cancelHandler())},t.prototype.render=function(){var a=this.props,n=a.render,r=a.children,i=a.nodeType,l=i===void 0?"div":i,c=this.state,d=c.width,s=c.height,f={width:d,height:s,targetRef:this.targetRef},u=this.getRenderType(),p;switch(u){case"renderProp":return n&&n(f);case"childFunction":return p=r,p(f);case"child":if(p=r,p.type&&typeof p.type=="string"){f.targetRef;var b=Ao(f,["targetRef"]);return V(p,b)}return V(p,f);case"childArray":return p=r,p.map(function(h){return!!h&&V(h,f)});default:return m(l,null)}},t})(ct);var Cl=oe()?G:Ee;function Ol(e){e===void 0&&(e={});var t=e.skipOnMount,a=t===void 0?!1:t,n=e.refreshMode,r=e.refreshRate,i=r===void 0?1e3:r,l=e.refreshOptions,c=e.handleWidth,d=c===void 0?!0:c,s=e.handleHeight,f=s===void 0?!0:s,u=e.targetRef,p=e.observerOptions,b=e.onResize,h=z(a),I=z(null),v=u??I,g=z(),A=H({width:void 0,height:void 0}),Y=A[0],C=A[1];return Cl(function(){if(!oe()){var D=An(b,C,d,f),$=function(F){!d&&!f||F.forEach(function(q){var te=q&&q.contentRect||{},ne=te.width,ae=te.height,se=!h.current&&!oe();se&&D({width:ne,height:ae}),h.current=!1})};g.current=Rn($,n,i,l);var x=new window.ResizeObserver(g.current);return v.current&&x.observe(v.current,p),function(){x.disconnect();var F=g.current;F&&F.cancel&&F.cancel()}}},[n,i,l,d,f,b,p,v.current]),et({ref:v},Y)}var wl=E.div({width:"100%",position:"relative",minHeight:"100%"}),kl=E.button(({theme:e})=>({textDecoration:"none",padding:"10px 15px",cursor:"pointer",color:e.textMutedColor,fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,lineHeight:1,height:40,border:"none",borderBottom:"3px solid transparent",background:"transparent","&:focus":{outline:"0 none",borderColor:e.color.secondary}}),({active:e,theme:t})=>e?{opacity:1,color:t.color.secondary,borderColor:t.color.secondary}:{}),Nl=E.div(({theme:e})=>({boxShadow:`${e.appBorderColor} 0 -1px 0 0 inset`,background:e.background.app,position:"sticky",top:0,zIndex:1,display:"flex",alignItems:"center",whiteSpace:"nowrap",overflow:"auto",paddingRight:10,gap:6})),Pl=E.div({}),Dl=E.div({display:"flex",flexBasis:"100%",justifyContent:"flex-end",containerType:"inline-size",minWidth:96,gap:6}),Ml=E(ie)({"@container (max-width: 193px)":{span:{display:"none"}}}),Ll=({tabs:e})=>{let{ref:t}=Ol({refreshMode:"debounce",handleHeight:!1,handleWidth:!0}),{tab:a,setTab:n,toggleHighlight:r,highlighted:i,handleManual:l,allExpanded:c,handleCollapseAll:d,handleExpandAll:s}=nt(),f=R(u=>{n(u.currentTarget.getAttribute("data-type"))},[n]);return m(wl,{ref:t},m(Nl,null,m(Pl,{role:"tablist"},e.map((u,p)=>m(kl,{role:"tab",key:p,"data-type":u.type,"data-active":a===u.type,"aria-selected":a===u.type,active:a===u.type,onClick:f},u.label))),m(Dl,null,m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(pe,{note:"Highlight elements with accessibility violations"}),trigger:"hover"},m(Ml,{onClick:r,active:i},i?m(At,null):m($t,null),m("span",null,i?"Hide highlights":"Show highlights"))),m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(pe,{note:c?"Collapse all":"Expand all"}),trigger:"hover"},m(ie,{onClick:c?d:s,"aria-label":c?"Collapse all":"Expand all"},c?m(It,null):m(Rt,null))),m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(pe,{note:"Rerun the accessibility scan"}),trigger:"hover"},m(ie,{onClick:l,"aria-label":"Rerun accessibility scan"},m(Me,null))))),m(Oe,{vertical:!0,horizontal:!0},e.find(u=>u.type===a)?.panel))},Fl=E.div(({theme:{color:e,typography:t,background:a}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:a.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative",code:{fontSize:`${t.size.s1-1}px`,color:"inherit",margin:"0 0.2em",padding:"0 0.2em",background:"rgba(255, 255, 255, 0.8)",borderRadius:"2px",boxShadow:"0 0 0 1px rgba(0, 0, 0, 0.1)"}})),Hl=({discrepancy:e})=>{let t=Re().getDocsUrl({subpath:Ja,versioned:!0,renderer:!0}),a=j(()=>{switch(e){case"browserPassedCliFailed":return"Accessibility checks passed in this browser but failed in the CLI.";case"cliPassedBrowserFailed":return"Accessibility checks passed in the CLI but failed in this browser.";case"cliFailedButModeManual":return"Accessibility checks failed in the CLI. Run the tests manually to see the results.";default:return null}},[e]);return a?o.createElement(Fl,null,a," ",o.createElement(Ie,{href:t,target:"_blank",withArrow:!0},"Learn what could cause this")):null},Zt=E(Me)(({theme:e})=>({animation:`${e.animation.rotate360} 1s linear infinite;`,margin:4})),We=E.div({display:"flex",alignItems:"center",gap:6}),Jt=E.span(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:e.typography.size.s2,height:"100%",gap:24,div:{display:"flex",flexDirection:"column",alignItems:"center",gap:8},p:{margin:0,color:e.textMutedColor},code:{display:"inline-block",fontSize:e.typography.size.s2-1,backgroundColor:e.background.app,border:`1px solid ${e.color.border}`,borderRadius:4,padding:"2px 3px"}})),Ul=()=>{let{parameters:e,tab:t,results:a,status:n,handleManual:r,error:i,discrepancy:l,handleSelectionChange:c,selectedItems:d,toggleOpen:s}=nt(),f=j(()=>{let{passes:u,incomplete:p,violations:b}=a??{passes:[],incomplete:[],violations:[]};return[{label:o.createElement(We,null,"Violations",o.createElement(le,{compact:!0,status:t==="violations"?"active":"neutral"},b.length)),panel:o.createElement(Be,{items:b,type:W.VIOLATION,empty:"No accessibility violations found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:b,type:W.VIOLATION},{label:o.createElement(We,null,"Passes",o.createElement(le,{compact:!0,status:t==="passes"?"active":"neutral"},u.length)),panel:o.createElement(Be,{items:u,type:W.PASS,empty:"No passing accessibility checks found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:u,type:W.PASS},{label:o.createElement(We,null,"Inconclusive",o.createElement(le,{compact:!0,status:t==="incomplete"?"active":"neutral"},p.length)),panel:o.createElement(Be,{items:p,type:W.INCOMPLETION,empty:"No inconclusive accessibility checks found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:p,type:W.INCOMPLETION}]},[t,a,c,d,s]);return e.disable||e.test==="off"?o.createElement(Jt,null,o.createElement("div",null,o.createElement("strong",null,"Accessibility tests are disabled for this story"),o.createElement("p",null,"Update"," ",o.createElement("code",null,e.disable?"parameters.a11y.disable":"parameters.a11y.test")," ","to enable accessibility tests."))):o.createElement(o.Fragment,null,l&&o.createElement(Hl,{discrepancy:l}),n==="ready"||n==="ran"?o.createElement(Ll,{key:"tabs",tabs:f}):o.createElement(Jt,{style:{marginTop:l?"1em":0}},n==="initial"&&o.createElement("div",null,o.createElement(Zt,{size:12}),o.createElement("strong",null,"Preparing accessibility scan"),o.createElement("p",null,"Please wait while the addon is initializing...")),n==="manual"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"Accessibility tests run manually for this story"),o.createElement("p",null,"Results will not show when using the testing module. You can still run accessibility tests manually.")),o.createElement(re,{size:"medium",onClick:r},"Run accessibility scan"),o.createElement("p",null,"Update ",o.createElement("code",null,"globals.a11y.manual")," to disable manual mode.")),n==="running"&&o.createElement("div",null,o.createElement(Zt,{size:12}),o.createElement("strong",null,"Accessibility scan in progress"),o.createElement("p",null,"Please wait while the accessibility scan is running...")),n==="error"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"The accessibility scan encountered an error"),o.createElement("p",null,typeof i=="string"?i:i instanceof Error?i.toString():JSON.stringify(i,null,2))),o.createElement(re,{size:"medium",onClick:r},"Rerun accessibility scan")),n==="component-test-error"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"This story's component tests failed"),o.createElement("p",null,"Automated accessibility tests will not run until this is resolved. You can still test manually.")),o.createElement(re,{size:"medium",onClick:r},"Run accessibility scan"))))},Gl=e=>m("svg",{...e},m("defs",null,m("filter",{id:"protanopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.567, 0.433, 0, 0, 0 0.558, 0.442, 0, 0, 0 0, 0.242, 0.758, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"protanomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.817, 0.183, 0, 0, 0 0.333, 0.667, 0, 0, 0 0, 0.125, 0.875, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"deuteranopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.625, 0.375, 0, 0, 0 0.7, 0.3, 0, 0, 0 0, 0.3, 0.7, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"deuteranomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.8, 0.2, 0, 0, 0 0.258, 0.742, 0, 0, 0 0, 0.142, 0.858, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"tritanopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.95, 0.05,  0, 0, 0 0,  0.433, 0.567, 0, 0 0,  0.475, 0.525, 0, 0 0,  0, 0, 1, 0"})),m("filter",{id:"tritanomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.967, 0.033, 0, 0, 0 0, 0.733, 0.267, 0, 0 0, 0.183, 0.817, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"achromatopsia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0, 0, 0, 1, 0"})))),Bl="storybook-preview-iframe",zl=[{name:"blurred vision",percentage:22.9},{name:"deuteranomaly",percentage:2.7},{name:"deuteranopia",percentage:.56},{name:"protanomaly",percentage:.66},{name:"protanopia",percentage:.59},{name:"tritanomaly",percentage:.01},{name:"tritanopia",percentage:.016},{name:"achromatopsia",percentage:1e-4},{name:"grayscale"}],$n=e=>e?e==="blurred vision"?"blur(2px)":e==="grayscale"?"grayscale(100%)":`url('#${e}')`:"none",Wl=E.div({"&, & svg":{position:"absolute",width:0,height:0}}),jl=E.span({background:"linear-gradient(to right, #F44336, #FF9800, #FFEB3B, #8BC34A, #2196F3, #9C27B0)",borderRadius:"1rem",display:"block",height:"1rem",width:"1rem"},({filter:e})=>({filter:$n(e)}),({theme:e})=>({boxShadow:`${e.appBorderColor} 0 0 0 1px inset`})),Yl=E.span({display:"flex",flexDirection:"column"}),Vl=E.span({textTransform:"capitalize"}),ql=E.span(({theme:e})=>({fontSize:11,color:e.textMutedColor})),Kl=(e,t)=>[...e!==null?[{id:"reset",title:"Reset color filter",onClick:()=>{t(null)},right:void 0,active:!1}]:[],...zl.map(a=>{let n=a.percentage!==void 0?`${a.percentage}% of users`:void 0;return{id:a.name,title:o.createElement(Yl,null,o.createElement(Vl,null,a.name),n&&o.createElement(ql,null,n)),onClick:()=>{t(a)},right:o.createElement(jl,{filter:a.name}),active:e===a}})],Ql=()=>{let[e,t]=H(null);return o.createElement(o.Fragment,null,e&&o.createElement(_t,{styles:{[`#${Bl}`]:{filter:$n(e.name)}}}),o.createElement(de,{placement:"top",tooltip:({onHide:a})=>{let n=Kl(e,r=>{t(r),a()});return o.createElement(ke,{links:n})},closeOnOutsideClick:!0,onDoubleClick:()=>t(null)},o.createElement(ie,{key:"filter",active:!!e,title:"Vision simulator"},o.createElement(St,null))),o.createElement(Wl,null,o.createElement(Gl,null)))},Xl=()=>{let e=Re().getSelectedPanel(),[t]=De(B),a=t?.violations?.length||0,n=t?.incomplete?.length||0,r=a+n;return o.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},o.createElement("span",null,"Accessibility"),r===0?null:o.createElement(le,{compact:!0,status:e===Ve?"active":"neutral"},r))};xe.register(B,e=>{xe.add(Ve,{title:"",type:Pe.TOOL,match:({viewMode:t,tabId:a})=>t==="story"&&!a,render:()=>o.createElement(Ql,null)}),xe.add(Ve,{title:Xl,type:Pe.PANEL,render:({active:t=!0})=>o.createElement(nr,null,t?o.createElement(Ul,null):null),paramKey:ja})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
