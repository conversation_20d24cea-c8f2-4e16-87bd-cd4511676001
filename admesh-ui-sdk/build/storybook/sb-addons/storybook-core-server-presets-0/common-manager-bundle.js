try{
(()=>{var Oc=Object.defineProperty;var st=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var sr=(e,t)=>()=>(e&&(t=e(e=0)),t);var Tc=(e,t)=>{for(var r in t)Oc(e,r,{get:t[r],enumerable:!0})};var te=sr(()=>{});var re=sr(()=>{});var ne=sr(()=>{});var mo={};Tc(mo,{A:()=>Dc,ActionBar:()=>Mt,AddonPanel:()=>$t,Badge:()=>ct,Bar:()=>Ut,Blockquote:()=>Bc,Button:()=>ge,Checkbox:()=>_c,ClipboardCode:()=>Rc,Code:()=>Nc,DL:()=>Lc,Div:()=>Fc,DocumentWrapper:()=>Pc,EmptyTabContent:()=>Ht,ErrorFormatter:()=>jc,FlexBar:()=>mn,Form:()=>de,H1:()=>Mc,H2:()=>$c,H3:()=>Uc,H4:()=>Hc,H5:()=>Vc,H6:()=>zc,HR:()=>qc,IconButton:()=>K,Img:()=>Gc,LI:()=>Wc,Link:()=>Te,ListItem:()=>Kc,Loader:()=>Yc,Modal:()=>He,OL:()=>Jc,P:()=>fn,Placeholder:()=>Xc,Pre:()=>Zc,ProgressSpinner:()=>Qc,ResetWrapper:()=>hn,ScrollArea:()=>gn,Separator:()=>bn,Spaced:()=>ed,Span:()=>td,StorybookIcon:()=>rd,StorybookLogo:()=>nd,SyntaxHighlighter:()=>Vt,TT:()=>ad,TabBar:()=>od,TabButton:()=>ld,TabWrapper:()=>id,Table:()=>ud,Tabs:()=>sd,TabsState:()=>cd,TooltipLinkList:()=>zt,TooltipMessage:()=>dd,TooltipNote:()=>Ne,UL:()=>pd,WithTooltip:()=>oe,WithTooltipPure:()=>yn,Zoom:()=>En,codeCommon:()=>Xe,components:()=>md,createCopyToClipboardFunction:()=>fd,default:()=>Ic,getStoryHref:()=>hd,interleaveSeparators:()=>gd,nameSpaceClassNames:()=>bd,resetComponents:()=>yd,withReset:()=>Ze});var Ic,Dc,Mt,$t,ct,Ut,Bc,ge,_c,Rc,Nc,Lc,Fc,Pc,Ht,jc,mn,de,Mc,$c,Uc,Hc,Vc,zc,qc,K,Gc,Wc,Te,Kc,Yc,He,Jc,fn,Xc,Zc,Qc,hn,gn,bn,ed,td,rd,nd,Vt,ad,od,ld,id,ud,sd,cd,zt,dd,Ne,pd,oe,yn,En,Xe,md,fd,hd,gd,bd,yd,Ze,U=sr(()=>{te();re();ne();Ic=__STORYBOOK_COMPONENTS__,{A:Dc,ActionBar:Mt,AddonPanel:$t,Badge:ct,Bar:Ut,Blockquote:Bc,Button:ge,Checkbox:_c,ClipboardCode:Rc,Code:Nc,DL:Lc,Div:Fc,DocumentWrapper:Pc,EmptyTabContent:Ht,ErrorFormatter:jc,FlexBar:mn,Form:de,H1:Mc,H2:$c,H3:Uc,H4:Hc,H5:Vc,H6:zc,HR:qc,IconButton:K,Img:Gc,LI:Wc,Link:Te,ListItem:Kc,Loader:Yc,Modal:He,OL:Jc,P:fn,Placeholder:Xc,Pre:Zc,ProgressSpinner:Qc,ResetWrapper:hn,ScrollArea:gn,Separator:bn,Spaced:ed,Span:td,StorybookIcon:rd,StorybookLogo:nd,SyntaxHighlighter:Vt,TT:ad,TabBar:od,TabButton:ld,TabWrapper:id,Table:ud,Tabs:sd,TabsState:cd,TooltipLinkList:zt,TooltipMessage:dd,TooltipNote:Ne,UL:pd,WithTooltip:oe,WithTooltipPure:yn,Zoom:En,codeCommon:Xe,components:md,createCopyToClipboardFunction:fd,getStoryHref:hd,interleaveSeparators:gd,nameSpaceClassNames:bd,resetComponents:yd,withReset:Ze}=__STORYBOOK_COMPONENTS__});te();re();ne();te();re();ne();te();re();ne();var n=__REACT__,{Children:cr,Component:Re,Fragment:Oe,Profiler:hb,PureComponent:gb,StrictMode:bb,Suspense:so,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:yb,act:Eb,cloneElement:ae,createContext:Ct,createElement:P,createFactory:vb,createRef:xb,forwardRef:co,isValidElement:Ab,lazy:po,memo:he,startTransition:Cb,unstable_act:Sb,useCallback:H,useContext:dr,useDebugValue:wb,useDeferredValue:kb,useEffect:j,useId:Ob,useImperativeHandle:Tb,useInsertionEffect:Ib,useLayoutEffect:pr,useMemo:ce,useReducer:Db,useRef:Q,useState:R,useSyncExternalStore:Bb,useTransition:_b,version:Rb}=__REACT__;U();te();re();ne();var $b=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Ub,AccessibilityIcon:Hb,AccessibilityIgnoredIcon:Vb,AddIcon:fr,AdminIcon:zb,AlertAltIcon:qb,AlertIcon:Gb,AlignLeftIcon:Wb,AlignRightIcon:Kb,AppleIcon:Yb,ArrowBottomLeftIcon:Jb,ArrowBottomRightIcon:Xb,ArrowDownIcon:Zb,ArrowLeftIcon:Qb,ArrowRightIcon:e1,ArrowSolidDownIcon:t1,ArrowSolidLeftIcon:r1,ArrowSolidRightIcon:n1,ArrowSolidUpIcon:a1,ArrowTopLeftIcon:o1,ArrowTopRightIcon:l1,ArrowUpIcon:i1,AzureDevOpsIcon:u1,BackIcon:s1,BasketIcon:c1,BatchAcceptIcon:d1,BatchDenyIcon:p1,BeakerIcon:m1,BellIcon:f1,BitbucketIcon:h1,BoldIcon:g1,BookIcon:b1,BookmarkHollowIcon:y1,BookmarkIcon:E1,BottomBarIcon:v1,BottomBarToggleIcon:x1,BoxIcon:A1,BranchIcon:C1,BrowserIcon:fo,ButtonIcon:S1,CPUIcon:w1,CalendarIcon:k1,CameraIcon:O1,CameraStabilizeIcon:T1,CategoryIcon:I1,CertificateIcon:D1,ChangedIcon:B1,ChatIcon:_1,CheckIcon:hr,ChevronDownIcon:ho,ChevronLeftIcon:R1,ChevronRightIcon:go,ChevronSmallDownIcon:gr,ChevronSmallLeftIcon:N1,ChevronSmallRightIcon:L1,ChevronSmallUpIcon:bo,ChevronUpIcon:F1,ChromaticIcon:P1,ChromeIcon:j1,CircleHollowIcon:M1,CircleIcon:br,ClearIcon:$1,CloseAltIcon:U1,CloseIcon:H1,CloudHollowIcon:V1,CloudIcon:z1,CogIcon:q1,CollapseIcon:G1,CommandIcon:W1,CommentAddIcon:K1,CommentIcon:Y1,CommentsIcon:J1,CommitIcon:X1,CompassIcon:Z1,ComponentDrivenIcon:Q1,ComponentIcon:ey,ContrastIcon:ty,ContrastIgnoredIcon:ry,ControlsIcon:ny,CopyIcon:ay,CreditIcon:oy,CrossIcon:ly,DashboardIcon:iy,DatabaseIcon:uy,DeleteIcon:sy,DiamondIcon:cy,DirectionIcon:dy,DiscordIcon:py,DocChartIcon:my,DocListIcon:fy,DocumentIcon:dt,DownloadIcon:hy,DragIcon:gy,EditIcon:by,EllipsisIcon:yy,EmailIcon:Ey,ExpandAltIcon:vy,ExpandIcon:xy,EyeCloseIcon:yo,EyeIcon:Eo,FaceHappyIcon:Ay,FaceNeutralIcon:Cy,FaceSadIcon:Sy,FacebookIcon:wy,FailedIcon:vo,FastForwardIcon:xo,FigmaIcon:ky,FilterIcon:Oy,FlagIcon:Ty,FolderIcon:Iy,FormIcon:Dy,GDriveIcon:By,GithubIcon:_y,GitlabIcon:Ry,GlobeIcon:Ny,GoogleIcon:Ly,GraphBarIcon:Fy,GraphLineIcon:Py,GraphqlIcon:jy,GridAltIcon:My,GridIcon:Ao,GrowIcon:Co,HeartHollowIcon:$y,HeartIcon:Uy,HomeIcon:Hy,HourglassIcon:Vy,InfoIcon:zy,ItalicIcon:qy,JumpToIcon:Gy,KeyIcon:Wy,LightningIcon:Ky,LightningOffIcon:Yy,LinkBrokenIcon:Jy,LinkIcon:Xy,LinkedinIcon:Zy,LinuxIcon:Qy,ListOrderedIcon:eE,ListUnorderedIcon:So,LocationIcon:tE,LockIcon:rE,MarkdownIcon:nE,MarkupIcon:wo,MediumIcon:aE,MemoryIcon:oE,MenuIcon:lE,MergeIcon:iE,MirrorIcon:uE,MobileIcon:ko,MoonIcon:sE,NutIcon:cE,OutboxIcon:dE,OutlineIcon:Oo,PaintBrushIcon:pE,PaperClipIcon:mE,ParagraphIcon:fE,PassedIcon:vn,PhoneIcon:hE,PhotoDragIcon:gE,PhotoIcon:To,PhotoStabilizeIcon:bE,PinAltIcon:yE,PinIcon:EE,PlayAllHollowIcon:vE,PlayBackIcon:Io,PlayHollowIcon:xE,PlayIcon:Do,PlayNextIcon:Bo,PlusIcon:AE,PointerDefaultIcon:CE,PointerHandIcon:SE,PowerIcon:wE,PrintIcon:kE,ProceedIcon:OE,ProfileIcon:TE,PullRequestIcon:IE,QuestionIcon:DE,RSSIcon:BE,RedirectIcon:_E,ReduxIcon:RE,RefreshIcon:yr,ReplyIcon:NE,RepoIcon:LE,RequestChangeIcon:FE,RewindIcon:_o,RulerIcon:Ro,SaveIcon:PE,SearchIcon:jE,ShareAltIcon:ME,ShareIcon:$E,ShieldIcon:UE,SideBySideIcon:HE,SidebarAltIcon:VE,SidebarAltToggleIcon:zE,SidebarIcon:qE,SidebarToggleIcon:GE,SpeakerIcon:WE,StackedIcon:KE,StarHollowIcon:YE,StarIcon:JE,StatusFailIcon:XE,StatusIcon:ZE,StatusPassIcon:QE,StatusWarnIcon:ev,StickerIcon:tv,StopAltHollowIcon:rv,StopAltIcon:No,StopIcon:nv,StorybookIcon:av,StructureIcon:ov,SubtractIcon:Lo,SunIcon:lv,SupportIcon:iv,SweepIcon:uv,SwitchAltIcon:sv,SyncIcon:Fo,TabletIcon:Po,ThumbsUpIcon:cv,TimeIcon:dv,TimerIcon:pv,TransferIcon:jo,TrashIcon:mv,TwitterIcon:fv,TypeIcon:hv,UbuntuIcon:gv,UndoIcon:Er,UnfoldIcon:bv,UnlockIcon:yv,UnpinIcon:Ev,UploadIcon:vv,UserAddIcon:xv,UserAltIcon:Av,UserIcon:Cv,UsersIcon:Sv,VSCodeIcon:wv,VerifiedIcon:kv,VideoIcon:Ov,WandIcon:Tv,WatchIcon:Iv,WindowsIcon:Dv,WrenchIcon:Bv,XIcon:_v,YoutubeIcon:Rv,ZoomIcon:Mo,ZoomOutIcon:$o,ZoomResetIcon:Uo,iconList:Nv}=__STORYBOOK_ICONS__;te();re();ne();var Mv=__STORYBOOK_THEMING__,{CacheProvider:$v,ClassNames:Uv,Global:Ho,ThemeProvider:Vo,background:Hv,color:vr,convert:zo,create:Vv,createCache:zv,createGlobal:qv,createReset:Gv,css:Wv,darken:Kv,ensure:Yv,ignoreSsrWarning:qo,isPropValid:Jv,jsx:Xv,keyframes:xn,lighten:Zv,styled:y,themes:An,typography:Le,useTheme:Ie,withTheme:Go}=__STORYBOOK_THEMING__;te();re();ne();var Qe=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})();te();re();ne();var ix=__STORYBOOK_API__,{ActiveTabs:ux,Consumer:Wo,ManagerContext:sx,Provider:cx,RequestResponseError:dx,addons:ee,combineParameters:px,controlOrMetaKey:mx,controlOrMetaSymbol:fx,eventMatchesShortcut:hx,eventToShortcut:gx,experimental_MockUniversalStore:bx,experimental_UniversalStore:yx,experimental_getStatusStore:Ex,experimental_getTestProviderStore:vx,experimental_requestResponse:Cn,experimental_useStatusStore:Ko,experimental_useTestProviderStore:xx,experimental_useUniversalStore:Ax,internal_fullStatusStore:Cx,internal_fullTestProviderStore:Sx,internal_universalStatusStore:wx,internal_universalTestProviderStore:kx,isMacLike:Ox,isShortcutTaken:Tx,keyToSymbol:Ix,merge:Dx,mockChannel:Bx,optionOrAltSymbol:_x,shortcutMatchesShortcut:Rx,shortcutToHumanString:Nx,types:be,useAddonState:St,useArgTypes:xr,useArgs:Yo,useChannel:Ar,useGlobalTypes:Lx,useGlobals:Ve,useParameter:et,useSharedState:Fx,useStoryPrepared:Px,useStorybookApi:ye,useStorybookState:Jo}=__STORYBOOK_API__;U();te();re();ne();var Hx=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:Vx,ARGTYPES_INFO_RESPONSE:zx,CHANNEL_CREATED:qx,CHANNEL_WS_DISCONNECT:Gx,CONFIG_ERROR:Wx,CREATE_NEW_STORYFILE_REQUEST:Kx,CREATE_NEW_STORYFILE_RESPONSE:Yx,CURRENT_STORY_WAS_SET:Jx,DOCS_PREPARED:Xx,DOCS_RENDERED:Zx,FILE_COMPONENT_SEARCH_REQUEST:Qx,FILE_COMPONENT_SEARCH_RESPONSE:eA,FORCE_REMOUNT:Xo,FORCE_RE_RENDER:tA,GLOBALS_UPDATED:rA,NAVIGATE_URL:nA,PLAY_FUNCTION_THREW_EXCEPTION:Zo,PRELOAD_ENTRIES:aA,PREVIEW_BUILDER_PROGRESS:oA,PREVIEW_KEYDOWN:lA,REGISTER_SUBSCRIPTION:iA,REQUEST_WHATS_NEW_DATA:uA,RESET_STORY_ARGS:sA,RESULT_WHATS_NEW_DATA:cA,SAVE_STORY_REQUEST:Sn,SAVE_STORY_RESPONSE:Cr,SELECT_STORY:dA,SET_CONFIG:pA,SET_CURRENT_STORY:mA,SET_FILTER:fA,SET_GLOBALS:hA,SET_INDEX:gA,SET_STORIES:bA,SET_WHATS_NEW_CACHE:yA,SHARED_STATE_CHANGED:EA,SHARED_STATE_SET:vA,STORIES_COLLAPSE_ALL:xA,STORIES_EXPAND_ALL:AA,STORY_ARGS_UPDATED:CA,STORY_CHANGED:qt,STORY_ERRORED:SA,STORY_FINISHED:wA,STORY_HOT_UPDATED:kA,STORY_INDEX_INVALIDATED:OA,STORY_MISSING:TA,STORY_PREPARED:IA,STORY_RENDERED:DA,STORY_RENDER_PHASE_CHANGED:Qo,STORY_SPECIFIED:BA,STORY_THREW_EXCEPTION:el,STORY_UNCHANGED:_A,TELEMETRY_ERROR:RA,TOGGLE_WHATS_NEW_NOTIFICATIONS:NA,UNHANDLED_ERRORS_WHILE_PLAYING:tl,UPDATE_GLOBALS:LA,UPDATE_QUERY_PARAMS:FA,UPDATE_STORY_ARGS:PA}=__STORYBOOK_CORE_EVENTS__;te();re();ne();var HA=__STORYBOOK_CLIENT_LOGGER__,{deprecate:VA,logger:tt,once:rl,pretty:zA}=__STORYBOOK_CLIENT_LOGGER__;U();te();re();ne();var Ed=Object.create,kn=Object.defineProperty,vd=Object.getOwnPropertyDescriptor,xd=Object.getOwnPropertyNames,Ad=Object.getPrototypeOf,Cd=Object.prototype.hasOwnProperty,pe=(e,t)=>kn(e,"name",{value:t,configurable:!0}),Sd=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),wd=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of xd(t))!Cd.call(e,o)&&o!==r&&kn(e,o,{get:()=>t[o],enumerable:!(a=vd(t,o))||a.enumerable});return e},kd=(e,t,r)=>(r=e!=null?Ed(Ad(e)):{},wd(t||!e||!e.__esModule?kn(r,"default",{value:e,enumerable:!0}):r,e)),Od=Sd(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isEqual=function(){var t=Object.prototype.toString,r=Object.getPrototypeOf,a=Object.getOwnPropertySymbols?function(o){return Object.keys(o).concat(Object.getOwnPropertySymbols(o))}:Object.keys;return function(o,c){return pe(function l(u,s,d){var m,f,p,h=t.call(u),g=t.call(s);if(u===s)return!0;if(u==null||s==null)return!1;if(d.indexOf(u)>-1&&d.indexOf(s)>-1)return!0;if(d.push(u,s),h!=g||(m=a(u),f=a(s),m.length!=f.length||m.some(function(b){return!l(u[b],s[b],d)})))return!1;switch(h.slice(8,-1)){case"Symbol":return u.valueOf()==s.valueOf();case"Date":case"Number":return+u==+s||+u!=+u&&+s!=+s;case"RegExp":case"Function":case"String":case"Boolean":return""+u==""+s;case"Set":case"Map":m=u.entries(),f=s.entries();do if(!l((p=m.next()).value,f.next().value,d))return!1;while(!p.done);return!0;case"ArrayBuffer":u=new Uint8Array(u),s=new Uint8Array(s);case"DataView":u=new Uint8Array(u.buffer),s=new Uint8Array(s.buffer);case"Float32Array":case"Float64Array":case"Int8Array":case"Int16Array":case"Int32Array":case"Uint8Array":case"Uint16Array":case"Uint32Array":case"Uint8ClampedArray":case"Arguments":case"Array":if(u.length!=s.length)return!1;for(p=0;p<u.length;p++)if((p in u||p in s)&&(p in u!=p in s||!l(u[p],s[p],d)))return!1;return!0;case"Object":return l(r(u),r(s),d);default:return!1}},"n")(o,c,[])}}()});function ol(e){return e.replace(/_/g," ").replace(/-/g," ").replace(/\./g," ").replace(/([^\n])([A-Z])([a-z])/g,(t,r,a,o)=>`${r} ${a}${o}`).replace(/([a-z])([A-Z])/g,(t,r,a)=>`${r} ${a}`).replace(/([a-z])([0-9])/gi,(t,r,a)=>`${r} ${a}`).replace(/([0-9])([a-z])/gi,(t,r,a)=>`${r} ${a}`).replace(/(\s|^)(\w)/g,(t,r,a)=>`${r}${a.toUpperCase()}`).replace(/ +/g," ").trim()}pe(ol,"toStartCaseStr");var nl=kd(Od(),1),ll=pe(e=>e.map(t=>typeof t<"u").filter(Boolean).length,"count"),Td=pe((e,t)=>{let{exists:r,eq:a,neq:o,truthy:c}=e;if(ll([r,a,o,c])>1)throw new Error(`Invalid conditional test ${JSON.stringify({exists:r,eq:a,neq:o})}`);if(typeof a<"u")return(0,nl.isEqual)(t,a);if(typeof o<"u")return!(0,nl.isEqual)(t,o);if(typeof r<"u"){let l=typeof t<"u";return r?l:!l}return typeof c>"u"||c?!!t:!t},"testValue"),il=pe((e,t,r)=>{if(!e.if)return!0;let{arg:a,global:o}=e.if;if(ll([a,o])!==1)throw new Error(`Invalid conditional value ${JSON.stringify({arg:a,global:o})}`);let c=a?t[a]:r[o];return Td(e.if,c)},"includeConditionalArg");function Id(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Preview"}pe(Id,"isPreview");function Dd(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Meta"}pe(Dd,"isMeta");function Bd(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Story"}pe(Bd,"isStory");var _d=pe(e=>e.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),"sanitize"),al=pe((e,t)=>{let r=_d(e);if(r==="")throw new Error(`Invalid ${t} '${e}', must include alphanumeric characters`);return r},"sanitizeSafe"),YA=pe((e,t)=>`${al(e,"kind")}${t?`--${al(t,"name")}`:""}`,"toId"),JA=pe(e=>ol(e),"storyNameFromExport");function wn(e,t){return Array.isArray(t)?t.includes(e):e.match(t)}pe(wn,"matches");function Rd(e,{includeStories:t,excludeStories:r}){return e!=="__esModule"&&(!t||wn(e,t))&&(!r||!wn(e,r))}pe(Rd,"isExportStory");var XA=pe((e,{rootSeparator:t,groupSeparator:r})=>{let[a,o]=e.split(t,2),c=(o||e).split(r).filter(l=>!!l);return{root:o?a:null,groups:c}},"parseKind"),ZA=pe((...e)=>{let t=e.reduce((r,a)=>(a.startsWith("!")?r.delete(a.slice(1)):r.add(a),r),new Set);return Array.from(t)},"combineTags");U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();var Nd=Object.create,rn=Object.defineProperty,Ld=Object.getOwnPropertyDescriptor,Fd=Object.getOwnPropertyNames,Pd=Object.getPrototypeOf,jd=Object.prototype.hasOwnProperty,i=(e,t)=>rn(e,"name",{value:t,configurable:!0}),Sr=(e=>typeof st<"u"?st:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof st<"u"?st:t)[r]}):e)(function(e){if(typeof st<"u")return st.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),G=(e,t)=>()=>(e&&(t=e(e=0)),t),X=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Md=(e,t)=>{for(var r in t)rn(e,r,{get:t[r],enumerable:!0})},$d=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Fd(t))!jd.call(e,o)&&o!==r&&rn(e,o,{get:()=>t[o],enumerable:!(a=Ld(t,o))||a.enumerable});return e},Ae=(e,t,r)=>(r=e!=null?Nd(Pd(e)):{},$d(t||!e||!e.__esModule?rn(r,"default",{value:e,enumerable:!0}):r,e));function Oa(e){return typeof e=="symbol"||e instanceof Symbol}var ki=G(()=>{i(Oa,"isSymbol")});function Oi(e){return Oa(e)?NaN:Number(e)}var Ud=G(()=>{ki(),i(Oi,"toNumber")});function Ti(e){return e?(e=Oi(e),e===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e===e?e:0):e===0?e:0}var Hd=G(()=>{Ud(),i(Ti,"toFinite")});function Ii(e){let t=Ti(e),r=t%1;return r?t-r:t}var Vd=G(()=>{Hd(),i(Ii,"toInteger")});function Di(e){return Array.from(new Set(e))}var zd=G(()=>{i(Di,"uniq")});function Bi(e){return e==null||typeof e!="object"&&typeof e!="function"}var qd=G(()=>{i(Bi,"isPrimitive")});function Ta(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}var _i=G(()=>{i(Ta,"isTypedArray")});function Ia(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}var Ri=G(()=>{i(Ia,"getSymbols")});function Ni(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var Gd=G(()=>{i(Ni,"getTag")}),Li,Da,Ba,_a,Ra,Fi,Pi,ji,Mi,$i,Ui,Hi,Vi,zi,qi,Gi,Wi,Ki,Yi,Ji,Xi,Zi,Qi=G(()=>{Li="[object RegExp]",Da="[object String]",Ba="[object Number]",_a="[object Boolean]",Ra="[object Arguments]",Fi="[object Symbol]",Pi="[object Date]",ji="[object Map]",Mi="[object Set]",$i="[object Array]",Ui="[object ArrayBuffer]",Hi="[object Object]",Vi="[object DataView]",zi="[object Uint8Array]",qi="[object Uint8ClampedArray]",Gi="[object Uint16Array]",Wi="[object Uint32Array]",Ki="[object Int8Array]",Yi="[object Int16Array]",Ji="[object Int32Array]",Xi="[object Float32Array]",Zi="[object Float64Array]"});function eu(e,t){return gt(e,void 0,e,new Map,t)}function gt(e,t,r,a=new Map,o=void 0){let c=o?.(e,t,r,a);if(c!=null)return c;if(Bi(e))return e;if(a.has(e))return a.get(e);if(Array.isArray(e)){let l=new Array(e.length);a.set(e,l);for(let u=0;u<e.length;u++)l[u]=gt(e[u],u,r,a,o);return Object.hasOwn(e,"index")&&(l.index=e.index),Object.hasOwn(e,"input")&&(l.input=e.input),l}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let l=new RegExp(e.source,e.flags);return l.lastIndex=e.lastIndex,l}if(e instanceof Map){let l=new Map;a.set(e,l);for(let[u,s]of e)l.set(u,gt(s,u,r,a,o));return l}if(e instanceof Set){let l=new Set;a.set(e,l);for(let u of e)l.add(gt(u,void 0,r,a,o));return l}if(typeof Buffer<"u"&&Buffer.isBuffer(e))return e.subarray();if(Ta(e)){let l=new(Object.getPrototypeOf(e)).constructor(e.length);a.set(e,l);for(let u=0;u<e.length;u++)l[u]=gt(e[u],u,r,a,o);return l}if(e instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let l=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return a.set(e,l),ot(l,e,r,a,o),l}if(typeof File<"u"&&e instanceof File){let l=new File([e],e.name,{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Blob){let l=new Blob([e],{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Error){let l=new e.constructor;return a.set(e,l),l.message=e.message,l.name=e.name,l.stack=e.stack,l.cause=e.cause,ot(l,e,r,a,o),l}if(typeof e=="object"&&tu(e)){let l=Object.create(Object.getPrototypeOf(e));return a.set(e,l),ot(l,e,r,a,o),l}return e}function ot(e,t,r=e,a,o){let c=[...Object.keys(t),...Ia(t)];for(let l=0;l<c.length;l++){let u=c[l],s=Object.getOwnPropertyDescriptor(e,u);(s==null||s.writable)&&(e[u]=gt(t[u],u,r,a,o))}}function tu(e){switch(Ni(e)){case Ra:case $i:case Ui:case Vi:case _a:case Pi:case Xi:case Zi:case Ki:case Yi:case Ji:case ji:case Ba:case Hi:case Li:case Mi:case Da:case Fi:case zi:case qi:case Gi:case Wi:return!0;default:return!1}}var Wd=G(()=>{Ri(),Gd(),Qi(),qd(),_i(),i(eu,"cloneDeepWith"),i(gt,"cloneDeepWithImpl"),i(ot,"copyProperties"),i(tu,"isCloneableObject")});function ru(e){return Number.isSafeInteger(e)&&e>=0}var Kd=G(()=>{i(ru,"isLength")});function nn(e){return e!=null&&typeof e!="function"&&ru(e.length)}var Na=G(()=>{Kd(),i(nn,"isArrayLike")});function nu(e,t){return eu(e,(r,a,o,c)=>{let l=t?.(r,a,o,c);if(l!=null)return l;if(typeof e=="object")switch(Object.prototype.toString.call(e)){case Ba:case Da:case _a:{let u=new e.constructor(e?.valueOf());return ot(u,e),u}case Ra:{let u={};return ot(u,e),u.length=e.length,u[Symbol.iterator]=e[Symbol.iterator],u}default:return}})}var Yd=G(()=>{Wd(),Qi(),i(nu,"cloneDeepWith")});function au(e){return nu(e)}var Jd=G(()=>{Yd(),i(au,"cloneDeep")});function ou(e,t,r=1){if(t==null&&(t=e,e=0),!Number.isInteger(r)||r===0)throw new Error("The step value must be a non-zero integer.");let a=Math.max(Math.ceil((t-e)/r),0),o=new Array(a);for(let c=0;c<a;c++)o[c]=e+c*r;return o}var Xd=G(()=>{i(ou,"range")});function lu(e){return nn(e)?Di(Array.from(e)):[]}var Zd=G(()=>{zd(),Na(),i(lu,"uniq")});function iu(e,t,{signal:r,edges:a}={}){let o,c=null,l=a!=null&&a.includes("leading"),u=a==null||a.includes("trailing"),s=i(()=>{c!==null&&(e.apply(o,c),o=void 0,c=null)},"invoke"),d=i(()=>{u&&s(),h()},"onTimerEnd"),m=null,f=i(()=>{m!=null&&clearTimeout(m),m=setTimeout(()=>{m=null,d()},t)},"schedule"),p=i(()=>{m!==null&&(clearTimeout(m),m=null)},"cancelTimer"),h=i(()=>{p(),o=void 0,c=null},"cancel"),g=i(()=>{p(),s()},"flush"),b=i(function(...E){if(r?.aborted)return;o=this,c=E;let v=m==null;f(),l&&v&&s()},"debounced");return b.schedule=f,b.cancel=h,b.flush=g,r?.addEventListener("abort",h,{once:!0}),b}var Qd=G(()=>{i(iu,"debounce")});function uu(e,t=0,r={}){typeof r!="object"&&(r={});let{signal:a,leading:o=!1,trailing:c=!0,maxWait:l}=r,u=Array(2);o&&(u[0]="leading"),c&&(u[1]="trailing");let s,d=null,m=iu(function(...h){s=e.apply(this,h),d=null},t,{signal:a,edges:u}),f=i(function(...h){if(l!=null){if(d===null)d=Date.now();else if(Date.now()-d>=l)return s=e.apply(this,h),d=Date.now(),m.cancel(),m.schedule(),s}return m.apply(this,h),s},"debounced"),p=i(()=>(m.flush(),s),"flush");return f.cancel=m.cancel,f.flush=p,f}var ep=G(()=>{Qd(),i(uu,"debounce")});function su(e){return typeof Buffer<"u"&&Buffer.isBuffer(e)}var tp=G(()=>{i(su,"isBuffer")});function cu(e){let t=e?.constructor,r=typeof t=="function"?t.prototype:Object.prototype;return e===r}var rp=G(()=>{i(cu,"isPrototype")});function du(e){return Ta(e)}var np=G(()=>{_i(),i(du,"isTypedArray")});function pu(e,t){if(e=Ii(e),e<1||!Number.isSafeInteger(e))return[];let r=new Array(e);for(let a=0;a<e;a++)r[a]=typeof t=="function"?t(a):a;return r}var ap=G(()=>{Vd(),i(pu,"times")});function mu(e){if(e==null)return[];switch(typeof e){case"object":case"function":return nn(e)?hu(e):cu(e)?fu(e):nr(e);default:return nr(Object(e))}}function nr(e){let t=[];for(let r in e)t.push(r);return t}function fu(e){return nr(e).filter(t=>t!=="constructor")}function hu(e){let t=pu(e.length,a=>`${a}`),r=new Set(t);return su(e)&&(r.add("offset"),r.add("parent")),du(e)&&(r.add("buffer"),r.add("byteLength"),r.add("byteOffset")),[...t,...nr(e).filter(a=>!r.has(a))]}var op=G(()=>{tp(),rp(),Na(),np(),ap(),i(mu,"keysIn"),i(nr,"keysInImpl"),i(fu,"prototypeKeysIn"),i(hu,"arrayLikeKeysIn")});function gu(e){let t=[];for(;e;)t.push(...Ia(e)),e=Object.getPrototypeOf(e);return t}var lp=G(()=>{Ri(),i(gu,"getSymbolsIn")});function bu(e,t){if(e==null)return{};let r={};if(t==null)return e;let a=nn(e)?ou(0,e.length):[...mu(e),...gu(e)];for(let o=0;o<a.length;o++){let c=Oa(a[o])?a[o]:a[o].toString(),l=e[c];t(l,c,e)&&(r[c]=l)}return r}var ip=G(()=>{op(),Xd(),lp(),Na(),ki(),i(bu,"pickBy")}),an=G(()=>{Zd(),ep(),Jd(),ip()}),ke,or,je=G(()=>{"use strict";ke=i(e=>`control-${e.replace(/\s+/g,"-")}`,"getControlId"),or=i(e=>`set-${e.replace(/\s+/g,"-")}`,"getControlSetterButtonId")}),up=X((e,t)=>{"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}),yu=X((e,t)=>{var r=up(),a={};for(let l of Object.keys(r))a[r[l]]=l;var o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};t.exports=o;for(let l of Object.keys(o)){if(!("channels"in o[l]))throw new Error("missing channels property: "+l);if(!("labels"in o[l]))throw new Error("missing channel labels property: "+l);if(o[l].labels.length!==o[l].channels)throw new Error("channel and label counts mismatch: "+l);let{channels:u,labels:s}=o[l];delete o[l].channels,delete o[l].labels,Object.defineProperty(o[l],"channels",{value:u}),Object.defineProperty(o[l],"labels",{value:s})}o.rgb.hsl=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.min(u,s,d),f=Math.max(u,s,d),p=f-m,h,g;f===m?h=0:u===f?h=(s-d)/p:s===f?h=2+(d-u)/p:d===f&&(h=4+(u-s)/p),h=Math.min(h*60,360),h<0&&(h+=360);let b=(m+f)/2;return f===m?g=0:b<=.5?g=p/(f+m):g=p/(2-f-m),[h,g*100,b*100]},o.rgb.hsv=function(l){let u,s,d,m,f,p=l[0]/255,h=l[1]/255,g=l[2]/255,b=Math.max(p,h,g),E=b-Math.min(p,h,g),v=i(function(A){return(b-A)/6/E+1/2},"diffc");return E===0?(m=0,f=0):(f=E/b,u=v(p),s=v(h),d=v(g),p===b?m=d-s:h===b?m=1/3+u-d:g===b&&(m=2/3+s-u),m<0?m+=1:m>1&&(m-=1)),[m*360,f*100,b*100]},o.rgb.hwb=function(l){let u=l[0],s=l[1],d=l[2],m=o.rgb.hsl(l)[0],f=1/255*Math.min(u,Math.min(s,d));return d=1-1/255*Math.max(u,Math.max(s,d)),[m,f*100,d*100]},o.rgb.cmyk=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.min(1-u,1-s,1-d),f=(1-u-m)/(1-m)||0,p=(1-s-m)/(1-m)||0,h=(1-d-m)/(1-m)||0;return[f*100,p*100,h*100,m*100]};function c(l,u){return(l[0]-u[0])**2+(l[1]-u[1])**2+(l[2]-u[2])**2}i(c,"comparativeDistance"),o.rgb.keyword=function(l){let u=a[l];if(u)return u;let s=1/0,d;for(let m of Object.keys(r)){let f=r[m],p=c(l,f);p<s&&(s=p,d=m)}return d},o.keyword.rgb=function(l){return r[l]},o.rgb.xyz=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255;u=u>.04045?((u+.055)/1.055)**2.4:u/12.92,s=s>.04045?((s+.055)/1.055)**2.4:s/12.92,d=d>.04045?((d+.055)/1.055)**2.4:d/12.92;let m=u*.4124+s*.3576+d*.1805,f=u*.2126+s*.7152+d*.0722,p=u*.0193+s*.1192+d*.9505;return[m*100,f*100,p*100]},o.rgb.lab=function(l){let u=o.rgb.xyz(l),s=u[0],d=u[1],m=u[2];s/=95.047,d/=100,m/=108.883,s=s>.008856?s**(1/3):7.787*s+16/116,d=d>.008856?d**(1/3):7.787*d+16/116,m=m>.008856?m**(1/3):7.787*m+16/116;let f=116*d-16,p=500*(s-d),h=200*(d-m);return[f,p,h]},o.hsl.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100,m,f,p;if(s===0)return p=d*255,[p,p,p];d<.5?m=d*(1+s):m=d+s-d*s;let h=2*d-m,g=[0,0,0];for(let b=0;b<3;b++)f=u+1/3*-(b-1),f<0&&f++,f>1&&f--,6*f<1?p=h+(m-h)*6*f:2*f<1?p=m:3*f<2?p=h+(m-h)*(2/3-f)*6:p=h,g[b]=p*255;return g},o.hsl.hsv=function(l){let u=l[0],s=l[1]/100,d=l[2]/100,m=s,f=Math.max(d,.01);d*=2,s*=d<=1?d:2-d,m*=f<=1?f:2-f;let p=(d+s)/2,h=d===0?2*m/(f+m):2*s/(d+s);return[u,h*100,p*100]},o.hsv.rgb=function(l){let u=l[0]/60,s=l[1]/100,d=l[2]/100,m=Math.floor(u)%6,f=u-Math.floor(u),p=255*d*(1-s),h=255*d*(1-s*f),g=255*d*(1-s*(1-f));switch(d*=255,m){case 0:return[d,g,p];case 1:return[h,d,p];case 2:return[p,d,g];case 3:return[p,h,d];case 4:return[g,p,d];case 5:return[d,p,h]}},o.hsv.hsl=function(l){let u=l[0],s=l[1]/100,d=l[2]/100,m=Math.max(d,.01),f,p;p=(2-s)*d;let h=(2-s)*m;return f=s*m,f/=h<=1?h:2-h,f=f||0,p/=2,[u,f*100,p*100]},o.hwb.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100,m=s+d,f;m>1&&(s/=m,d/=m);let p=Math.floor(6*u),h=1-d;f=6*u-p,(p&1)!==0&&(f=1-f);let g=s+f*(h-s),b,E,v;switch(p){default:case 6:case 0:b=h,E=g,v=s;break;case 1:b=g,E=h,v=s;break;case 2:b=s,E=h,v=g;break;case 3:b=s,E=g,v=h;break;case 4:b=g,E=s,v=h;break;case 5:b=h,E=s,v=g;break}return[b*255,E*255,v*255]},o.cmyk.rgb=function(l){let u=l[0]/100,s=l[1]/100,d=l[2]/100,m=l[3]/100,f=1-Math.min(1,u*(1-m)+m),p=1-Math.min(1,s*(1-m)+m),h=1-Math.min(1,d*(1-m)+m);return[f*255,p*255,h*255]},o.xyz.rgb=function(l){let u=l[0]/100,s=l[1]/100,d=l[2]/100,m,f,p;return m=u*3.2406+s*-1.5372+d*-.4986,f=u*-.9689+s*1.8758+d*.0415,p=u*.0557+s*-.204+d*1.057,m=m>.0031308?1.055*m**(1/2.4)-.055:m*12.92,f=f>.0031308?1.055*f**(1/2.4)-.055:f*12.92,p=p>.0031308?1.055*p**(1/2.4)-.055:p*12.92,m=Math.min(Math.max(0,m),1),f=Math.min(Math.max(0,f),1),p=Math.min(Math.max(0,p),1),[m*255,f*255,p*255]},o.xyz.lab=function(l){let u=l[0],s=l[1],d=l[2];u/=95.047,s/=100,d/=108.883,u=u>.008856?u**(1/3):7.787*u+16/116,s=s>.008856?s**(1/3):7.787*s+16/116,d=d>.008856?d**(1/3):7.787*d+16/116;let m=116*s-16,f=500*(u-s),p=200*(s-d);return[m,f,p]},o.lab.xyz=function(l){let u=l[0],s=l[1],d=l[2],m,f,p;f=(u+16)/116,m=s/500+f,p=f-d/200;let h=f**3,g=m**3,b=p**3;return f=h>.008856?h:(f-16/116)/7.787,m=g>.008856?g:(m-16/116)/7.787,p=b>.008856?b:(p-16/116)/7.787,m*=95.047,f*=100,p*=108.883,[m,f,p]},o.lab.lch=function(l){let u=l[0],s=l[1],d=l[2],m;m=Math.atan2(d,s)*360/2/Math.PI,m<0&&(m+=360);let f=Math.sqrt(s*s+d*d);return[u,f,m]},o.lch.lab=function(l){let u=l[0],s=l[1],d=l[2]/360*2*Math.PI,m=s*Math.cos(d),f=s*Math.sin(d);return[u,m,f]},o.rgb.ansi16=function(l,u=null){let[s,d,m]=l,f=u===null?o.rgb.hsv(l)[2]:u;if(f=Math.round(f/50),f===0)return 30;let p=30+(Math.round(m/255)<<2|Math.round(d/255)<<1|Math.round(s/255));return f===2&&(p+=60),p},o.hsv.ansi16=function(l){return o.rgb.ansi16(o.hsv.rgb(l),l[2])},o.rgb.ansi256=function(l){let u=l[0],s=l[1],d=l[2];return u===s&&s===d?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(s/255*5)+Math.round(d/255*5)},o.ansi16.rgb=function(l){let u=l%10;if(u===0||u===7)return l>50&&(u+=3.5),u=u/10.5*255,[u,u,u];let s=(~~(l>50)+1)*.5,d=(u&1)*s*255,m=(u>>1&1)*s*255,f=(u>>2&1)*s*255;return[d,m,f]},o.ansi256.rgb=function(l){if(l>=232){let f=(l-232)*10+8;return[f,f,f]}l-=16;let u,s=Math.floor(l/36)/5*255,d=Math.floor((u=l%36)/6)/5*255,m=u%6/5*255;return[s,d,m]},o.rgb.hex=function(l){let u=(((Math.round(l[0])&255)<<16)+((Math.round(l[1])&255)<<8)+(Math.round(l[2])&255)).toString(16).toUpperCase();return"000000".substring(u.length)+u},o.hex.rgb=function(l){let u=l.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!u)return[0,0,0];let s=u[0];u[0].length===3&&(s=s.split("").map(h=>h+h).join(""));let d=parseInt(s,16),m=d>>16&255,f=d>>8&255,p=d&255;return[m,f,p]},o.rgb.hcg=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.max(Math.max(u,s),d),f=Math.min(Math.min(u,s),d),p=m-f,h,g;return p<1?h=f/(1-p):h=0,p<=0?g=0:m===u?g=(s-d)/p%6:m===s?g=2+(d-u)/p:g=4+(u-s)/p,g/=6,g%=1,[g*360,p*100,h*100]},o.hsl.hcg=function(l){let u=l[1]/100,s=l[2]/100,d=s<.5?2*u*s:2*u*(1-s),m=0;return d<1&&(m=(s-.5*d)/(1-d)),[l[0],d*100,m*100]},o.hsv.hcg=function(l){let u=l[1]/100,s=l[2]/100,d=u*s,m=0;return d<1&&(m=(s-d)/(1-d)),[l[0],d*100,m*100]},o.hcg.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100;if(s===0)return[d*255,d*255,d*255];let m=[0,0,0],f=u%1*6,p=f%1,h=1-p,g=0;switch(Math.floor(f)){case 0:m[0]=1,m[1]=p,m[2]=0;break;case 1:m[0]=h,m[1]=1,m[2]=0;break;case 2:m[0]=0,m[1]=1,m[2]=p;break;case 3:m[0]=0,m[1]=h,m[2]=1;break;case 4:m[0]=p,m[1]=0,m[2]=1;break;default:m[0]=1,m[1]=0,m[2]=h}return g=(1-s)*d,[(s*m[0]+g)*255,(s*m[1]+g)*255,(s*m[2]+g)*255]},o.hcg.hsv=function(l){let u=l[1]/100,s=l[2]/100,d=u+s*(1-u),m=0;return d>0&&(m=u/d),[l[0],m*100,d*100]},o.hcg.hsl=function(l){let u=l[1]/100,s=l[2]/100*(1-u)+.5*u,d=0;return s>0&&s<.5?d=u/(2*s):s>=.5&&s<1&&(d=u/(2*(1-s))),[l[0],d*100,s*100]},o.hcg.hwb=function(l){let u=l[1]/100,s=l[2]/100,d=u+s*(1-u);return[l[0],(d-u)*100,(1-d)*100]},o.hwb.hcg=function(l){let u=l[1]/100,s=1-l[2]/100,d=s-u,m=0;return d<1&&(m=(s-d)/(1-d)),[l[0],d*100,m*100]},o.apple.rgb=function(l){return[l[0]/65535*255,l[1]/65535*255,l[2]/65535*255]},o.rgb.apple=function(l){return[l[0]/255*65535,l[1]/255*65535,l[2]/255*65535]},o.gray.rgb=function(l){return[l[0]/100*255,l[0]/100*255,l[0]/100*255]},o.gray.hsl=function(l){return[0,0,l[0]]},o.gray.hsv=o.gray.hsl,o.gray.hwb=function(l){return[0,100,l[0]]},o.gray.cmyk=function(l){return[0,0,0,l[0]]},o.gray.lab=function(l){return[l[0],0,0]},o.gray.hex=function(l){let u=Math.round(l[0]/100*255)&255,s=((u<<16)+(u<<8)+u).toString(16).toUpperCase();return"000000".substring(s.length)+s},o.rgb.gray=function(l){return[(l[0]+l[1]+l[2])/3/255*100]}}),sp=X((e,t)=>{var r=yu();function a(){let u={},s=Object.keys(r);for(let d=s.length,m=0;m<d;m++)u[s[m]]={distance:-1,parent:null};return u}i(a,"buildGraph");function o(u){let s=a(),d=[u];for(s[u].distance=0;d.length;){let m=d.pop(),f=Object.keys(r[m]);for(let p=f.length,h=0;h<p;h++){let g=f[h],b=s[g];b.distance===-1&&(b.distance=s[m].distance+1,b.parent=m,d.unshift(g))}}return s}i(o,"deriveBFS");function c(u,s){return function(d){return s(u(d))}}i(c,"link");function l(u,s){let d=[s[u].parent,u],m=r[s[u].parent][u],f=s[u].parent;for(;s[f].parent;)d.unshift(s[f].parent),m=c(r[s[f].parent][f],m),f=s[f].parent;return m.conversion=d,m}i(l,"wrapConversion"),t.exports=function(u){let s=o(u),d={},m=Object.keys(s);for(let f=m.length,p=0;p<f;p++){let h=m[p];s[h].parent!==null&&(d[h]=l(h,s))}return d}}),cp=X((e,t)=>{var r=yu(),a=sp(),o={},c=Object.keys(r);function l(s){let d=i(function(...m){let f=m[0];return f==null?f:(f.length>1&&(m=f),s(m))},"wrappedFn");return"conversion"in s&&(d.conversion=s.conversion),d}i(l,"wrapRaw");function u(s){let d=i(function(...m){let f=m[0];if(f==null)return f;f.length>1&&(m=f);let p=s(m);if(typeof p=="object")for(let h=p.length,g=0;g<h;g++)p[g]=Math.round(p[g]);return p},"wrappedFn");return"conversion"in s&&(d.conversion=s.conversion),d}i(u,"wrapRounded"),c.forEach(s=>{o[s]={},Object.defineProperty(o[s],"channels",{value:r[s].channels}),Object.defineProperty(o[s],"labels",{value:r[s].labels});let d=a(s);Object.keys(d).forEach(m=>{let f=d[m];o[s][m]=u(f),o[s][m].raw=l(f)})}),t.exports=o});function rt(){return(rt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function wr(e,t){if(e==null)return{};var r,a,o={},c=Object.keys(e);for(a=0;a<c.length;a++)t.indexOf(r=c[a])>=0||(o[r]=e[r]);return o}function Mr(e){var t=Q(e),r=Q(function(a){t.current&&t.current(a)});return t.current=e,r.current}function On(e,t,r){var a=Mr(r),o=R(function(){return e.toHsva(t)}),c=o[0],l=o[1],u=Q({color:t,hsva:c});j(function(){if(!e.equal(t,u.current.color)){var d=e.toHsva(t);u.current={hsva:d,color:t},l(d)}},[t,e]),j(function(){var d;na(c,u.current.hsva)||e.equal(d=e.fromHsva(c),u.current.color)||(u.current={hsva:c,color:d},a(d))},[c,e,a]);var s=H(function(d){l(function(m){return Object.assign({},m,d)})},[]);return[c,s]}var pt,wt,kr,Tn,In,Or,kt,Tr,le,ul,sl,Ir,cl,dl,pl,ml,Dn,Dr,Gt,Bn,fl,Wt,hl,_n,Rn,Nn,na,Ln,gl,dp,bl,yl,Fn,Pn,El,vl,Eu,xl,jn,Al,vu,Cl,xu,pp=G(()=>{i(rt,"u"),i(wr,"c"),i(Mr,"i"),pt=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=1),e>r?r:e<t?t:e},"s"),wt=i(function(e){return"touches"in e},"f"),kr=i(function(e){return e&&e.ownerDocument.defaultView||self},"v"),Tn=i(function(e,t,r){var a=e.getBoundingClientRect(),o=wt(t)?function(c,l){for(var u=0;u<c.length;u++)if(c[u].identifier===l)return c[u];return c[0]}(t.touches,r):t;return{left:pt((o.pageX-(a.left+kr(e).pageXOffset))/a.width),top:pt((o.pageY-(a.top+kr(e).pageYOffset))/a.height)}},"d"),In=i(function(e){!wt(e)&&e.preventDefault()},"h"),Or=n.memo(function(e){var t=e.onMove,r=e.onKey,a=wr(e,["onMove","onKey"]),o=Q(null),c=Mr(t),l=Mr(r),u=Q(null),s=Q(!1),d=ce(function(){var h=i(function(E){In(E),(wt(E)?E.touches.length>0:E.buttons>0)&&o.current?c(Tn(o.current,E,u.current)):b(!1)},"e"),g=i(function(){return b(!1)},"r");function b(E){var v=s.current,A=kr(o.current),S=E?A.addEventListener:A.removeEventListener;S(v?"touchmove":"mousemove",h),S(v?"touchend":"mouseup",g)}return i(b,"t"),[function(E){var v=E.nativeEvent,A=o.current;if(A&&(In(v),!function(O,D){return D&&!wt(O)}(v,s.current)&&A)){if(wt(v)){s.current=!0;var S=v.changedTouches||[];S.length&&(u.current=S[0].identifier)}A.focus(),c(Tn(A,v,u.current)),b(!0)}},function(E){var v=E.which||E.keyCode;v<37||v>40||(E.preventDefault(),l({left:v===39?.05:v===37?-.05:0,top:v===40?.05:v===38?-.05:0}))},b]},[l,c]),m=d[0],f=d[1],p=d[2];return j(function(){return p},[p]),n.createElement("div",rt({},a,{onTouchStart:m,onMouseDown:m,className:"react-colorful__interactive",ref:o,onKeyDown:f,tabIndex:0,role:"slider"}))}),kt=i(function(e){return e.filter(Boolean).join(" ")},"g"),Tr=i(function(e){var t=e.color,r=e.left,a=e.top,o=a===void 0?.5:a,c=kt(["react-colorful__pointer",e.className]);return n.createElement("div",{className:c,style:{top:100*o+"%",left:100*r+"%"}},n.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t}}))},"p"),le=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=Math.pow(10,t)),Math.round(r*e)/r},"b"),ul={grad:.9,turn:360,rad:360/(2*Math.PI)},sl=i(function(e){return _n(Ir(e))},"x"),Ir=i(function(e){return e[0]==="#"&&(e=e.substring(1)),e.length<6?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:e.length===4?le(parseInt(e[3]+e[3],16)/255,2):1}:{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16),a:e.length===8?le(parseInt(e.substring(6,8),16)/255,2):1}},"C"),cl=i(function(e,t){return t===void 0&&(t="deg"),Number(e)*(ul[t]||1)},"E"),dl=i(function(e){var t=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?pl({h:cl(t[1],t[2]),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)}):{h:0,s:0,v:0,a:1}},"H"),pl=i(function(e){var t=e.s,r=e.l;return{h:e.h,s:(t*=(r<50?r:100-r)/100)>0?2*t/(r+t)*100:0,v:r+t,a:e.a}},"N"),ml=i(function(e){return hl(Bn(e))},"w"),Dn=i(function(e){var t=e.s,r=e.v,a=e.a,o=(200-t)*r/100;return{h:le(e.h),s:le(o>0&&o<200?t*r/100/(o<=100?o:200-o)*100:0),l:le(o/2),a:le(a,2)}},"y"),Dr=i(function(e){var t=Dn(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},"q"),Gt=i(function(e){var t=Dn(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},"k"),Bn=i(function(e){var t=e.h,r=e.s,a=e.v,o=e.a;t=t/360*6,r/=100,a/=100;var c=Math.floor(t),l=a*(1-r),u=a*(1-(t-c)*r),s=a*(1-(1-t+c)*r),d=c%6;return{r:le(255*[a,u,l,l,s,a][d]),g:le(255*[s,a,a,u,l,l][d]),b:le(255*[l,l,s,a,a,u][d]),a:le(o,2)}},"I"),fl=i(function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?_n({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},"z"),Wt=i(function(e){var t=e.toString(16);return t.length<2?"0"+t:t},"D"),hl=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=o<1?Wt(le(255*o)):"";return"#"+Wt(t)+Wt(r)+Wt(a)+c},"K"),_n=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=Math.max(t,r,a),l=c-Math.min(t,r,a),u=l?c===t?(r-a)/l:c===r?2+(a-t)/l:4+(t-r)/l:0;return{h:le(60*(u<0?u+6:u)),s:le(c?l/c*100:0),v:le(c/255*100),a:o}},"L"),Rn=n.memo(function(e){var t=e.hue,r=e.onChange,a=kt(["react-colorful__hue",e.className]);return n.createElement("div",{className:a},n.createElement(Or,{onMove:i(function(o){r({h:360*o.left})},"onMove"),onKey:i(function(o){r({h:pt(t+360*o.left,0,360)})},"onKey"),"aria-label":"Hue","aria-valuenow":le(t),"aria-valuemax":"360","aria-valuemin":"0"},n.createElement(Tr,{className:"react-colorful__hue-pointer",left:t/360,color:Dr({h:t,s:100,v:100,a:1})})))}),Nn=n.memo(function(e){var t=e.hsva,r=e.onChange,a={backgroundColor:Dr({h:t.h,s:100,v:100,a:1})};return n.createElement("div",{className:"react-colorful__saturation",style:a},n.createElement(Or,{onMove:i(function(o){r({s:100*o.left,v:100-100*o.top})},"onMove"),onKey:i(function(o){r({s:pt(t.s+100*o.left,0,100),v:pt(t.v-100*o.top,0,100)})},"onKey"),"aria-label":"Color","aria-valuetext":"Saturation "+le(t.s)+"%, Brightness "+le(t.v)+"%"},n.createElement(Tr,{className:"react-colorful__saturation-pointer",top:1-t.v/100,left:t.s/100,color:Dr(t)})))}),na=i(function(e,t){if(e===t)return!0;for(var r in e)if(e[r]!==t[r])return!1;return!0},"F"),Ln=i(function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")},"P"),gl=i(function(e,t){return e.toLowerCase()===t.toLowerCase()||na(Ir(e),Ir(t))},"X"),i(On,"Y"),bl=typeof window<"u"?pr:j,yl=i(function(){return dp||(typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0)},"$"),Fn=new Map,Pn=i(function(e){bl(function(){var t=e.current?e.current.ownerDocument:document;if(t!==void 0&&!Fn.has(t)){var r=t.createElement("style");r.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,Fn.set(t,r);var a=yl();a&&r.setAttribute("nonce",a),t.head.appendChild(r)}},[])},"Q"),El=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),u=Q(null);Pn(u);var s=On(r,o,c),d=s[0],m=s[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:u,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m,className:"react-colorful__last-control"}))},"U"),vl={defaultColor:"000",toHsva:sl,fromHsva:i(function(e){return ml({h:e.h,s:e.s,v:e.v,a:1})},"fromHsva"),equal:gl},Eu=i(function(e){return n.createElement(El,rt({},e,{colorModel:vl}))},"Z"),xl=i(function(e){var t=e.className,r=e.hsva,a=e.onChange,o={backgroundImage:"linear-gradient(90deg, "+Gt(Object.assign({},r,{a:0}))+", "+Gt(Object.assign({},r,{a:1}))+")"},c=kt(["react-colorful__alpha",t]),l=le(100*r.a);return n.createElement("div",{className:c},n.createElement("div",{className:"react-colorful__alpha-gradient",style:o}),n.createElement(Or,{onMove:i(function(u){a({a:u.left})},"onMove"),onKey:i(function(u){a({a:pt(r.a+u.left)})},"onKey"),"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},n.createElement(Tr,{className:"react-colorful__alpha-pointer",left:r.a,color:Gt(r)})))},"ee"),jn=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),u=Q(null);Pn(u);var s=On(r,o,c),d=s[0],m=s[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:u,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m}),n.createElement(xl,{hsva:d,onChange:m,className:"react-colorful__last-control"}))},"re"),Al={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:dl,fromHsva:Gt,equal:Ln},vu=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Al}))},"ue"),Cl={defaultColor:"rgba(0, 0, 0, 1)",toHsva:fl,fromHsva:i(function(e){var t=Bn(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},"fromHsva"),equal:Ln},xu=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Cl}))},"He")}),Au={};Md(Au,{ColorControl:()=>aa,default:()=>Cu});var Se,Sl,wl,kl,Ol,Tl,Il,Dl,Mn,Bl,_l,$n,Br,Rl,Nl,Ll,_r,Fl,Pl,Kt,Un,jl,Ml,$l,mt,Ul,Hl,Yt,Vl,aa,Cu,mp=G(()=>{"use strict";Se=Ae(cp()),an(),pp(),je(),Sl=y.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),wl=y(oe)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),kl=y.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Ol=y(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),Tl=y.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),Il=y.div(({theme:e,active:t})=>({width:16,height:16,boxShadow:t?`${e.appBorderColor} 0 0 0 1px inset, ${e.textMutedColor}50 0 0 0 4px`:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:e.appBorderRadius})),Dl=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,Mn=i(({value:e,style:t,...r})=>{let a=`linear-gradient(${e}, ${e}), ${Dl}, linear-gradient(#fff, #fff)`;return n.createElement(Il,{...r,style:{...t,backgroundImage:a}})},"Swatch"),Bl=y(de.Input)(({theme:e,readOnly:t})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:e.typography.fonts.base})),_l=y(wo)(({theme:e})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:e.input.color})),$n=(e=>(e.RGB="rgb",e.HSL="hsl",e.HEX="hex",e))($n||{}),Br=Object.values($n),Rl=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,Nl=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,Ll=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,_r=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,Fl=/^\s*#?([0-9a-f]{3})\s*$/i,Pl={hex:Eu,rgb:xu,hsl:vu},Kt={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},Un=i(e=>{let t=e?.match(Rl);if(!t)return[0,0,0,1];let[,r,a,o,c=1]=t;return[r,a,o,c].map(Number)},"stringToArgs"),jl=i(e=>{let[t,r,a,o]=Un(e),[c,l,u]=Se.default.rgb.hsl([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:Se.default.rgb.keyword([t,r,a]),colorSpace:"rgb",rgb:e,hsl:`hsla(${c}, ${l}%, ${u}%, ${o})`,hex:`#${Se.default.rgb.hex([t,r,a]).toLowerCase()}`}},"parseRgb"),Ml=i(e=>{let[t,r,a,o]=Un(e),[c,l,u]=Se.default.hsl.rgb([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:Se.default.hsl.keyword([t,r,a]),colorSpace:"hsl",rgb:`rgba(${c}, ${l}, ${u}, ${o})`,hsl:e,hex:`#${Se.default.hsl.hex([t,r,a]).toLowerCase()}`}},"parseHsl"),$l=i(e=>{let t=e.replace("#",""),r=Se.default.keyword.rgb(t)||Se.default.hex.rgb(t),a=Se.default.rgb.hsl(r),o=e;/[^#a-f0-9]/i.test(e)?o=t:_r.test(e)&&(o=`#${t}`);let c=!0;if(o.startsWith("#"))c=_r.test(o);else try{Se.default.keyword.hex(o)}catch{c=!1}return{valid:c,value:o,keyword:Se.default.rgb.keyword(r),colorSpace:"hex",rgb:`rgba(${r[0]}, ${r[1]}, ${r[2]}, 1)`,hsl:`hsla(${a[0]}, ${a[1]}%, ${a[2]}%, 1)`,hex:o}},"parseHexOrKeyword"),mt=i(e=>{if(e)return Nl.test(e)?jl(e):Ll.test(e)?Ml(e):$l(e)},"parseValue"),Ul=i((e,t,r)=>{if(!e||!t?.valid)return Kt[r];if(r!=="hex")return t?.[r]||Kt[r];if(!t.hex.startsWith("#"))try{return`#${Se.default.keyword.hex(t.hex)}`}catch{return Kt.hex}let a=t.hex.match(Fl);if(!a)return _r.test(t.hex)?t.hex:Kt.hex;let[o,c,l]=a[1].split("");return`#${o}${o}${c}${c}${l}${l}`},"getRealValue"),Hl=i((e,t)=>{let[r,a]=R(e||""),[o,c]=R(()=>mt(r)),[l,u]=R(o?.colorSpace||"hex");j(()=>{let f=e||"",p=mt(f);a(f),c(p),u(p?.colorSpace||"hex")},[e]);let s=ce(()=>Ul(r,o,l).toLowerCase(),[r,o,l]),d=H(f=>{let p=mt(f),h=p?.value||f||"";a(h),h===""&&(c(void 0),t(void 0)),p&&(c(p),u(p.colorSpace),t(p.value))},[t]),m=H(()=>{let f=(Br.indexOf(l)+1)%Br.length,p=Br[f];u(p);let h=o?.[p]||"";a(h),t(h)},[o,l,t]);return{value:r,realValue:s,updateValue:d,color:o,colorSpace:l,cycleColorSpace:m}},"useColorInput"),Yt=i(e=>e.replace(/\s*/,"").toLowerCase(),"id"),Vl=i((e,t,r)=>{let[a,o]=R(t?.valid?[t]:[]);j(()=>{t===void 0&&o([])},[t]);let c=ce(()=>(e||[]).map(u=>typeof u=="string"?mt(u):u.title?{...mt(u.color),keyword:u.title}:mt(u.color)).concat(a).filter(Boolean).slice(-27),[e,a]),l=H(u=>{u?.valid&&(c.some(s=>s&&s[r]&&Yt(s[r]||"")===Yt(u[r]||""))||o(s=>s.concat(u)))},[r,c]);return{presets:c,addPreset:l}},"usePresets"),aa=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,presetColors:c,startOpen:l=!1,argType:u})=>{let s=H(uu(r,200),[r]),{value:d,realValue:m,updateValue:f,color:p,colorSpace:h,cycleColorSpace:g}=Hl(t,s),{presets:b,addPreset:E}=Vl(c??[],p,h),v=Pl[h],A=!!u?.table?.readonly;return n.createElement(Sl,{"aria-readonly":A},n.createElement(wl,{startOpen:l,trigger:A?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>p&&E(p),tooltip:n.createElement(kl,null,n.createElement(v,{color:m==="transparent"?"#000000":m,onChange:f,onFocus:a,onBlur:o}),b.length>0&&n.createElement(Tl,null,b.map((S,O)=>n.createElement(oe,{key:`${S?.value||O}-${O}`,hasChrome:!1,tooltip:n.createElement(Ol,{note:S?.keyword||S?.value||""})},n.createElement(Mn,{value:S?.[h]||"",active:!!(p&&S&&S[h]&&Yt(S[h]||"")===Yt(p[h])),onClick:()=>S&&f(S.value||"")})))))},n.createElement(Mn,{value:m,style:{margin:4}})),n.createElement(Bl,{id:ke(e),value:d,onChange:S=>f(S.target.value),onFocus:S=>S.target.select(),readOnly:A,placeholder:"Choose color..."}),d?n.createElement(_l,{onClick:g}):null)},"ColorControl"),Cu=aa}),fp=X((e,t)=>{(function(r){if(typeof e=="object"&&typeof t<"u")t.exports=r();else if(typeof define=="function"&&define.amd)define([],r);else{var a;typeof window<"u"||typeof window<"u"?a=window:typeof self<"u"?a=self:a=this,a.memoizerific=r()}})(function(){var r,a,o;return i(function c(l,u,s){function d(p,h){if(!u[p]){if(!l[p]){var g=typeof Sr=="function"&&Sr;if(!h&&g)return g(p,!0);if(m)return m(p,!0);var b=new Error("Cannot find module '"+p+"'");throw b.code="MODULE_NOT_FOUND",b}var E=u[p]={exports:{}};l[p][0].call(E.exports,function(v){var A=l[p][1][v];return d(A||v)},E,E.exports,c,l,u,s)}return u[p].exports}i(d,"s");for(var m=typeof Sr=="function"&&Sr,f=0;f<s.length;f++)d(s[f]);return d},"e")({1:[function(c,l,u){l.exports=function(s){if(typeof Map!="function"||s){var d=c("./similar");return new d}else return new Map}},{"./similar":2}],2:[function(c,l,u){function s(){return this.list=[],this.lastItem=void 0,this.size=0,this}i(s,"Similar"),s.prototype.get=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d))return this.lastItem.val;if(m=this.indexOf(d),m>=0)return this.lastItem=this.list[m],this.list[m].val},s.prototype.set=function(d,m){var f;return this.lastItem&&this.isEqual(this.lastItem.key,d)?(this.lastItem.val=m,this):(f=this.indexOf(d),f>=0?(this.lastItem=this.list[f],this.list[f].val=m,this):(this.lastItem={key:d,val:m},this.list.push(this.lastItem),this.size++,this))},s.prototype.delete=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d)&&(this.lastItem=void 0),m=this.indexOf(d),m>=0)return this.size--,this.list.splice(m,1)[0]},s.prototype.has=function(d){var m;return this.lastItem&&this.isEqual(this.lastItem.key,d)?!0:(m=this.indexOf(d),m>=0?(this.lastItem=this.list[m],!0):!1)},s.prototype.forEach=function(d,m){var f;for(f=0;f<this.size;f++)d.call(m||this,this.list[f].val,this.list[f].key,this)},s.prototype.indexOf=function(d){var m;for(m=0;m<this.size;m++)if(this.isEqual(this.list[m].key,d))return m;return-1},s.prototype.isEqual=function(d,m){return d===m||d!==d&&m!==m},l.exports=s},{}],3:[function(c,l,u){var s=c("map-or-similar");l.exports=function(p){var h=new s(!1),g=[];return function(b){var E=i(function(){var v=h,A,S,O=arguments.length-1,D=Array(O+1),k=!0,I;if((E.numArgs||E.numArgs===0)&&E.numArgs!==O+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(I=0;I<O;I++){if(D[I]={cacheItem:v,arg:arguments[I]},v.has(arguments[I])){v=v.get(arguments[I]);continue}k=!1,A=new s(!1),v.set(arguments[I],A),v=A}return k&&(v.has(arguments[O])?S=v.get(arguments[O]):k=!1),k||(S=b.apply(null,arguments),v.set(arguments[O],S)),p>0&&(D[O]={cacheItem:v,arg:arguments[O]},k?d(g,D):g.push(D),g.length>p&&m(g.shift())),E.wasMemoized=k,E.numArgs=O+1,S},"memoizerific");return E.limit=p,E.wasMemoized=!1,E.cache=h,E.lru=g,E}};function d(p,h){var g=p.length,b=h.length,E,v,A;for(v=0;v<g;v++){for(E=!0,A=0;A<b;A++)if(!f(p[v][A].arg,h[A].arg)){E=!1;break}if(E)break}p.push(p.splice(v,1)[0])}i(d,"moveToMostRecentLru");function m(p){var h=p.length,g=p[h-1],b,E;for(g.cacheItem.delete(g.arg),E=h-2;E>=0&&(g=p[E],b=g.cacheItem.get(g.arg),!b||!b.size);E--)g.cacheItem.delete(g.arg)}i(m,"removeCachedResult");function f(p,h){return p===h||p!==p&&h!==h}i(f,"isEqual")},{"map-or-similar":1}]},{},[3])(3)})}),Su=X((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",amp:"&",AMP:"&",andand:"\u2A55",And:"\u2A53",and:"\u2227",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angmsd:"\u2221",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",apacir:"\u2A6F",ap:"\u2248",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250C",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252C",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxul:"\u2518",boxuL:"\u255B",boxUl:"\u255C",boxUL:"\u255D",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255A",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253C",boxvH:"\u256A",boxVh:"\u256B",boxVH:"\u256C",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251C",boxvR:"\u255E",boxVr:"\u255F",boxVR:"\u2560",bprime:"\u2035",breve:"\u02D8",Breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",Bscr:"\u212C",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsolb:"\u29C5",bsol:"\\",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",cap:"\u2229",Cap:"\u22D2",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",CenterDot:"\xB7",cfr:"\u{1D520}",Cfr:"\u212D",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25CB",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",conint:"\u222E",Conint:"\u222F",ContourIntegral:"\u222E",copf:"\u{1D554}",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xA9",COPY:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",cross:"\u2717",Cross:"\u2A2F",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cupbrcap:"\u2A48",cupcap:"\u2A46",CupCap:"\u224D",cup:"\u222A",Cup:"\u22D3",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21A1",dArr:"\u21D3",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21CA",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",diamond:"\u22C4",Diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21D3",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21BD",DownRightTeeVector:"\u295F",DownRightVectorBar:"\u2957",DownRightVector:"\u21C1",DownTeeArrow:"\u21A7",DownTee:"\u22A4",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",Ecirc:"\xCA",ecirc:"\xEA",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",escr:"\u212F",Escr:"\u2130",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",forall:"\u2200",ForAll:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",Fscr:"\u2131",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",gescc:"\u2AA9",ges:"\u2A7E",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",gg:"\u226B",Gg:"\u22D9",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2AA5",gl:"\u2277",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gnE:"\u2269",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gtcc:"\u2AA7",gtcir:"\u2A7A",gt:">",GT:">",Gt:"\u226B",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",harrcir:"\u2948",harr:"\u2194",hArr:"\u21D4",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",Hfr:"\u210C",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",Hopf:"\u210D",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\u{1D4BD}",Hscr:"\u210B",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",Ifr:"\u2111",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",incare:"\u2105",in:"\u2208",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",intcal:"\u22BA",int:"\u222B",Int:"\u222C",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",Iscr:"\u2110",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",lang:"\u27E8",Lang:"\u27EA",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",larrb:"\u21E4",larrbfs:"\u291F",larr:"\u2190",Larr:"\u219E",lArr:"\u21D0",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",latail:"\u2919",lAtail:"\u291B",lat:"\u2AAB",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lBarr:"\u290E",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27E8",LeftArrowBar:"\u21E4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21D0",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21C3",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTeeArrow:"\u21A4",LeftTee:"\u22A3",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangleBar:"\u29CF",LeftTriangle:"\u22B2",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21BF",LeftVectorBar:"\u2952",LeftVector:"\u21BC",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",lescc:"\u2AA8",les:"\u2A7D",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21C7",ll:"\u226A",Ll:"\u22D8",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoustache:"\u23B0",lmoust:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lnE:"\u2268",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftrightarrow:"\u27F7",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longmapsto:"\u27FC",longrightarrow:"\u27F6",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",Lscr:"\u2112",lsh:"\u21B0",Lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2AA6",ltcir:"\u2A79",lt:"<",LT:"<",Lt:"\u226A",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",midast:"*",midcir:"\u2AF0",mid:"\u2223",middot:"\xB7",minusb:"\u229F",minus:"\u2212",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",Mscr:"\u2133",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266E",naturals:"\u2115",natur:"\u266E",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21D7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nharr:"\u21AE",nhArr:"\u21CE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlarr:"\u219A",nlArr:"\u21CD",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219A",nLeftarrow:"\u21CD",nleftrightarrow:"\u21AE",nLeftrightarrow:"\u21CE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",nopf:"\u{1D55F}",Nopf:"\u2115",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangle:"\u22EB",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",nprec:"\u2280",npreceq:"\u2AAF\u0338",npre:"\u2AAF\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219B",nrArr:"\u21CF",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nRightarrow:"\u21CF",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvDash:"\u22AD",nVdash:"\u22AE",nVDash:"\u22AF",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21D6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",Ocirc:"\xD4",ocirc:"\xF4",ocir:"\u229A",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",Or:"\u2A54",or:"\u2228",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",otimesas:"\u2A36",Otimes:"\u2A37",otimes:"\u2297",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",para:"\xB6",parallel:"\u2225",par:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plus:"+",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",popf:"\u{1D561}",Popf:"\u2119",pound:"\xA3",prap:"\u2AB7",Pr:"\u2ABB",pr:"\u227A",prcue:"\u227C",precapprox:"\u2AB7",prec:"\u227A",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",pre:"\u2AAF",prE:"\u2AB3",precsim:"\u227E",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportional:"\u221D",Proportion:"\u2237",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",Qopf:"\u211A",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',QUOT:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",Rang:"\u27EB",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21A0",rArr:"\u21D2",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",rAtail:"\u291C",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rBarr:"\u290F",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",Re:"\u211C",rect:"\u25AD",reg:"\xAE",REG:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",Rfr:"\u211C",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrowBar:"\u21E5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21D2",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVectorBar:"\u2955",RightDownVector:"\u21C2",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTeeArrow:"\u21A6",RightTee:"\u22A2",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangleBar:"\u29D0",RightTriangle:"\u22B3",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVectorBar:"\u2954",RightUpVector:"\u21BE",RightVectorBar:"\u2953",RightVector:"\u21C0",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoustache:"\u23B1",rmoust:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",Ropf:"\u211D",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",rscr:"\u{1D4C7}",Rscr:"\u211B",rsh:"\u21B1",Rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2ABC",sc:"\u227B",sccue:"\u227D",sce:"\u2AB0",scE:"\u2AB4",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdotb:"\u22A1",sdot:"\u22C5",sdote:"\u2A66",searhk:"\u2925",searr:"\u2198",seArr:"\u21D8",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",solbar:"\u233F",solb:"\u29C4",sol:"/",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25A1",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squ:"\u25A1",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",Sub:"\u22D0",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",Subset:"\u22D0",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succapprox:"\u2AB8",succ:"\u227B",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",sum:"\u2211",Sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",Sup:"\u22D1",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",Supset:"\u22D1",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21D9",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",tilde:"\u02DC",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2A31",timesb:"\u22A0",times:"\xD7",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",topbot:"\u2336",topcir:"\u2AF1",top:"\u22A4",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",uarr:"\u2191",Uarr:"\u219F",uArr:"\u21D1",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21D1",UpArrowDownArrow:"\u21C5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21D5",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03C5",Upsi:"\u03D2",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTeeArrow:"\u21A5",UpTee:"\u22A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",vArr:"\u21D5",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vBar:"\u2AE8",Vbar:"\u2AEB",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22A2",vDash:"\u22A8",Vdash:"\u22A9",VDash:"\u22AB",Vdashl:"\u2AE6",veebar:"\u22BB",vee:"\u2228",Vee:"\u22C1",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",Wedge:"\u22C0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xharr:"\u27F7",xhArr:"\u27FA",Xi:"\u039E",xi:"\u03BE",xlarr:"\u27F5",xlArr:"\u27F8",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrarr:"\u27F6",xrArr:"\u27F9",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",yuml:"\xFF",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",zfr:"\u{1D537}",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",Zopf:"\u2124",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}}),hp=X((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",AElig:"\xC6",aelig:"\xE6",Agrave:"\xC0",agrave:"\xE0",amp:"&",AMP:"&",Aring:"\xC5",aring:"\xE5",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",brvbar:"\xA6",Ccedil:"\xC7",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",COPY:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",Eacute:"\xC9",eacute:"\xE9",Ecirc:"\xCA",ecirc:"\xEA",Egrave:"\xC8",egrave:"\xE8",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",GT:">",Iacute:"\xCD",iacute:"\xED",Icirc:"\xCE",icirc:"\xEE",iexcl:"\xA1",Igrave:"\xCC",igrave:"\xEC",iquest:"\xBF",Iuml:"\xCF",iuml:"\xEF",laquo:"\xAB",lt:"<",LT:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",Ntilde:"\xD1",ntilde:"\xF1",Oacute:"\xD3",oacute:"\xF3",Ocirc:"\xD4",ocirc:"\xF4",Ograve:"\xD2",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",Oslash:"\xD8",oslash:"\xF8",Otilde:"\xD5",otilde:"\xF5",Ouml:"\xD6",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',QUOT:'"',raquo:"\xBB",reg:"\xAE",REG:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",THORN:"\xDE",thorn:"\xFE",times:"\xD7",Uacute:"\xDA",uacute:"\xFA",Ucirc:"\xDB",ucirc:"\xFB",Ugrave:"\xD9",ugrave:"\xF9",uml:"\xA8",Uuml:"\xDC",uuml:"\xFC",Yacute:"\xDD",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"}}),wu=X((e,t)=>{t.exports={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}}),gp=X((e,t)=>{t.exports={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376}}),bp=X(e=>{"use strict";var t=e&&e.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(e,"__esModule",{value:!0});var r=t(gp()),a=String.fromCodePoint||function(c){var l="";return c>65535&&(c-=65536,l+=String.fromCharCode(c>>>10&1023|55296),c=56320|c&1023),l+=String.fromCharCode(c),l};function o(c){return c>=55296&&c<=57343||c>1114111?"\uFFFD":(c in r.default&&(c=r.default[c]),a(c))}i(o,"decodeCodePoint"),e.default=o}),zl=X(e=>{"use strict";var t=e&&e.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeHTML=e.decodeHTMLStrict=e.decodeXML=void 0;var r=t(Su()),a=t(hp()),o=t(wu()),c=t(bp()),l=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;e.decodeXML=u(o.default),e.decodeHTMLStrict=u(r.default);function u(m){var f=d(m);return function(p){return String(p).replace(l,f)}}i(u,"getStrictDecoder");var s=i(function(m,f){return m<f?1:-1},"sorter");e.decodeHTML=function(){for(var m=Object.keys(a.default).sort(s),f=Object.keys(r.default).sort(s),p=0,h=0;p<f.length;p++)m[h]===f[p]?(f[p]+=";?",h++):f[p]+=";";var g=new RegExp("&(?:"+f.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),b=d(r.default);function E(v){return v.substr(-1)!==";"&&(v+=";"),b(v)}return i(E,"replacer"),function(v){return String(v).replace(g,E)}}();function d(m){return i(function(f){if(f.charAt(1)==="#"){var p=f.charAt(2);return p==="X"||p==="x"?c.default(parseInt(f.substr(3),16)):c.default(parseInt(f.substr(2),10))}return m[f.slice(1,-1)]||f},"replace")}i(d,"getReplacer")}),ql=X(e=>{"use strict";var t=e&&e.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(e,"__esModule",{value:!0}),e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=void 0;var r=t(wu()),a=s(r.default),o=d(a);e.encodeXML=v(a);var c=t(Su()),l=s(c.default),u=d(l);e.encodeHTML=h(l,u),e.encodeNonAsciiHTML=v(l);function s(A){return Object.keys(A).sort().reduce(function(S,O){return S[A[O]]="&"+O+";",S},{})}i(s,"getInverseObj");function d(A){for(var S=[],O=[],D=0,k=Object.keys(A);D<k.length;D++){var I=k[D];I.length===1?S.push("\\"+I):O.push(I)}S.sort();for(var F=0;F<S.length-1;F++){for(var $=F;$<S.length-1&&S[$].charCodeAt(1)+1===S[$+1].charCodeAt(1);)$+=1;var V=1+$-F;V<3||S.splice(F,V,S[F]+"-"+S[$])}return O.unshift("["+S.join("")+"]"),new RegExp(O.join("|"),"g")}i(d,"getInverseReplacer");var m=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,f=String.prototype.codePointAt!=null?function(A){return A.codePointAt(0)}:function(A){return(A.charCodeAt(0)-55296)*1024+A.charCodeAt(1)-56320+65536};function p(A){return"&#x"+(A.length>1?f(A):A.charCodeAt(0)).toString(16).toUpperCase()+";"}i(p,"singleCharReplacer");function h(A,S){return function(O){return O.replace(S,function(D){return A[D]}).replace(m,p)}}i(h,"getInverse");var g=new RegExp(o.source+"|"+m.source,"g");function b(A){return A.replace(g,p)}i(b,"escape"),e.escape=b;function E(A){return A.replace(o,p)}i(E,"escapeUTF8"),e.escapeUTF8=E;function v(A){return function(S){return S.replace(g,function(O){return A[O]||p(O)})}}i(v,"getASCIIEncoder")}),yp=X(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXMLStrict=e.decodeHTML5Strict=e.decodeHTML4Strict=e.decodeHTML5=e.decodeHTML4=e.decodeHTMLStrict=e.decodeHTML=e.decodeXML=e.encodeHTML5=e.encodeHTML4=e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=e.encode=e.decodeStrict=e.decode=void 0;var t=zl(),r=ql();function a(s,d){return(!d||d<=0?t.decodeXML:t.decodeHTML)(s)}i(a,"decode"),e.decode=a;function o(s,d){return(!d||d<=0?t.decodeXML:t.decodeHTMLStrict)(s)}i(o,"decodeStrict"),e.decodeStrict=o;function c(s,d){return(!d||d<=0?r.encodeXML:r.encodeHTML)(s)}i(c,"encode"),e.encode=c;var l=ql();Object.defineProperty(e,"encodeXML",{enumerable:!0,get:i(function(){return l.encodeXML},"get")}),Object.defineProperty(e,"encodeHTML",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeNonAsciiHTML",{enumerable:!0,get:i(function(){return l.encodeNonAsciiHTML},"get")}),Object.defineProperty(e,"escape",{enumerable:!0,get:i(function(){return l.escape},"get")}),Object.defineProperty(e,"escapeUTF8",{enumerable:!0,get:i(function(){return l.escapeUTF8},"get")}),Object.defineProperty(e,"encodeHTML4",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeHTML5",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")});var u=zl();Object.defineProperty(e,"decodeXML",{enumerable:!0,get:i(function(){return u.decodeXML},"get")}),Object.defineProperty(e,"decodeHTML",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTMLStrict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML4",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML5",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML4Strict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML5Strict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeXMLStrict",{enumerable:!0,get:i(function(){return u.decodeXML},"get")})}),Ep=X((e,t)=>{"use strict";function r(x,C){if(!(x instanceof C))throw new TypeError("Cannot call a class as a function")}i(r,"_classCallCheck");function a(x,C){for(var w=0;w<C.length;w++){var B=C[w];B.enumerable=B.enumerable||!1,B.configurable=!0,"value"in B&&(B.writable=!0),Object.defineProperty(x,B.key,B)}}i(a,"_defineProperties");function o(x,C,w){return C&&a(x.prototype,C),w&&a(x,w),x}i(o,"_createClass");function c(x,C){var w=typeof Symbol<"u"&&x[Symbol.iterator]||x["@@iterator"];if(!w){if(Array.isArray(x)||(w=l(x))||C&&x&&typeof x.length=="number"){w&&(x=w);var B=0,T=i(function(){},"F");return{s:T,n:i(function(){return B>=x.length?{done:!0}:{done:!1,value:x[B++]}},"n"),e:i(function(Z){throw Z},"e"),f:T}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var _=!0,N=!1,q;return{s:i(function(){w=w.call(x)},"s"),n:i(function(){var Z=w.next();return _=Z.done,Z},"n"),e:i(function(Z){N=!0,q=Z},"e"),f:i(function(){try{!_&&w.return!=null&&w.return()}finally{if(N)throw q}},"f")}}i(c,"_createForOfIteratorHelper");function l(x,C){if(x){if(typeof x=="string")return u(x,C);var w=Object.prototype.toString.call(x).slice(8,-1);if(w==="Object"&&x.constructor&&(w=x.constructor.name),w==="Map"||w==="Set")return Array.from(x);if(w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w))return u(x,C)}}i(l,"_unsupportedIterableToArray");function u(x,C){(C==null||C>x.length)&&(C=x.length);for(var w=0,B=new Array(C);w<C;w++)B[w]=x[w];return B}i(u,"_arrayLikeToArray");var s=yp(),d={fg:"#FFF",bg:"#000",newline:!1,escapeXML:!1,stream:!1,colors:m()};function m(){var x={0:"#000",1:"#A00",2:"#0A0",3:"#A50",4:"#00A",5:"#A0A",6:"#0AA",7:"#AAA",8:"#555",9:"#F55",10:"#5F5",11:"#FF5",12:"#55F",13:"#F5F",14:"#5FF",15:"#FFF"};return A(0,5).forEach(function(C){A(0,5).forEach(function(w){A(0,5).forEach(function(B){return f(C,w,B,x)})})}),A(0,23).forEach(function(C){var w=C+232,B=p(C*10+8);x[w]="#"+B+B+B}),x}i(m,"getDefaultColors");function f(x,C,w,B){var T=16+x*36+C*6+w,_=x>0?x*40+55:0,N=C>0?C*40+55:0,q=w>0?w*40+55:0;B[T]=h([_,N,q])}i(f,"setStyleColor");function p(x){for(var C=x.toString(16);C.length<2;)C="0"+C;return C}i(p,"toHexString");function h(x){var C=[],w=c(x),B;try{for(w.s();!(B=w.n()).done;){var T=B.value;C.push(p(T))}}catch(_){w.e(_)}finally{w.f()}return"#"+C.join("")}i(h,"toColorHexString");function g(x,C,w,B){var T;return C==="text"?T=D(w,B):C==="display"?T=E(x,w,B):C==="xterm256Foreground"?T=F(x,B.colors[w]):C==="xterm256Background"?T=$(x,B.colors[w]):C==="rgb"&&(T=b(x,w)),T}i(g,"generateOutput");function b(x,C){C=C.substring(2).slice(0,-1);var w=+C.substr(0,2),B=C.substring(5).split(";"),T=B.map(function(_){return("0"+Number(_).toString(16)).substr(-2)}).join("");return I(x,(w===38?"color:#":"background-color:#")+T)}i(b,"handleRgb");function E(x,C,w){C=parseInt(C,10);var B={"-1":i(function(){return"<br/>"},"_"),0:i(function(){return x.length&&v(x)},"_"),1:i(function(){return k(x,"b")},"_"),3:i(function(){return k(x,"i")},"_"),4:i(function(){return k(x,"u")},"_"),8:i(function(){return I(x,"display:none")},"_"),9:i(function(){return k(x,"strike")},"_"),22:i(function(){return I(x,"font-weight:normal;text-decoration:none;font-style:normal")},"_"),23:i(function(){return V(x,"i")},"_"),24:i(function(){return V(x,"u")},"_"),39:i(function(){return F(x,w.fg)},"_"),49:i(function(){return $(x,w.bg)},"_"),53:i(function(){return I(x,"text-decoration:overline")},"_")},T;return B[C]?T=B[C]():4<C&&C<7?T=k(x,"blink"):29<C&&C<38?T=F(x,w.colors[C-30]):39<C&&C<48?T=$(x,w.colors[C-40]):89<C&&C<98?T=F(x,w.colors[8+(C-90)]):99<C&&C<108&&(T=$(x,w.colors[8+(C-100)])),T}i(E,"handleDisplay");function v(x){var C=x.slice(0);return x.length=0,C.reverse().map(function(w){return"</"+w+">"}).join("")}i(v,"resetStyles");function A(x,C){for(var w=[],B=x;B<=C;B++)w.push(B);return w}i(A,"range");function S(x){return function(C){return(x===null||C.category!==x)&&x!=="all"}}i(S,"notCategory");function O(x){x=parseInt(x,10);var C=null;return x===0?C="all":x===1?C="bold":2<x&&x<5?C="underline":4<x&&x<7?C="blink":x===8?C="hide":x===9?C="strike":29<x&&x<38||x===39||89<x&&x<98?C="foreground-color":(39<x&&x<48||x===49||99<x&&x<108)&&(C="background-color"),C}i(O,"categoryForCode");function D(x,C){return C.escapeXML?s.encodeXML(x):x}i(D,"pushText");function k(x,C,w){return w||(w=""),x.push(C),"<".concat(C).concat(w?' style="'.concat(w,'"'):"",">")}i(k,"pushTag");function I(x,C){return k(x,"span",C)}i(I,"pushStyle");function F(x,C){return k(x,"span","color:"+C)}i(F,"pushForegroundColor");function $(x,C){return k(x,"span","background-color:"+C)}i($,"pushBackgroundColor");function V(x,C){var w;if(x.slice(-1)[0]===C&&(w=x.pop()),w)return"</"+C+">"}i(V,"closeTag");function z(x,C,w){var B=!1,T=3;function _(){return""}i(_,"remove");function N(Be,_e){return w("xterm256Foreground",_e),""}i(N,"removeXterm256Foreground");function q(Be,_e){return w("xterm256Background",_e),""}i(q,"removeXterm256Background");function Z(Be){return C.newline?w("display",-1):w("text",Be),""}i(Z,"newline");function Ft(Be,_e){B=!0,_e.trim().length===0&&(_e="0"),_e=_e.trimRight(";").split(";");var ur=c(_e),uo;try{for(ur.s();!(uo=ur.n()).done;){var wc=uo.value;w("display",wc)}}catch(kc){ur.e(kc)}finally{ur.f()}return""}i(Ft,"ansiMess");function At(Be){return w("text",Be),""}i(At,"realText");function Pt(Be){return w("rgb",Be),""}i(Pt,"rgb");var ao=[{pattern:/^\x08+/,sub:_},{pattern:/^\x1b\[[012]?K/,sub:_},{pattern:/^\x1b\[\(B/,sub:_},{pattern:/^\x1b\[[34]8;2;\d+;\d+;\d+m/,sub:Pt},{pattern:/^\x1b\[38;5;(\d+)m/,sub:N},{pattern:/^\x1b\[48;5;(\d+)m/,sub:q},{pattern:/^\n/,sub:Z},{pattern:/^\r+\n/,sub:Z},{pattern:/^\r/,sub:Z},{pattern:/^\x1b\[((?:\d{1,3};?)+|)m/,sub:Ft},{pattern:/^\x1b\[\d?J/,sub:_},{pattern:/^\x1b\[\d{0,3};\d{0,3}f/,sub:_},{pattern:/^\x1b\[?[\d;]{0,3}/,sub:_},{pattern:/^(([^\x1b\x08\r\n])+)/,sub:At}];function oo(Be,_e){_e>T&&B||(B=!1,x=x.replace(Be.pattern,Be.sub))}i(oo,"process");var lo=[],Ac=x,jt=Ac.length;e:for(;jt>0;){for(var pn=0,io=0,Cc=ao.length;io<Cc;pn=++io){var Sc=ao[pn];if(oo(Sc,pn),x.length!==jt){jt=x.length;continue e}}if(x.length===jt)break;lo.push(0),jt=x.length}return lo}i(z,"tokenize");function W(x,C,w){return C!=="text"&&(x=x.filter(S(O(w))),x.push({token:C,data:w,category:O(w)})),x}i(W,"updateStickyStack");var M=function(){function x(C){r(this,x),C=C||{},C.colors&&(C.colors=Object.assign({},d.colors,C.colors)),this.options=Object.assign({},d,C),this.stack=[],this.stickyStack=[]}return i(x,"Filter"),o(x,[{key:"toHtml",value:i(function(C){var w=this;C=typeof C=="string"?[C]:C;var B=this.stack,T=this.options,_=[];return this.stickyStack.forEach(function(N){var q=g(B,N.token,N.data,T);q&&_.push(q)}),z(C.join(""),T,function(N,q){var Z=g(B,N,q,T);Z&&_.push(Z),T.stream&&(w.stickyStack=W(w.stickyStack,N,q))}),B.length&&_.push(v(B)),_.join("")},"toHtml")}]),x}();t.exports=M}),La=X((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),vp=X((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Fa=X((e,t)=>{var r=vp();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),xp=X((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),Ap=X((e,t)=>{var r=xp();function a(c,l){var u=Object.keys(c);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(c);l&&(s=s.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),u.push.apply(u,s)}return u}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var u=arguments[l]!=null?arguments[l]:{};l%2?a(u,!0).forEach(function(s){r(c,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):a(u).forEach(function(s){Object.defineProperty(c,s,Object.getOwnPropertyDescriptor(u,s))})}return c}i(o,"_objectSpread2"),t.exports=o}),Cp=X((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Sp=X((e,t)=>{var r=Cp();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),wp=X((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),kp=X((e,t)=>{var r=wp();function a(c,l){var u=Object.keys(c);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(c);l&&(s=s.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),u.push.apply(u,s)}return u}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var u=arguments[l]!=null?arguments[l]:{};l%2?a(u,!0).forEach(function(s){r(c,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):a(u).forEach(function(s){Object.defineProperty(c,s,Object.getOwnPropertyDescriptor(u,s))})}return c}i(o,"_objectSpread2"),t.exports=o}),Op=X((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),Tp=X((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Ip=X((e,t)=>{var r=Tp();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),Gl=Object.prototype.hasOwnProperty;function oa(e,t,r){for(r of e.keys())if(ut(r,t))return r}i(oa,"find");function ut(e,t){var r,a,o;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((a=e.length)===t.length)for(;a--&&ut(e[a],t[a]););return a===-1}if(r===Set){if(e.size!==t.size)return!1;for(a of e)if(o=a,o&&typeof o=="object"&&(o=oa(t,o),!o)||!t.has(o))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(a of e)if(o=a[0],o&&typeof o=="object"&&(o=oa(t,o),!o)||!ut(a[1],t.get(o)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((a=e.byteLength)===t.byteLength)for(;a--&&e.getInt8(a)===t.getInt8(a););return a===-1}if(ArrayBuffer.isView(e)){if((a=e.byteLength)===t.byteLength)for(;a--&&e[a]===t[a];);return a===-1}if(!r||typeof e=="object"){a=0;for(r in e)if(Gl.call(e,r)&&++a&&!Gl.call(t,r)||!(r in t)||!ut(e[r],t[r]))return!1;return Object.keys(t).length===a}}return e!==e&&t!==t}i(ut,"dequal");an();function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},fe.apply(null,arguments)}i(fe,"_extends");function ku(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}i(ku,"_assertThisInitialized");function Rt(e,t){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,a){return r.__proto__=a,r},Rt(e,t)}i(Rt,"_setPrototypeOf");function Ou(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Rt(e,t)}i(Ou,"_inheritsLoose");function Kr(e){return Kr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kr(e)}i(Kr,"_getPrototypeOf");function Tu(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}i(Tu,"_isNativeFunction");function Pa(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pa=i(function(){return!!e},"_isNativeReflectConstruct"))()}i(Pa,"_isNativeReflectConstruct");function Iu(e,t,r){if(Pa())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var o=new(e.bind.apply(e,a));return r&&Rt(o,r.prototype),o}i(Iu,"_construct");function Yr(e){var t=typeof Map=="function"?new Map:void 0;return Yr=i(function(r){if(r===null||!Tu(r))return r;if(typeof r!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(r))return t.get(r);t.set(r,a)}function a(){return Iu(r,arguments,Kr(this).constructor)}return i(a,"Wrapper"),a.prototype=Object.create(r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),Rt(a,r)},"_wrapNativeSuper"),Yr(e)}i(Yr,"_wrapNativeSuper");var Dp={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).

`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).

`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.

`,4:`Couldn't generate valid rgb string from %s, it returned %s.

`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.

`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).

`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).

`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.

`,9:`Please provide a number of steps to the modularScale helper.

`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,11:`Invalid value passed as base to modularScale, expected number or em string but got "%s"

`,12:`Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.

`,13:`Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.

`,14:`Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.

`,15:`Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.

`,16:`You must provide a template to this method.

`,17:`You passed an unsupported selector state to this method.

`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,19:`fromSize and toSize must be provided as stringified numbers with the same units.

`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,21:"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",22:"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",23:`fontFace expects a name of a font-family.

`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,25:`fontFace expects localFonts to be an array.

`,26:`fontFace expects fileFormats to be an array.

`,27:`radialGradient requries at least 2 color-stops to properly render.

`,28:`Please supply a filename to retinaImage() as the first argument.

`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,30:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation

`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])
To pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')

`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation

`,34:`borderRadius expects a radius value as a string or number as the second argument.

`,35:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,36:`Property must be a string value.

`,37:`Syntax Error at %s.

`,38:`Formula contains a function that needs parentheses at %s.

`,39:`Formula is missing closing parenthesis at %s.

`,40:`Formula has too many closing parentheses at %s.

`,41:`All values in a formula must have the same unit or be unitless.

`,42:`Please provide a number of steps to the modularScale helper.

`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.

`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.

`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.

`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,48:`fromSize and toSize must be provided as stringified numbers with the same units.

`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.

`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.

`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,53:`fontFace expects localFonts to be an array.

`,54:`fontFace expects fileFormats to be an array.

`,55:`fontFace expects a name of a font-family.

`,56:`linearGradient requries at least 2 color-stops to properly render.

`,57:`radialGradient requries at least 2 color-stops to properly render.

`,58:`Please supply a filename to retinaImage() as the first argument.

`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,60:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",61:`Property must be a string value.

`,62:`borderRadius expects a radius value as a string or number as the second argument.

`,63:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.

`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').

`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.

`,67:`You must provide a template to this method.

`,68:`You passed an unsupported selector state to this method.

`,69:`Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.

`,70:`Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.

`,71:`Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.

`,72:`Passed invalid base value %s to %s(), please pass a value like "12px" or 12.

`,73:`Please provide a valid CSS variable.

`,74:`CSS variable not found and no default was provided.

`,75:`important requires a valid style object, got a %s instead.

`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.

`,77:`remToPx expects a value in "rem" but you provided it in "%s".

`,78:`base must be set in "px" or "%" but you set it in "%s".
`};function Du(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t[0],o=[],c;for(c=1;c<t.length;c+=1)o.push(t[c]);return o.forEach(function(l){a=a.replace(/%[a-z]/,l)}),a}i(Du,"format");var xe=function(e){Ou(t,e);function t(r){for(var a,o=arguments.length,c=new Array(o>1?o-1:0),l=1;l<o;l++)c[l-1]=arguments[l];return a=e.call(this,Du.apply(void 0,[Dp[r]].concat(c)))||this,ku(a)}return i(t,"PolishedError"),t}(Yr(Error));function la(e,t){return e.substr(-t.length)===t}i(la,"endsWith");var Bp=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function ia(e){if(typeof e!="string")return e;var t=e.match(Bp);return t?parseFloat(e):e}i(ia,"stripUnit");var _p=i(function(e){return function(t,r){r===void 0&&(r="16px");var a=t,o=r;if(typeof t=="string"){if(!la(t,"px"))throw new xe(69,e,t);a=ia(t)}if(typeof r=="string"){if(!la(r,"px"))throw new xe(70,e,r);o=ia(r)}if(typeof a=="string")throw new xe(71,t,e);if(typeof o=="string")throw new xe(72,r,e);return""+a/o+e}},"pxtoFactory"),Bu=_p,wC=Bu("em"),kC=Bu("rem");function $r(e){return Math.round(e*255)}i($r,"colorToInt");function _u(e,t,r){return $r(e)+","+$r(t)+","+$r(r)}i(_u,"convertToInt");function Nt(e,t,r,a){if(a===void 0&&(a=_u),t===0)return a(r,r,r);var o=(e%360+360)%360/60,c=(1-Math.abs(2*r-1))*t,l=c*(1-Math.abs(o%2-1)),u=0,s=0,d=0;o>=0&&o<1?(u=c,s=l):o>=1&&o<2?(u=l,s=c):o>=2&&o<3?(s=c,d=l):o>=3&&o<4?(s=l,d=c):o>=4&&o<5?(u=l,d=c):o>=5&&o<6&&(u=c,d=l);var m=r-c/2,f=u+m,p=s+m,h=d+m;return a(f,p,h)}i(Nt,"hslToRgb");var Wl={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function Ru(e){if(typeof e!="string")return e;var t=e.toLowerCase();return Wl[t]?"#"+Wl[t]:e}i(Ru,"nameToHex");var Rp=/^#[a-fA-F0-9]{6}$/,Np=/^#[a-fA-F0-9]{8}$/,Lp=/^#[a-fA-F0-9]{3}$/,Fp=/^#[a-fA-F0-9]{4}$/,Hn=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,Pp=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,jp=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,Mp=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function bt(e){if(typeof e!="string")throw new xe(3);var t=Ru(e);if(t.match(Rp))return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16)};if(t.match(Np)){var r=parseFloat((parseInt(""+t[7]+t[8],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16),alpha:r}}if(t.match(Lp))return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16)};if(t.match(Fp)){var a=parseFloat((parseInt(""+t[4]+t[4],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16),alpha:a}}var o=Hn.exec(t);if(o)return{red:parseInt(""+o[1],10),green:parseInt(""+o[2],10),blue:parseInt(""+o[3],10)};var c=Pp.exec(t.substring(0,50));if(c)return{red:parseInt(""+c[1],10),green:parseInt(""+c[2],10),blue:parseInt(""+c[3],10),alpha:parseFloat(""+c[4])>1?parseFloat(""+c[4])/100:parseFloat(""+c[4])};var l=jp.exec(t);if(l){var u=parseInt(""+l[1],10),s=parseInt(""+l[2],10)/100,d=parseInt(""+l[3],10)/100,m="rgb("+Nt(u,s,d)+")",f=Hn.exec(m);if(!f)throw new xe(4,t,m);return{red:parseInt(""+f[1],10),green:parseInt(""+f[2],10),blue:parseInt(""+f[3],10)}}var p=Mp.exec(t.substring(0,50));if(p){var h=parseInt(""+p[1],10),g=parseInt(""+p[2],10)/100,b=parseInt(""+p[3],10)/100,E="rgb("+Nt(h,g,b)+")",v=Hn.exec(E);if(!v)throw new xe(4,t,E);return{red:parseInt(""+v[1],10),green:parseInt(""+v[2],10),blue:parseInt(""+v[3],10),alpha:parseFloat(""+p[4])>1?parseFloat(""+p[4])/100:parseFloat(""+p[4])}}throw new xe(5)}i(bt,"parseToRgb");function Nu(e){var t=e.red/255,r=e.green/255,a=e.blue/255,o=Math.max(t,r,a),c=Math.min(t,r,a),l=(o+c)/2;if(o===c)return e.alpha!==void 0?{hue:0,saturation:0,lightness:l,alpha:e.alpha}:{hue:0,saturation:0,lightness:l};var u,s=o-c,d=l>.5?s/(2-o-c):s/(o+c);switch(o){case t:u=(r-a)/s+(r<a?6:0);break;case r:u=(a-t)/s+2;break;default:u=(t-r)/s+4;break}return u*=60,e.alpha!==void 0?{hue:u,saturation:d,lightness:l,alpha:e.alpha}:{hue:u,saturation:d,lightness:l}}i(Nu,"rgbToHsl");function Ye(e){return Nu(bt(e))}i(Ye,"parseToHsl");var $p=i(function(e){return e.length===7&&e[1]===e[2]&&e[3]===e[4]&&e[5]===e[6]?"#"+e[1]+e[3]+e[5]:e},"reduceHexValue"),ua=$p;function at(e){var t=e.toString(16);return t.length===1?"0"+t:t}i(at,"numberToHex");function Ur(e){return at(Math.round(e*255))}i(Ur,"colorToHex");function Lu(e,t,r){return ua("#"+Ur(e)+Ur(t)+Ur(r))}i(Lu,"convertToHex");function ar(e,t,r){return Nt(e,t,r,Lu)}i(ar,"hslToHex");function Fu(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return ar(e,t,r);if(typeof e=="object"&&t===void 0&&r===void 0)return ar(e.hue,e.saturation,e.lightness);throw new xe(1)}i(Fu,"hsl");function Pu(e,t,r,a){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?ar(e,t,r):"rgba("+Nt(e,t,r)+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?ar(e.hue,e.saturation,e.lightness):"rgba("+Nt(e.hue,e.saturation,e.lightness)+","+e.alpha+")";throw new xe(2)}i(Pu,"hsla");function Jr(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return ua("#"+at(e)+at(t)+at(r));if(typeof e=="object"&&t===void 0&&r===void 0)return ua("#"+at(e.red)+at(e.green)+at(e.blue));throw new xe(6)}i(Jr,"rgb");function Fe(e,t,r,a){if(typeof e=="string"&&typeof t=="number"){var o=bt(e);return"rgba("+o.red+","+o.green+","+o.blue+","+t+")"}else{if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?Jr(e,t,r):"rgba("+e+","+t+","+r+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?Jr(e.red,e.green,e.blue):"rgba("+e.red+","+e.green+","+e.blue+","+e.alpha+")"}throw new xe(7)}i(Fe,"rgba");var Up=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isRgb"),Hp=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&typeof e.alpha=="number"},"isRgba"),Vp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isHsl"),zp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&typeof e.alpha=="number"},"isHsla");function Je(e){if(typeof e!="object")throw new xe(8);if(Hp(e))return Fe(e);if(Up(e))return Jr(e);if(zp(e))return Pu(e);if(Vp(e))return Fu(e);throw new xe(8)}i(Je,"toColorString");function ja(e,t,r){return i(function(){var a=r.concat(Array.prototype.slice.call(arguments));return a.length>=t?e.apply(this,a):ja(e,t,a)},"fn")}i(ja,"curried");function Ce(e){return ja(e,e.length,[])}i(Ce,"curry");function ju(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{hue:r.hue+parseFloat(e)}))}i(ju,"adjustHue");var OC=Ce(ju);function vt(e,t,r){return Math.max(e,Math.min(t,r))}i(vt,"guard");function Mu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness-parseFloat(e))}))}i(Mu,"darken");var qp=Ce(Mu),Ge=qp;function $u(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation-parseFloat(e))}))}i($u,"desaturate");var TC=Ce($u);function Uu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness+parseFloat(e))}))}i(Uu,"lighten");var Gp=Ce(Uu),ht=Gp;function Hu(e,t,r){if(t==="transparent")return r;if(r==="transparent")return t;if(e===0)return r;var a=bt(t),o=fe({},a,{alpha:typeof a.alpha=="number"?a.alpha:1}),c=bt(r),l=fe({},c,{alpha:typeof c.alpha=="number"?c.alpha:1}),u=o.alpha-l.alpha,s=parseFloat(e)*2-1,d=s*u===-1?s:s+u,m=1+s*u,f=(d/m+1)/2,p=1-f,h={red:Math.floor(o.red*f+l.red*p),green:Math.floor(o.green*f+l.green*p),blue:Math.floor(o.blue*f+l.blue*p),alpha:o.alpha*parseFloat(e)+l.alpha*(1-parseFloat(e))};return Fe(h)}i(Hu,"mix");var Wp=Ce(Hu),Vu=Wp;function zu(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,(a*100+parseFloat(e)*100)/100)});return Fe(o)}i(zu,"opacify");var Kp=Ce(zu),Xt=Kp;function qu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation+parseFloat(e))}))}i(qu,"saturate");var IC=Ce(qu);function Gu(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{hue:parseFloat(e)}))}i(Gu,"setHue");var DC=Ce(Gu);function Wu(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{lightness:parseFloat(e)}))}i(Wu,"setLightness");var BC=Ce(Wu);function Ku(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{saturation:parseFloat(e)}))}i(Ku,"setSaturation");var _C=Ce(Ku);function Yu(e,t){return t==="transparent"?t:Vu(parseFloat(e),"rgb(0, 0, 0)",t)}i(Yu,"shade");var RC=Ce(Yu);function Ju(e,t){return t==="transparent"?t:Vu(parseFloat(e),"rgb(255, 255, 255)",t)}i(Ju,"tint");var NC=Ce(Ju);function Xu(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,+(a*100-parseFloat(e)*100).toFixed(2)/100)});return Fe(o)}i(Xu,"transparentize");var Yp=Ce(Xu),Y=Yp,Jp=y.div(Ze,({theme:e})=>({backgroundColor:e.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:e.appBorderRadius,border:`1px dashed ${e.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:Y(.3,e.color.defaultText),fontSize:e.typography.size.s2})),Zu=i(e=>n.createElement(Jp,{...e,className:"docblock-emptyblock sb-unstyled"}),"EmptyBlock"),Xp=y(Vt)(({theme:e})=>({fontSize:`${e.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:e.appBorderRadius,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}})),Zp=y.div(({theme:e})=>({background:e.background.content,borderRadius:e.appBorderRadius,border:`1px solid ${e.appBorderColor}`,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),Rr=y.div(({theme:e})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${qo}`]:{margin:0}})),Qp=i(()=>n.createElement(Zp,null,n.createElement(Rr,null),n.createElement(Rr,{style:{width:"80%"}}),n.createElement(Rr,{style:{width:"30%"}}),n.createElement(Rr,{style:{width:"80%"}})),"SourceSkeleton"),em=i(({isLoading:e,error:t,language:r,code:a,dark:o,format:c=!0,...l})=>{let{typography:u}=Ie();if(e)return n.createElement(Qp,null);if(t)return n.createElement(Zu,null,t);let s=n.createElement(Xp,{bordered:!0,copyable:!0,format:c,language:r??"jsx",className:"docblock-source sb-unstyled",...l},a);if(typeof o>"u")return s;let d=o?An.dark:An.light;return n.createElement(Vo,{theme:zo({...d,fontCode:u.fonts.mono,fontBase:u.fonts.base})},s)},"Source"),ie=i(e=>`& :where(${e}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${e}))`,"toGlobalSelector"),Ma=600,GC=y.h1(Ze,({theme:e})=>({color:e.color.defaultText,fontSize:e.typography.size.m3,fontWeight:e.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),WC=y.h2(Ze,({theme:e})=>({fontWeight:e.typography.weight.regular,fontSize:e.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.m1,lineHeight:"28px",marginBottom:24},color:Y(.25,e.color.defaultText)})),KC=y.div(({theme:e})=>{let t={fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},r={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:e.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},a={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:e.typography.size.s2-1,border:e.base==="light"?`1px solid ${e.color.mediumlight}`:`1px solid ${e.color.darker}`,color:e.base==="light"?Y(.1,e.color.defaultText):Y(.3,e.color.defaultText),backgroundColor:e.base==="light"?e.color.lighter:e.color.border};return{maxWidth:1e3,width:"100%",minWidth:0,[ie("a")]:{...t,fontSize:"inherit",lineHeight:"24px",color:e.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[ie("blockquote")]:{...t,margin:"16px 0",borderLeft:`4px solid ${e.color.medium}`,padding:"0 15px",color:e.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[ie("div")]:t,[ie("dl")]:{...t,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[ie("h1")]:{...t,...r,fontSize:`${e.typography.size.l1}px`,fontWeight:e.typography.weight.bold},[ie("h2")]:{...t,...r,fontSize:`${e.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${e.appBorderColor}`},[ie("h3")]:{...t,...r,fontSize:`${e.typography.size.m1}px`,fontWeight:e.typography.weight.bold},[ie("h4")]:{...t,...r,fontSize:`${e.typography.size.s3}px`},[ie("h5")]:{...t,...r,fontSize:`${e.typography.size.s2}px`},[ie("h6")]:{...t,...r,fontSize:`${e.typography.size.s2}px`,color:e.color.dark},[ie("hr")]:{border:"0 none",borderTop:`1px solid ${e.appBorderColor}`,height:4,padding:0},[ie("img")]:{maxWidth:"100%"},[ie("li")]:{...t,fontSize:e.typography.size.s2,color:e.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":a},[ie("ol")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[ie("p")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",color:e.color.defaultText,"& code":a},[ie("pre")]:{...t,fontFamily:e.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[ie("span")]:{...t,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${e.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:e.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[ie("table")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${e.appBorderColor}`,backgroundColor:e.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:e.base==="dark"?e.color.darker:e.color.lighter},"& tr th":{fontWeight:"bold",color:e.color.defaultText,border:`1px solid ${e.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${e.appBorderColor}`,color:e.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[ie("ul")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),YC=y.div(({theme:e})=>({background:e.background.content,display:"flex",flexDirection:"row-reverse",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${Ma}px)`]:{}})),on=i(e=>({borderRadius:e.appBorderRadius,background:e.background.content,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${e.appBorderColor}`}),"getBlockBackgroundStyle"),{window:p5}=globalThis,tm=Ct({scale:1}),{PREVIEW_URL:f5}=globalThis,h5=y.strong(({theme:e})=>({color:e.color.orange})),rm=y(mn)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),nm=y.div({display:"flex",alignItems:"center",gap:4}),am=y.div(({theme:e})=>({width:14,height:14,borderRadius:2,margin:"0 7px",backgroundColor:e.appBorderColor,animation:`${e.animation.glow} 1.5s ease-in-out infinite`})),om=i(({isLoading:e,storyId:t,baseUrl:r,zoom:a,resetZoom:o,...c})=>n.createElement(rm,{...c},n.createElement(nm,{key:"left"},e?[1,2,3].map(l=>n.createElement(am,{key:l})):n.createElement(n.Fragment,null,n.createElement(K,{key:"zoomin",onClick:l=>{l.preventDefault(),a(.8)},title:"Zoom in"},n.createElement(Mo,null)),n.createElement(K,{key:"zoomout",onClick:l=>{l.preventDefault(),a(1.25)},title:"Zoom out"},n.createElement($o,null)),n.createElement(K,{key:"zoomreset",onClick:l=>{l.preventDefault(),o()},title:"Reset zoom"},n.createElement(Uo,null))))),"Toolbar"),lm=y.div(({isColumn:e,columns:t,layout:r})=>({display:e||!t?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:e?"column":"row","& .innerZoomElementWrapper > *":e?{width:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout:e="padded",inline:t})=>e==="centered"||e==="padded"?{padding:t?"32px 22px":"0px","& .innerZoomElementWrapper > *":{width:"auto",border:"8px solid transparent!important"}}:{},({layout:e="padded",inline:t})=>e==="centered"&&t?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns:e})=>e&&e>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${e} - 20px)`}}:{}),Kl=y(em)(({theme:e})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:e.appBorderRadius,borderBottomRightRadius:e.appBorderRadius,border:"none",background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content),color:e.color.lightest,button:{background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content)}})),im=y.div(({theme:e,withSource:t,isExpanded:r})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...on(e),borderBottomLeftRadius:t&&r&&0,borderBottomRightRadius:t&&r&&0,borderBottomWidth:r&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar:e})=>e&&{paddingTop:40}),um=i((e,t,r)=>{switch(!0){case!!(e&&e.error):return{source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:i(()=>r(!1),"onClick")}};case t:return{source:n.createElement(Kl,{...e,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:i(()=>r(!1),"onClick")}};default:return{source:n.createElement(Kl,{...e,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:i(()=>r(!0),"onClick")}}}},"getSource");function Qu(e){if(cr.count(e)===1){let t=e;if(t.props)return t.props.id}return null}i(Qu,"getStoryId");var sm=y(om)({position:"absolute",top:0,left:0,right:0,height:40}),cm=y.div({overflow:"hidden",position:"relative"}),dm=i(({isLoading:e,isColumn:t,columns:r,children:a,withSource:o,withToolbar:c=!1,isExpanded:l=!1,additionalActions:u,className:s,layout:d="padded",inline:m=!1,...f})=>{let[p,h]=R(l),{source:g,actionItem:b}=um(o,p,h),[E,v]=R(1),A=[s].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),S=o?[b]:[],[O,D]=R(u?[...u]:[]),k=[...S,...O],{window:I}=globalThis,F=H(async V=>{let{createCopyToClipboardFunction:z}=await Promise.resolve().then(()=>(U(),mo));z()},[]),$=i(V=>{let z=I.getSelection();z&&z.type==="Range"||(V.preventDefault(),O.filter(W=>W.title==="Copied").length===0&&F(g?.props.code??"").then(()=>{D([...O,{title:"Copied",onClick:i(()=>{},"onClick")}]),I.setTimeout(()=>D(O.filter(W=>W.title!=="Copied")),1500)}))},"onCopyCapture");return n.createElement(im,{withSource:o,withToolbar:c,...f,className:A.join(" ")},c&&n.createElement(sm,{isLoading:e,border:!0,zoom:V=>v(E*V),resetZoom:()=>v(1),storyId:Qu(a),baseUrl:"./iframe.html"}),n.createElement(tm.Provider,{value:{scale:E}},n.createElement(cm,{className:"docs-story",onCopyCapture:o&&$},n.createElement(lm,{isColumn:t||!Array.isArray(a),columns:r,layout:d,inline:m},n.createElement(En.Element,{centered:d==="centered",scale:m?E:1},Array.isArray(a)?a.map((V,z)=>n.createElement("div",{key:z},V)):n.createElement("div",null,a))),n.createElement(Mt,{actionItems:k}))),o&&p&&g)},"Preview"),v5=y(dm)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}})),I5=y.div(({theme:e})=>({marginRight:30,fontSize:`${e.typography.size.s1}px`,color:e.base==="light"?Y(.4,e.color.defaultText):Y(.6,e.color.defaultText)})),D5=y.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),B5=y.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),_5=y.div(Ze,({theme:e})=>({...on(e),margin:"25px 0 40px",padding:"30px 20px"})),j5=y.div(({theme:e})=>({fontWeight:e.typography.weight.bold,color:e.color.defaultText})),M5=y.div(({theme:e})=>({color:e.base==="light"?Y(.2,e.color.defaultText):Y(.6,e.color.defaultText)})),$5=y.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),U5=y.div(({theme:e})=>({flex:1,textAlign:"center",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,lineHeight:1,overflow:"hidden",color:e.base==="light"?Y(.4,e.color.defaultText):Y(.6,e.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),H5=y.div({display:"flex",flexDirection:"row"}),V5=y.div(({background:e})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:e,content:'""'}})),z5=y.div(({theme:e})=>({...on(e),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),q5=y.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),G5=y.div({flex:1,display:"flex",flexDirection:"row"}),W5=y.div({display:"flex",alignItems:"flex-start"}),K5=y.div({flex:"0 0 30%"}),Y5=y.div({flex:1}),J5=y.div(({theme:e})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:e.typography.weight.bold,color:e.base==="light"?Y(.4,e.color.defaultText):Y(.6,e.color.defaultText)})),X5=y.div(({theme:e})=>({fontSize:e.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"})),nS=y.div(({theme:e})=>({fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s1,color:e.color.defaultText,marginLeft:10,lineHeight:1.2,display:"-webkit-box",overflow:"hidden",wordBreak:"break-word",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical"})),aS=y.div(({theme:e})=>({...on(e),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),oS=y.div({display:"inline-flex",flexDirection:"row",alignItems:"center",width:"100%"}),lS=y.div({display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(140px, 1fr))",gridGap:"8px 16px",gridAutoFlow:"row dense",gridAutoRows:50}),fS=y.aside(()=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),hS=y.nav(({theme:e})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${e.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:e.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:e.color.secondary,textDecoration:"none"}})),gS=y.p(({theme:e})=>({fontWeight:600,fontSize:"0.875em",color:e.textColor,textTransform:"uppercase",marginBottom:10}));function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},lt.apply(this,arguments)}i(lt,"t");var pm=["children","options"],L={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},Yl;(function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"})(Yl||(Yl={}));var Jl=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:"className",for:"htmlFor"}),Xl={amp:"&",apos:"'",gt:">",lt:"<",nbsp:"\xA0",quot:"\u201C"},mm=["style","script"],fm=["src","href","data","formAction","srcDoc","action"],hm=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,gm=/mailto:/i,bm=/\n{2,}$/,es=/^(\s*>[\s\S]*?)(?=\n\n|$)/,ym=/^ *> ?/gm,Em=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,vm=/^ {2,}\n/,xm=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,ts=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,rs=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,Am=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,Cm=/^(?:\n *)*\n/,Sm=/\r\n?/g,wm=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,km=/^\[\^([^\]]+)]/,Om=/\f/g,Tm=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,Im=/^\s*?\[(x|\s)\]/,ns=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,as=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,os=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,sa=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,Dm=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,ls=/^<!--[\s\S]*?(?:-->)/,Bm=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,ca=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,_m=/^\{.*\}$/,Rm=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,Nm=/^<([^ >]+@[^ >]+)>/,Lm=/^<([^ >]+:\/[^ >]+)>/,Fm=/-([a-z])?/gi,is=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,Pm=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,jm=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,Mm=/^\[([^\]]*)\] ?\[([^\]]*)\]/,$m=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,Um=/\t/g,Hm=/(^ *\||\| *$)/g,Vm=/^ *:-+: *$/,zm=/^ *:-+ *$/,qm=/^ *-+: *$/,ln="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",Gm=new RegExp(`^([*_])\\1${ln}\\1\\1(?!\\1)`),Wm=new RegExp(`^([*_])${ln}\\1(?!\\1)`),Km=new RegExp(`^(==)${ln}\\1`),Ym=new RegExp(`^(~~)${ln}\\1`),Jm=/^\\([^0-9A-Za-z\s])/,Zl=/\\([^0-9A-Za-z\s])/g,Xm=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,Zm=/^\n+/,Qm=/^([ \t]*)/,ef=/\\([^\\])/g,tf=/(?:^|\n)( *)$/,$a="(?:\\d+\\.)",Ua="(?:[*+-])";function Ha(e){return"( *)("+(e===1?$a:Ua)+") +"}i(Ha,"de");var us=Ha(1),ss=Ha(2);function Va(e){return new RegExp("^"+(e===1?us:ss))}i(Va,"fe");var rf=Va(1),nf=Va(2);function za(e){return new RegExp("^"+(e===1?us:ss)+"[^\\n]*(?:\\n(?!\\1"+(e===1?$a:Ua)+" )[^\\n]*)*(\\n|$)","gm")}i(za,"ge");var af=za(1),of=za(2);function qa(e){let t=e===1?$a:Ua;return new RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}i(qa,"xe");var cs=qa(1),ds=qa(2);function da(e,t){let r=t===1,a=r?cs:ds,o=r?af:of,c=r?rf:nf;return{match:yt(function(l,u){let s=tf.exec(u.prevCapture);return s&&(u.list||!u.inline&&!u.simple)?a.exec(l=s[1]+l):null}),order:1,parse(l,u,s){let d=r?+l[2]:void 0,m=l[0].replace(bm,`
`).match(o),f=!1;return{items:m.map(function(p,h){let g=c.exec(p)[0].length,b=new RegExp("^ {1,"+g+"}","gm"),E=p.replace(b,"").replace(c,""),v=h===m.length-1,A=E.indexOf(`

`)!==-1||v&&f;f=A;let S=s.inline,O=s.list,D;s.list=!0,A?(s.inline=!1,D=Lt(E)+`

`):(s.inline=!0,D=Lt(E));let k=u(D,s);return s.inline=S,s.list=O,k}),ordered:r,start:d}},render:i((l,u,s)=>e(l.ordered?"ol":"ul",{key:s.key,start:l.type===L.orderedList?l.start:void 0},l.items.map(function(d,m){return e("li",{key:m},u(d,s))})),"render")}}i(da,"Ce");var lf=new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`),uf=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,ps=[es,ts,rs,ns,os,as,is,cs,ds],sf=[...ps,/^[^\n]+(?:  \n|\n{2,})/,sa,ls,ca];function Lt(e){let t=e.length;for(;t>0&&e[t-1]<=" ";)t--;return e.slice(0,t)}i(Lt,"ze");function It(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}i(It,"Le");function ms(e){return qm.test(e)?"right":Vm.test(e)?"center":zm.test(e)?"left":null}i(ms,"Ae");function pa(e,t,r,a){let o=r.inTable;r.inTable=!0;let c=[[]],l="";function u(){if(!l)return;let s=c[c.length-1];s.push.apply(s,t(l,r)),l=""}return i(u,"a"),e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((s,d,m)=>{s.trim()==="|"&&(u(),a)?d!==0&&d!==m.length-1&&c.push([]):l+=s}),u(),r.inTable=o,c}i(pa,"Oe");function fs(e,t,r){r.inline=!0;let a=e[2]?e[2].replace(Hm,"").split("|").map(ms):[],o=e[3]?function(l,u,s){return l.trim().split(`
`).map(function(d){return pa(d,u,s,!0)})}(e[3],t,r):[],c=pa(e[1],t,r,!!o.length);return r.inline=!1,o.length?{align:a,cells:o,header:c,type:L.table}:{children:c,type:L.paragraph}}i(fs,"Te");function ma(e,t){return e.align[t]==null?{}:{textAlign:e.align[t]}}i(ma,"Be");function yt(e){return e.inline=1,e}i(yt,"Me");function We(e){return yt(function(t,r){return r.inline?e.exec(t):null})}i(We,"Re");function Ke(e){return yt(function(t,r){return r.inline||r.simple?e.exec(t):null})}i(Ke,"Ie");function ze(e){return function(t,r){return r.inline||r.simple?null:e.exec(t)}}i(ze,"De");function Dt(e){return yt(function(t){return e.exec(t)})}i(Dt,"Ue");function hs(e,t){if(t.inline||t.simple)return null;let r="";e.split(`
`).every(o=>(o+=`
`,!ps.some(c=>c.test(o))&&(r+=o,!!o.trim())));let a=Lt(r);return a==""?null:[r,,a]}i(hs,"Ne");var cf=/(javascript|vbscript|data(?!:image)):/i;function gs(e){try{let t=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(cf.test(t))return null}catch{return null}return e}i(gs,"He");function fa(e){return e.replace(ef,"$1")}i(fa,"Pe");function Zt(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!0,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(Zt,"_e");function bs(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!1,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(bs,"Fe");function ys(e,t,r){let a=r.inline||!1;r.inline=!1;let o=e(t,r);return r.inline=a,o}i(ys,"We");var Vn=i((e,t,r)=>({children:Zt(t,e[2],r)}),"Ge");function Hr(){return{}}i(Hr,"Ze");function Vr(){return null}i(Vr,"qe");function Es(...e){return e.filter(Boolean).join(" ")}i(Es,"Qe");function zr(e,t,r){let a=e,o=t.split(".");for(;o.length&&(a=a[o[0]],a!==void 0);)o.shift();return a||r}i(zr,"Ve");function vs(e="",t={}){function r(p,h,...g){let b=zr(t.overrides,`${p}.props`,{});return t.createElement(function(E,v){let A=zr(v,E);return A?typeof A=="function"||typeof A=="object"&&"render"in A?A:zr(v,`${E}.component`,E):E}(p,t.overrides),lt({},h,b,{className:Es(h?.className,b.className)||void 0}),...g)}i(r,"u");function a(p){p=p.replace(Tm,"");let h=!1;t.forceInline?h=!0:t.forceBlock||(h=$m.test(p)===!1);let g=d(s(h?p:`${Lt(p).replace(Zm,"")}

`,{inline:h}));for(;typeof g[g.length-1]=="string"&&!g[g.length-1].trim();)g.pop();if(t.wrapper===null)return g;let b=t.wrapper||(h?"span":"div"),E;if(g.length>1||t.forceWrapper)E=g;else{if(g.length===1)return E=g[0],typeof E=="string"?r("span",{key:"outer"},E):E;E=null}return t.createElement(b,{key:"outer"},E)}i(a,"Z");function o(p,h){let g=h.match(hm);return g?g.reduce(function(b,E){let v=E.indexOf("=");if(v!==-1){let A=function(k){return k.indexOf("-")!==-1&&k.match(Bm)===null&&(k=k.replace(Fm,function(I,F){return F.toUpperCase()})),k}(E.slice(0,v)).trim(),S=function(k){let I=k[0];return(I==='"'||I==="'")&&k.length>=2&&k[k.length-1]===I?k.slice(1,-1):k}(E.slice(v+1).trim()),O=Jl[A]||A;if(O==="ref")return b;let D=b[O]=function(k,I,F,$){return I==="style"?function(V){let z=[],W="",M=!1,x=!1,C="";if(!V)return z;for(let B=0;B<V.length;B++){let T=V[B];if(T!=='"'&&T!=="'"||M||(x?T===C&&(x=!1,C=""):(x=!0,C=T)),T==="("&&W.endsWith("url")?M=!0:T===")"&&M&&(M=!1),T!==";"||x||M)W+=T;else{let _=W.trim();if(_){let N=_.indexOf(":");if(N>0){let q=_.slice(0,N).trim(),Z=_.slice(N+1).trim();z.push([q,Z])}}W=""}}let w=W.trim();if(w){let B=w.indexOf(":");if(B>0){let T=w.slice(0,B).trim(),_=w.slice(B+1).trim();z.push([T,_])}}return z}(F).reduce(function(V,[z,W]){return V[z.replace(/(-[a-z])/g,M=>M[1].toUpperCase())]=$(W,k,z),V},{}):fm.indexOf(I)!==-1?$(F,k,I):(F.match(_m)&&(F=F.slice(1,F.length-1)),F==="true"||F!=="false"&&F)}(p,A,S,t.sanitizer);typeof D=="string"&&(sa.test(D)||ca.test(D))&&(b[O]=a(D.trim()))}else E!=="style"&&(b[Jl[E]||E]=!0);return b},{}):null}i(o,"q"),t.overrides=t.overrides||{},t.sanitizer=t.sanitizer||gs,t.slugify=t.slugify||It,t.namedCodesToUnicode=t.namedCodesToUnicode?lt({},Xl,t.namedCodesToUnicode):Xl,t.createElement=t.createElement||P;let c=[],l={},u={[L.blockQuote]:{match:ze(es),order:1,parse(p,h,g){let[,b,E]=p[0].replace(ym,"").match(Em);return{alert:b,children:h(E,g)}},render(p,h,g){let b={key:g.key};return p.alert&&(b.className="markdown-alert-"+t.slugify(p.alert.toLowerCase(),It),p.children.unshift({attrs:{},children:[{type:L.text,text:p.alert}],noInnerParse:!0,type:L.htmlBlock,tag:"header"})),r("blockquote",b,h(p.children,g))}},[L.breakLine]:{match:Dt(vm),order:1,parse:Hr,render:i((p,h,g)=>r("br",{key:g.key}),"render")},[L.breakThematic]:{match:ze(xm),order:1,parse:Hr,render:i((p,h,g)=>r("hr",{key:g.key}),"render")},[L.codeBlock]:{match:ze(rs),order:0,parse:i(p=>({lang:void 0,text:Lt(p[0].replace(/^ {4}/gm,"")).replace(Zl,"$1")}),"parse"),render:i((p,h,g)=>r("pre",{key:g.key},r("code",lt({},p.attrs,{className:p.lang?`lang-${p.lang}`:""}),p.text)),"render")},[L.codeFenced]:{match:ze(ts),order:0,parse:i(p=>({attrs:o("code",p[3]||""),lang:p[2]||void 0,text:p[4],type:L.codeBlock}),"parse")},[L.codeInline]:{match:Ke(Am),order:3,parse:i(p=>({text:p[2].replace(Zl,"$1")}),"parse"),render:i((p,h,g)=>r("code",{key:g.key},p.text),"render")},[L.footnote]:{match:ze(wm),order:0,parse:i(p=>(c.push({footnote:p[2],identifier:p[1]}),{}),"parse"),render:Vr},[L.footnoteReference]:{match:We(km),order:1,parse:i(p=>({target:`#${t.slugify(p[1],It)}`,text:p[1]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href")},r("sup",{key:g.key},p.text)),"render")},[L.gfmTask]:{match:We(Im),order:1,parse:i(p=>({completed:p[1].toLowerCase()==="x"}),"parse"),render:i((p,h,g)=>r("input",{checked:p.completed,key:g.key,readOnly:!0,type:"checkbox"}),"render")},[L.heading]:{match:ze(t.enforceAtxHeadings?as:ns),order:1,parse:i((p,h,g)=>({children:Zt(h,p[2],g),id:t.slugify(p[2],It),level:p[1].length}),"parse"),render:i((p,h,g)=>r(`h${p.level}`,{id:p.id,key:g.key},h(p.children,g)),"render")},[L.headingSetext]:{match:ze(os),order:0,parse:i((p,h,g)=>({children:Zt(h,p[1],g),level:p[2]==="="?1:2,type:L.heading}),"parse")},[L.htmlBlock]:{match:Dt(sa),order:1,parse(p,h,g){let[,b]=p[3].match(Qm),E=new RegExp(`^${b}`,"gm"),v=p[3].replace(E,""),A=(S=v,sf.some(F=>F.test(S))?ys:Zt);var S;let O=p[1].toLowerCase(),D=mm.indexOf(O)!==-1,k=(D?O:p[1]).trim(),I={attrs:o(k,p[2]),noInnerParse:D,tag:k};return g.inAnchor=g.inAnchor||O==="a",D?I.text=p[3]:I.children=A(h,v,g),g.inAnchor=!1,I},render:i((p,h,g)=>r(p.tag,lt({key:g.key},p.attrs),p.text||(p.children?h(p.children,g):"")),"render")},[L.htmlSelfClosing]:{match:Dt(ca),order:1,parse(p){let h=p[1].trim();return{attrs:o(h,p[2]||""),tag:h}},render:i((p,h,g)=>r(p.tag,lt({},p.attrs,{key:g.key})),"render")},[L.htmlComment]:{match:Dt(ls),order:1,parse:i(()=>({}),"parse"),render:Vr},[L.image]:{match:Ke(uf),order:1,parse:i(p=>({alt:p[1],target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("img",{key:g.key,alt:p.alt||void 0,title:p.title||void 0,src:t.sanitizer(p.target,"img","src")}),"render")},[L.link]:{match:We(lf),order:3,parse:i((p,h,g)=>({children:bs(h,p[1],g),target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href"),title:p.title},h(p.children,g)),"render")},[L.linkAngleBraceStyleDetector]:{match:We(Lm),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],type:L.link}),"parse")},[L.linkBareUrlDetector]:{match:yt((p,h)=>h.inAnchor||t.disableAutoLink?null:We(Rm)(p,h)),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],title:void 0,type:L.link}),"parse")},[L.linkMailtoDetector]:{match:We(Nm),order:0,parse(p){let h=p[1],g=p[1];return gm.test(g)||(g="mailto:"+g),{children:[{text:h.replace("mailto:",""),type:L.text}],target:g,type:L.link}}},[L.orderedList]:da(r,1),[L.unorderedList]:da(r,2),[L.newlineCoalescer]:{match:ze(Cm),order:3,parse:Hr,render:i(()=>`
`,"render")},[L.paragraph]:{match:yt(hs),order:3,parse:Vn,render:i((p,h,g)=>r("p",{key:g.key},h(p.children,g)),"render")},[L.ref]:{match:We(Pm),order:0,parse:i(p=>(l[p[1]]={target:p[2],title:p[4]},{}),"parse"),render:Vr},[L.refImage]:{match:Ke(jm),order:0,parse:i(p=>({alt:p[1]||void 0,ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("img",{key:g.key,alt:p.alt,src:t.sanitizer(l[p.ref].target,"img","src"),title:l[p.ref].title}):null,"render")},[L.refLink]:{match:We(Mm),order:0,parse:i((p,h,g)=>({children:h(p[1],g),fallbackChildren:p[0],ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("a",{key:g.key,href:t.sanitizer(l[p.ref].target,"a","href"),title:l[p.ref].title},h(p.children,g)):r("span",{key:g.key},p.fallbackChildren),"render")},[L.table]:{match:ze(is),order:1,parse:fs,render(p,h,g){let b=p;return r("table",{key:g.key},r("thead",null,r("tr",null,b.header.map(function(E,v){return r("th",{key:v,style:ma(b,v)},h(E,g))}))),r("tbody",null,b.cells.map(function(E,v){return r("tr",{key:v},E.map(function(A,S){return r("td",{key:S,style:ma(b,S)},h(A,g))}))})))}},[L.text]:{match:Dt(Xm),order:4,parse:i(p=>({text:p[0].replace(Dm,(h,g)=>t.namedCodesToUnicode[g]?t.namedCodesToUnicode[g]:h)}),"parse"),render:i(p=>p.text,"render")},[L.textBolded]:{match:Ke(Gm),order:2,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("strong",{key:g.key},h(p.children,g)),"render")},[L.textEmphasized]:{match:Ke(Wm),order:3,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("em",{key:g.key},h(p.children,g)),"render")},[L.textEscaped]:{match:Ke(Jm),order:1,parse:i(p=>({text:p[1],type:L.text}),"parse")},[L.textMarked]:{match:Ke(Km),order:3,parse:Vn,render:i((p,h,g)=>r("mark",{key:g.key},h(p.children,g)),"render")},[L.textStrikethroughed]:{match:Ke(Ym),order:3,parse:Vn,render:i((p,h,g)=>r("del",{key:g.key},h(p.children,g)),"render")}};t.disableParsingRawHTML===!0&&(delete u[L.htmlBlock],delete u[L.htmlSelfClosing]);let s=function(p){let h=Object.keys(p);function g(b,E){let v,A,S=[],O="",D="";for(E.prevCapture=E.prevCapture||"";b;){let k=0;for(;k<h.length;){if(O=h[k],v=p[O],E.inline&&!v.match.inline){k++;continue}let I=v.match(b,E);if(I){D=I[0],E.prevCapture+=D,b=b.substring(D.length),A=v.parse(I,g,E),A.type==null&&(A.type=O),S.push(A);break}k++}}return E.prevCapture="",S}return i(g,"n"),h.sort(function(b,E){let v=p[b].order,A=p[E].order;return v!==A?v-A:b<E?-1:1}),function(b,E){return g(function(v){return v.replace(Sm,`
`).replace(Om,"").replace(Um,"    ")}(b),E)}}(u),d=(m=function(p,h){return function(g,b,E){let v=p[g.type].render;return h?h(()=>v(g,b,E),g,b,E):v(g,b,E)}}(u,t.renderRule),i(function p(h,g={}){if(Array.isArray(h)){let b=g.key,E=[],v=!1;for(let A=0;A<h.length;A++){g.key=A;let S=p(h[A],g),O=typeof S=="string";O&&v?E[E.length-1]+=S:S!==null&&E.push(S),v=O}return g.key=b,E}return m(h,p,g)},"e"));var m;let f=a(e);return c.length?r("div",null,f,r("footer",{key:"footer"},c.map(function(p){return r("div",{id:t.slugify(p.identifier,It),key:p.identifier},p.identifier,d(s(p.footnote,{inline:!0})))}))):f}i(vs,"Xe");var df=i(e=>{let{children:t="",options:r}=e,a=function(o,c){if(o==null)return{};var l,u,s={},d=Object.keys(o);for(u=0;u<d.length;u++)c.indexOf(l=d[u])>=0||(s[l]=o[l]);return s}(e,pm);return ae(vs(t,r),a)},"default");je();var pf=y.label(({theme:e})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:e.boolean.background,borderRadius:"3em",padding:1,'&[aria-disabled="true"]':{opacity:.5,input:{cursor:"not-allowed"}},input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${e.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:Y(.5,e.color.defaultText),background:"transparent","&:hover":{boxShadow:`${Xt(.3,e.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${Xt(.05,e.appBorderColor)} 0 0 0 2px inset`,color:Xt(1,e.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:e.boolean.selectedBackground,boxShadow:e.base==="light"?`${Xt(.1,e.appBorderColor)} 0 0 2px`:`${e.appBorderColor} 0 0 0 1px`,color:e.color.defaultText,padding:"7px 15px"}})),mf=i(e=>e==="true","parse"),ff=i(({name:e,value:t,onChange:r,onBlur:a,onFocus:o,argType:c})=>{let l=H(()=>r(!1),[r]),u=!!c?.table?.readonly;if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:l,disabled:u},"Set boolean");let s=ke(e),d=typeof t=="string"?mf(t):t;return n.createElement(pf,{"aria-disabled":u,htmlFor:s,"aria-label":e},n.createElement("input",{id:s,type:"checkbox",onChange:m=>r(m.target.checked),checked:d,role:"switch",disabled:u,name:e,onBlur:a,onFocus:o}),n.createElement("span",{"aria-hidden":"true"},"False"),n.createElement("span",{"aria-hidden":"true"},"True"))},"BooleanControl");je();var hf=i(e=>{let[t,r,a]=e.split("-"),o=new Date;return o.setFullYear(parseInt(t,10),parseInt(r,10)-1,parseInt(a,10)),o},"parseDate"),gf=i(e=>{let[t,r]=e.split(":"),a=new Date;return a.setHours(parseInt(t,10)),a.setMinutes(parseInt(r,10)),a},"parseTime"),bf=i(e=>{let t=new Date(e),r=`000${t.getFullYear()}`.slice(-4),a=`0${t.getMonth()+1}`.slice(-2),o=`0${t.getDate()}`.slice(-2);return`${r}-${a}-${o}`},"formatDate"),yf=i(e=>{let t=new Date(e),r=`0${t.getHours()}`.slice(-2),a=`0${t.getMinutes()}`.slice(-2);return`${r}:${a}`},"formatTime"),Ql=y(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),Ef=y.div(({theme:e})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:e.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),vf=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,argType:c})=>{let[l,u]=R(!0),s=Q(),d=Q(),m=!!c?.table?.readonly;j(()=>{l!==!1&&(s&&s.current&&(s.current.value=t?bf(t):""),d&&d.current&&(d.current.value=t?yf(t):""))},[t]);let f=i(g=>{if(!g.target.value)return r();let b=hf(g.target.value),E=new Date(t??"");E.setFullYear(b.getFullYear(),b.getMonth(),b.getDate());let v=E.getTime();v&&r(v),u(!!v)},"onDateChange"),p=i(g=>{if(!g.target.value)return r();let b=gf(g.target.value),E=new Date(t??"");E.setHours(b.getHours()),E.setMinutes(b.getMinutes());let v=E.getTime();v&&r(v),u(!!v)},"onTimeChange"),h=ke(e);return n.createElement(Ef,null,n.createElement(Ql,{type:"date",max:"9999-12-31",ref:s,id:`${h}-date`,name:`${h}-date`,readOnly:m,onChange:f,onFocus:a,onBlur:o}),n.createElement(Ql,{type:"time",id:`${h}-time`,name:`${h}-time`,ref:d,onChange:p,readOnly:m,onFocus:a,onBlur:o}),l?null:n.createElement("div",null,"invalid"))},"DateControl");je();var xf=y.label({display:"flex"}),Af=i(e=>{let t=parseFloat(e);return Number.isNaN(t)?void 0:t},"parse"),Cf=y(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),Sf=i(({name:e,value:t,onChange:r,min:a,max:o,step:c,onBlur:l,onFocus:u,argType:s})=>{let[d,m]=R(typeof t=="number"?t:""),[f,p]=R(!1),[h,g]=R(null),b=!!s?.table?.readonly,E=H(S=>{m(S.target.value);let O=parseFloat(S.target.value);Number.isNaN(O)?g(new Error(`'${S.target.value}' is not a number`)):(r(O),g(null))},[r,g]),v=H(()=>{m("0"),r(0),p(!0)},[p]),A=Q(null);return j(()=>{f&&A.current&&A.current.select()},[f]),j(()=>{let S=typeof t=="number"?t:"";d!==S&&m(S)},[t]),t===void 0?n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:v,disabled:b},"Set number"):n.createElement(xf,null,n.createElement(Cf,{ref:A,id:ke(e),type:"number",onChange:E,size:"flex",placeholder:"Edit number...",value:d,valid:h?"error":void 0,autoFocus:f,readOnly:b,name:e,min:a,max:o,step:c,onFocus:u,onBlur:l}))},"NumberControl");je();var xs=i((e,t)=>{let r=t&&Object.entries(t).find(([a,o])=>o===e);return r?r[0]:void 0},"selectedKey"),ha=i((e,t)=>e&&t?Object.entries(t).filter(r=>e.includes(r[1])).map(r=>r[0]):[],"selectedKeys"),As=i((e,t)=>e&&t&&e.map(r=>t[r]),"selectedValues"),wf=y.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),kf=y.span({"[aria-readonly=true] &":{opacity:.5}}),Of=y.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ei=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Checkbox with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=ha(r||[],t),[u,s]=R(l),d=!!c?.table?.readonly,m=i(p=>{let h=p.target.value,g=[...u];g.includes(h)?g.splice(g.indexOf(h),1):g.push(h),a(As(g,t)),s(g)},"handleChange");j(()=>{s(ha(r||[],t))},[r]);let f=ke(e);return n.createElement(wf,{"aria-readonly":d,isInline:o},Object.keys(t).map((p,h)=>{let g=`${f}-${h}`;return n.createElement(Of,{key:g,htmlFor:g},n.createElement("input",{type:"checkbox",disabled:d,id:g,name:g,value:p,onChange:m,checked:u?.includes(p)}),n.createElement(kf,null,p))}))},"CheckboxControl");je();var Tf=y.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),If=y.span({"[aria-readonly=true] &":{opacity:.5}}),Df=y.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ti=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Radio with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=xs(r,t),u=ke(e),s=!!c?.table?.readonly;return n.createElement(Tf,{"aria-readonly":s,isInline:o},Object.keys(t).map((d,m)=>{let f=`${u}-${m}`;return n.createElement(Df,{key:f,htmlFor:f},n.createElement("input",{type:"radio",id:f,name:u,disabled:s,value:d,onChange:p=>a(t[p.currentTarget.value]),checked:d===l}),n.createElement(If,null,d))}))},"RadioControl");je();var Bf={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},Cs=y.select(Bf,({theme:e})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:e.input.color||"inherit",background:e.input.background,borderRadius:e.input.borderRadius,boxShadow:`${e.input.border} 0 0 0 1px inset`,fontSize:e.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${e.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:e.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),Ss=y.span(({theme:e})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:e.textMutedColor,path:{fill:e.textMutedColor}}})),ri="Choose option...",_f=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{a(r[d.currentTarget.value])},"handleChange"),l=xs(t,r)||ri,u=ke(e),s=!!o?.table?.readonly;return n.createElement(Ss,null,n.createElement(gr,null),n.createElement(Cs,{disabled:s,id:u,value:l,onChange:c},n.createElement("option",{key:"no-selection",disabled:!0},ri),Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"SingleSelect"),Rf=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{let m=Array.from(d.currentTarget.options).filter(f=>f.selected).map(f=>f.value);a(As(m,r))},"handleChange"),l=ha(t,r),u=ke(e),s=!!o?.table?.readonly;return n.createElement(Ss,null,n.createElement(Cs,{disabled:s,id:u,multiple:!0,value:l,onChange:c},Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"MultiSelect"),ni=i(e=>{let{name:t,options:r}=e;return r?e.isMulti?n.createElement(Rf,{...e}):n.createElement(_f,{...e}):(tt.warn(`Select with no options: ${t}`),n.createElement(n.Fragment,null,"-"))},"SelectControl"),Nf=i((e,t)=>Array.isArray(e)?e.reduce((r,a)=>(r[t?.[a]||String(a)]=a,r),{}):e,"normalizeOptions"),Lf={check:ei,"inline-check":ei,radio:ti,"inline-radio":ti,select:ni,"multi-select":ni},Ot=i(e=>{let{type:t="select",labels:r,argType:a}=e,o={...e,argType:a,options:a?Nf(a.options,r):{},isInline:t.includes("inline"),isMulti:t.includes("multi")},c=Lf[t];if(c)return n.createElement(c,{...o});throw new Error(`Unknown options type: ${t}`)},"OptionsControl");an();je();var Ff="Error",Pf="Object",jf="Array",Mf="String",$f="Number",Uf="Boolean",Hf="Date",Vf="Null",zf="Undefined",qf="Function",Gf="Symbol",ws="ADD_DELTA_TYPE",ks="REMOVE_DELTA_TYPE",Os="UPDATE_DELTA_TYPE",Ga="value",Wf="key";function it(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&typeof e[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(e).slice(8,-1)}i(it,"getObjectType");function Wa(e,t){let r=it(e),a=it(t);return(r==="Function"||a==="Function")&&a!==r}i(Wa,"isComponentWillChange");var Ts=class extends Re{constructor(t){super(t),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this)}componentDidMount(){let{inputRefKey:t,inputRefValue:r}=this.state,{onlyValue:a}=this.props;t&&typeof t.focus=="function"&&t.focus(),a&&r&&typeof r.focus=="function"&&r.focus(),document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.onSubmit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.props.handleCancel()))}onSubmit(){let{handleAdd:t,onlyValue:r,onSubmitValueParser:a,keyPath:o,deep:c}=this.props,{inputRefKey:l,inputRefValue:u}=this.state,s={};if(!r){if(!l.value)return;s.key=l.value}s.newValue=a(!1,o,c,s.key,u.value),t(s)}refInputKey(t){this.state.inputRefKey=t}refInputValue(t){this.state.inputRefValue=t}render(){let{handleCancel:t,onlyValue:r,addButtonElement:a,cancelButtonElement:o,inputElementGenerator:c,keyPath:l,deep:u}=this.props,s=a&&ae(a,{onClick:this.onSubmit}),d=o&&ae(o,{onClick:t}),m=c(Ga,l,u),f=ae(m,{placeholder:"Value",ref:this.refInputValue}),p=null;if(!r){let h=c(Wf,l,u);p=ae(h,{placeholder:"Key",ref:this.refInputKey})}return n.createElement("span",{className:"rejt-add-value-node"},p,f,d,s)}};i(Ts,"JsonAddValue");var Ka=Ts;Ka.defaultProps={onlyValue:!1,addButtonElement:n.createElement("button",null,"+"),cancelButtonElement:n.createElement("button",null,"c")};var Is=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={data:t.data,name:t.name,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleRemoveItem(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c,nextDeep:l}=this.state,u=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,u).then(()=>{let s={keyPath:c,deep:l,key:t,oldValue:u,type:ks};o.splice(t,1),this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(s)}).catch(a.error)}}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:u}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:s,onDeltaUpdate:d}=this.props;s(o[o.length-1],a),d({type:ws,keyPath:o,deep:c,key:t,newValue:r})}).catch(u.error)}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:u,nextDeep:s}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,u,s,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(u[u.length-1],l),f({type:Os,keyPath:u,deep:s,key:t,newValue:r,oldValue:d}),a(void 0)}).catch(o)})}renderCollapsed(){let{name:t,data:r,keyPath:a,deep:o}=this.state,{handleRemove:c,readOnly:l,getStyle:u,dataType:s,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=u(t,r,a,o,s),p=l(t,r,a,o,s),h=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"[...] ",r.length," ",r.length===1?"item":"items"),!p&&h)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,addFormVisible:c,nextDeep:l}=this.state,{isCollapsed:u,handleRemove:s,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:b,inputElementGenerator:E,textareaElementGenerator:v,minusMenuElement:A,plusMenuElement:S,beforeRemoveAction:O,beforeAddAction:D,beforeUpdateAction:k,logger:I,onSubmitValueParser:F}=this.props,{minus:$,plus:V,delimiter:z,ul:W,addForm:M}=f(t,r,a,o,p),x=m(t,r,a,o,p),C=S&&ae(S,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:V}),w=A&&ae(A,{onClick:s,className:"rejt-minus-menu",style:$});return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"["),!c&&C,n.createElement("ul",{className:"rejt-not-collapsed-list",style:W},r.map((B,T)=>n.createElement(un,{key:T,name:T.toString(),data:B,keyPath:a,deep:l,isCollapsed:u,handleRemove:this.handleRemoveItem(T),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:b,inputElementGenerator:E,textareaElementGenerator:v,minusMenuElement:A,plusMenuElement:S,beforeRemoveAction:O,beforeAddAction:D,beforeUpdateAction:k,logger:I,onSubmitValueParser:F}))),!x&&c&&n.createElement("div",{className:"rejt-add-form",style:M},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:E,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"]"),!x&&w)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{dataType:l,getStyle:u}=this.props,s=r?this.renderCollapsed():this.renderNotCollapsed(),d=u(t,a,o,c,l);return n.createElement("div",{className:"rejt-array-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),s)}};i(Is,"JsonArray");var Ds=Is;Ds.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var Bs=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:u,dataType:s}=this.props,d=u(a,o,c,l,s);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:u,deep:s}=this.state;if(!l)return;let d=o(!0,c,s,u,l.value),m={value:d,key:u};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:u,readOnly:s,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,textareaElementGenerator:h,minusMenuElement:g,keyPath:b}=this.props,E=m(t,u,o,c,d),v=null,A=null,S=s(t,u,o,c,d);if(a&&!S){let O=h(Ga,b,c,t,u,d),D=f&&ae(f,{onClick:this.handleEdit}),k=p&&ae(p,{onClick:this.handleCancelEdit}),I=ae(O,{ref:this.refInput,defaultValue:u});v=n.createElement("span",{className:"rejt-edit-form",style:E.editForm},I," ",k,D),A=null}else{v=n.createElement("span",{className:"rejt-value",style:E.value,onClick:S?void 0:this.handleEditMode},r);let O=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:E.minus});A=S?null:O}return n.createElement("li",{className:"rejt-function-value-node",style:E.li},n.createElement("span",{className:"rejt-name",style:E.name},t," :"," "),v,A)}};i(Bs,"JsonFunctionValue");var _s=Bs;_s.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>{},"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};var Rs=class extends Re{constructor(t){super(t),this.state={data:t.data,name:t.name,keyPath:t.keyPath,deep:t.deep}}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}render(){let{data:t,name:r,keyPath:a,deep:o}=this.state,{isCollapsed:c,handleRemove:l,handleUpdateValue:u,onUpdate:s,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,textareaElementGenerator:E,minusMenuElement:v,plusMenuElement:A,beforeRemoveAction:S,beforeAddAction:O,beforeUpdateAction:D,logger:k,onSubmitValueParser:I}=this.props,F=i(()=>!0,"readOnlyTrue"),$=it(t);switch($){case Ff:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:F,dataType:$,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,textareaElementGenerator:E,minusMenuElement:v,plusMenuElement:A,beforeRemoveAction:S,beforeAddAction:O,beforeUpdateAction:D,logger:k,onSubmitValueParser:I});case Pf:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:m,dataType:$,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,textareaElementGenerator:E,minusMenuElement:v,plusMenuElement:A,beforeRemoveAction:S,beforeAddAction:O,beforeUpdateAction:D,logger:k,onSubmitValueParser:I});case jf:return n.createElement(Ds,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:m,dataType:$,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,textareaElementGenerator:E,minusMenuElement:v,plusMenuElement:A,beforeRemoveAction:S,beforeAddAction:O,beforeUpdateAction:D,logger:k,onSubmitValueParser:I});case Mf:return n.createElement(nt,{name:r,value:`"${t}"`,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case $f:return n.createElement(nt,{name:r,value:t,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case Uf:return n.createElement(nt,{name:r,value:t?"true":"false",originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case Hf:return n.createElement(nt,{name:r,value:t.toISOString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:F,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case Vf:return n.createElement(nt,{name:r,value:"null",originalValue:"null",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case zf:return n.createElement(nt,{name:r,value:"undefined",originalValue:"undefined",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});case qf:return n.createElement(_s,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,textareaElementGenerator:E,minusMenuElement:v,logger:k,onSubmitValueParser:I});case Gf:return n.createElement(nt,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:F,dataType:$,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:b,minusMenuElement:v,logger:k,onSubmitValueParser:I});default:return null}}};i(Rs,"JsonNode");var un=Rs;un.defaultProps={keyPath:[],deep:0};var Ns=class extends Re{constructor(t){super(t);let r=t.deep===-1?[]:[...t.keyPath||[],t.name];this.state={name:t.name,data:t.data,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:u}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:s,onDeltaUpdate:d}=this.props;s(o[o.length-1],a),d({type:ws,keyPath:o,deep:c,key:t,newValue:r})}).catch(u.error)}handleRemoveValue(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c=[],nextDeep:l}=this.state,u=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,u).then(()=>{let s={keyPath:c,deep:l,key:t,oldValue:u,type:ks};delete o[t],this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(s)}).catch(a.error)}}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:u=[],nextDeep:s}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,u,s,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(u[u.length-1],l),f({type:Os,keyPath:u,deep:s,key:t,newValue:r,oldValue:d}),a()}).catch(o)})}renderCollapsed(){let{name:t,keyPath:r,deep:a,data:o}=this.state,{handleRemove:c,readOnly:l,dataType:u,getStyle:s,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=s(t,o,r,a,u),p=Object.getOwnPropertyNames(o),h=l(t,o,r,a,u),g=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"{...}"," ",p.length," ",p.length===1?"key":"keys"),!h&&g)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,nextDeep:c,addFormVisible:l}=this.state,{isCollapsed:u,handleRemove:s,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:b,inputElementGenerator:E,textareaElementGenerator:v,minusMenuElement:A,plusMenuElement:S,beforeRemoveAction:O,beforeAddAction:D,beforeUpdateAction:k,logger:I,onSubmitValueParser:F}=this.props,{minus:$,plus:V,addForm:z,ul:W,delimiter:M}=f(t,r,a,o,p),x=Object.getOwnPropertyNames(r),C=m(t,r,a,o,p),w=S&&ae(S,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:V}),B=A&&ae(A,{onClick:s,className:"rejt-minus-menu",style:$}),T=x.map(_=>n.createElement(un,{key:_,name:_,data:r[_],keyPath:a,deep:c,isCollapsed:u,handleRemove:this.handleRemoveValue(_),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:b,inputElementGenerator:E,textareaElementGenerator:v,minusMenuElement:A,plusMenuElement:S,beforeRemoveAction:O,beforeAddAction:D,beforeUpdateAction:k,logger:I,onSubmitValueParser:F}));return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:M},"{"),!C&&w,n.createElement("ul",{className:"rejt-not-collapsed-list",style:W},T),!C&&l&&n.createElement("div",{className:"rejt-add-form",style:z},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:E,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:M},"}"),!C&&B)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{getStyle:l,dataType:u}=this.props,s=r?this.renderCollapsed():this.renderNotCollapsed(),d=l(t,a,o,c,u);return n.createElement("div",{className:"rejt-object-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),s)}};i(Ns,"JsonObject");var ga=Ns;ga.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var Ls=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:u,dataType:s}=this.props,d=u(a,o,c,l,s);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:u,deep:s}=this.state;if(!l)return;let d=o(!0,c,s,u,l.value),m={value:d,key:u};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:u,readOnly:s,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,inputElementGenerator:h,minusMenuElement:g,keyPath:b}=this.props,E=m(t,u,o,c,d),v=s(t,u,o,c,d),A=a&&!v,S=h(Ga,b,c,t,u,d),O=f&&ae(f,{onClick:this.handleEdit}),D=p&&ae(p,{onClick:this.handleCancelEdit}),k=ae(S,{ref:this.refInput,defaultValue:JSON.stringify(u)}),I=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:E.minus});return n.createElement("li",{className:"rejt-value-node",style:E.li},n.createElement("span",{className:"rejt-name",style:E.name},t," : "),A?n.createElement("span",{className:"rejt-edit-form",style:E.editForm},k," ",D,O):n.createElement("span",{className:"rejt-value",style:E.value,onClick:v?void 0:this.handleEditMode},String(r)),!v&&!A&&I)}};i(Ls,"JsonValue");var nt=Ls;nt.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>Promise.resolve(),"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};function Fs(e){let t=e;if(t.indexOf("function")===0)return(0,eval)(`(${t})`);try{t=JSON.parse(e)}catch{}return t}i(Fs,"parse");var Kf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Yf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Jf={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}},Ps=class extends Re{constructor(t){super(t),this.state={data:t.data,rootName:t.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data||t.rootName!==r.rootName?{data:t.data,rootName:t.rootName}:null}onUpdate(t,r){this.setState({data:r}),this.props.onFullyUpdate?.(r)}removeRoot(){this.onUpdate(null,null)}render(){let{data:t,rootName:r}=this.state,{isCollapsed:a,onDeltaUpdate:o,readOnly:c,getStyle:l,addButtonElement:u,cancelButtonElement:s,editButtonElement:d,inputElement:m,textareaElement:f,minusMenuElement:p,plusMenuElement:h,beforeRemoveAction:g,beforeAddAction:b,beforeUpdateAction:E,logger:v,onSubmitValueParser:A,fallback:S=null}=this.props,O=it(t),D=c;it(c)==="Boolean"&&(D=i(()=>c,"readOnlyFunction"));let k=m;m&&it(m)!=="Function"&&(k=i(()=>m,"inputElementFunction"));let I=f;return f&&it(f)!=="Function"&&(I=i(()=>f,"textareaElementFunction")),O==="Object"||O==="Array"?n.createElement("div",{className:"rejt-tree"},n.createElement(un,{data:t,name:r||"root",deep:-1,isCollapsed:a??(()=>!1),onUpdate:this.onUpdate,onDeltaUpdate:o??(()=>{}),readOnly:D,getStyle:l??(()=>({})),addButtonElement:u,cancelButtonElement:s,editButtonElement:d,inputElementGenerator:k,textareaElementGenerator:I,minusMenuElement:p,plusMenuElement:h,handleRemove:this.removeRoot,beforeRemoveAction:g,beforeAddAction:b,beforeUpdateAction:E,logger:v??{},onSubmitValueParser:A??(F=>F)})):S}};i(Ps,"JsonTree");var js=Ps;js.defaultProps={rootName:"root",isCollapsed:i((e,t)=>t!==-1,"isCollapsed"),getStyle:i((e,t,r,a,o)=>{switch(o){case"Object":case"Error":return Kf;case"Array":return Yf;default:return Jf}},"getStyle"),readOnly:i(()=>!1,"readOnly"),onFullyUpdate:i(()=>{},"onFullyUpdate"),onDeltaUpdate:i(()=>{},"onDeltaUpdate"),beforeRemoveAction:i(()=>Promise.resolve(),"beforeRemoveAction"),beforeAddAction:i(()=>Promise.resolve(),"beforeAddAction"),beforeUpdateAction:i(()=>Promise.resolve(),"beforeUpdateAction"),logger:{error:i(()=>{},"error")},onSubmitValueParser:i((e,t,r,a,o)=>Fs(o),"onSubmitValueParser"),inputElement:i(()=>n.createElement("input",null),"inputElement"),textareaElement:i(()=>n.createElement("textarea",null),"textareaElement"),fallback:null};var{window:Xf}=globalThis,Zf=y.div(({theme:e})=>({position:"relative",display:"flex",'&[aria-readonly="true"]':{opacity:.5},".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:e.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:e.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:e.color.lighter,borderColor:e.appBorderColor}})),zn=y.button(({theme:e,primary:t})=>({border:0,height:20,margin:1,borderRadius:4,background:t?e.color.secondary:"transparent",color:t?e.color.lightest:e.color.dark,fontWeight:t?"bold":"normal",cursor:"pointer",order:t?"initial":9})),Qf=y(fr)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.ancillary},"svg + &":{marginLeft:0}})),eh=y(Lo)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.negative},"svg + &":{marginLeft:0}})),ai=y.input(({theme:e,placeholder:t})=>({outline:0,margin:t?1:"1px 0",padding:"3px 4px",color:e.color.defaultText,background:e.background.app,border:`1px solid ${e.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:t==="Key"?80:120,"&:focus":{border:`1px solid ${e.color.secondary}`}})),th=y(K)(({theme:e})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:e.background.bar,border:`1px solid ${e.appBorderColor}`,borderRadius:3,color:e.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),rh=y(de.Textarea)(({theme:e})=>({flex:1,padding:"7px 6px",fontFamily:e.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:e.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),nh={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},ah=i(e=>{e.currentTarget.dispatchEvent(new Xf.KeyboardEvent("keydown",nh))},"dispatchEnterKey"),oh=i(e=>{e.currentTarget.select()},"selectValue"),lh=i(e=>()=>({name:{color:e.color.secondary},collapsed:{color:e.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),"getCustomStyleFunction"),oi=i(({name:e,value:t,onChange:r,argType:a})=>{let o=Ie(),c=ce(()=>t&&au(t),[t]),l=c!=null,[u,s]=R(!l),[d,m]=R(null),f=!!a?.table?.readonly,p=H(S=>{try{S&&r(JSON.parse(S)),m(null)}catch(O){m(O)}},[r]),[h,g]=R(!1),b=H(()=>{r({}),g(!0)},[g]),E=Q(null);if(j(()=>{h&&E.current&&E.current.select()},[h]),!l)return n.createElement(ge,{disabled:f,id:or(e),onClick:b},"Set object");let v=n.createElement(rh,{ref:E,id:ke(e),name:e,defaultValue:t===null?"":JSON.stringify(t,null,2),onBlur:S=>p(S.target.value),placeholder:"Edit JSON string...",autoFocus:h,valid:d?"error":void 0,readOnly:f}),A=Array.isArray(t)||typeof t=="object"&&t?.constructor===Object;return n.createElement(Zf,{"aria-readonly":f},A&&n.createElement(th,{onClick:S=>{S.preventDefault(),s(O=>!O)}},u?n.createElement(yo,null):n.createElement(Eo,null),n.createElement("span",null,"RAW")),u?v:n.createElement(js,{readOnly:f||!A,isCollapsed:A?void 0:()=>!0,data:c,rootName:e,onFullyUpdate:r,getStyle:lh(o),cancelButtonElement:n.createElement(zn,{type:"button"},"Cancel"),editButtonElement:n.createElement(zn,{type:"submit"},"Save"),addButtonElement:n.createElement(zn,{type:"submit",primary:!0},"Save"),plusMenuElement:n.createElement(Qf,null),minusMenuElement:n.createElement(eh,null),inputElement:(S,O,D,k)=>k?n.createElement(ai,{onFocus:oh,onBlur:ah}):n.createElement(ai,null),fallback:v}))},"ObjectControl");je();var ih=y.input(({theme:e,min:t,max:r,value:a,disabled:o})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${Fe(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Fe(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grab",appearance:"none",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:o?"not-allowed":"grab"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:Fe(e.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:e.color.secondary,boxShadow:`0 0px 5px 0px ${e.color.secondary}`}},"&::-moz-range-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${Fe(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Fe(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grap",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${e.input.background}`,border:`1px solid ${Fe(e.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),Ms=y.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums","[aria-readonly=true] &":{opacity:.5}}),uh=y(Ms)(({numberOFDecimalsPlaces:e,max:t})=>({width:`${e+t.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),sh=y.div({display:"flex",alignItems:"center",width:"100%"});function $s(e){let t=e.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}i($s,"getNumberOfDecimalPlaces");var ch=i(({name:e,value:t,onChange:r,min:a=0,max:o=100,step:c=1,onBlur:l,onFocus:u,argType:s})=>{let d=i(h=>{r(Af(h.target.value))},"handleChange"),m=t!==void 0,f=ce(()=>$s(c),[c]),p=!!s?.table?.readonly;return n.createElement(sh,{"aria-readonly":p},n.createElement(Ms,null,a),n.createElement(ih,{id:ke(e),type:"range",disabled:p,onChange:d,name:e,min:a,max:o,step:c,onFocus:u,onBlur:l,value:t??a}),n.createElement(uh,{numberOFDecimalsPlaces:f,max:o},m?t.toFixed(f):"--"," / ",o))},"RangeControl");je();var dh=y.label({display:"flex"}),ph=y.div(({isMaxed:e})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:e?"red":void 0})),mh=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,maxLength:c,argType:l})=>{let u=i(h=>{r(h.target.value)},"handleChange"),s=!!l?.table?.readonly,[d,m]=R(!1),f=H(()=>{r(""),m(!0)},[m]);if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",disabled:s,id:or(e),onClick:f},"Set string");let p=typeof t=="string";return n.createElement(dh,null,n.createElement(de.Textarea,{id:ke(e),maxLength:c,onChange:u,disabled:s,size:"flex",placeholder:"Edit string...",autoFocus:d,valid:p?void 0:"error",name:e,value:p?t:"",onFocus:a,onBlur:o}),c&&n.createElement(ph,{isMaxed:t?.length===c},t?.length??0," / ",c))},"TextControl");je();var fh=y(de.Input)({padding:10});function Us(e){e.forEach(t=>{t.startsWith("blob:")&&URL.revokeObjectURL(t)})}i(Us,"revokeOldUrls");var hh=i(({onChange:e,name:t,accept:r="image/*",value:a,argType:o})=>{let c=Q(null),l=o?.control?.readOnly;function u(s){if(!s.target.files)return;let d=Array.from(s.target.files).map(m=>URL.createObjectURL(m));e(d),Us(a||[])}return i(u,"handleFileChange"),j(()=>{a==null&&c.current&&(c.current.value="")},[a,t]),n.createElement(fh,{ref:c,id:ke(t),type:"file",name:t,multiple:!0,disabled:l,onChange:u,accept:r,size:"flex"})},"FilesControl"),gh=po(()=>Promise.resolve().then(()=>(mp(),Au))),bh=i(e=>n.createElement(so,{fallback:n.createElement("div",null)},n.createElement(gh,{...e})),"ColorControl"),yh={array:oi,object:oi,boolean:ff,color:bh,date:vf,number:Sf,check:Ot,"inline-check":Ot,radio:Ot,"inline-radio":Ot,select:Ot,"multi-select":Ot,range:ch,text:mh,file:hh},li=i(()=>n.createElement(n.Fragment,null,"-"),"NoControl"),Eh=i(({row:e,arg:t,updateArgs:r,isHovered:a})=>{let{key:o,control:c}=e,[l,u]=R(!1),[s,d]=R({value:t});j(()=>{l||d({value:t})},[l,t]);let m=H(b=>(d({value:b}),r({[o]:b}),b),[r,o]),f=H(()=>u(!1),[]),p=H(()=>u(!0),[]);if(!c||c.disable){let b=c?.disable!==!0&&e?.type?.name!=="function";return a&&b?n.createElement(Te,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):n.createElement(li,null)}let h={name:o,argType:e,value:s.value,onChange:m,onBlur:f,onFocus:p},g=yh[c.type]||li;return n.createElement(g,{...h,...c,controlType:c.type})},"ArgControl"),vh=y.table(({theme:e})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:Xe({theme:e}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:e.typography.size.s1}}})),xh=i(({tags:e})=>{let t=(e.params||[]).filter(c=>c.description),r=t.length!==0,a=e.deprecated!=null,o=e.returns!=null&&e.returns.description!=null;return!r&&!o&&!a?null:n.createElement(n.Fragment,null,n.createElement(vh,null,n.createElement("tbody",null,a&&n.createElement("tr",{key:"deprecated"},n.createElement("td",{colSpan:2},n.createElement("strong",null,"Deprecated"),": ",e.deprecated?.toString())),r&&t.map(c=>n.createElement("tr",{key:c.name},n.createElement("td",null,n.createElement("code",null,c.name)),n.createElement("td",null,c.description))),o&&n.createElement("tr",{key:"returns"},n.createElement("td",null,n.createElement("code",null,"Returns")),n.createElement("td",null,e.returns?.description)))))},"ArgJsDoc");an();var Ah=Ae(fp()),ba=8,ii=y.div(({isExpanded:e})=>({display:"flex",flexDirection:e?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Ch=y.span(Xe,({theme:e,simple:t=!1})=>({flex:"0 0 auto",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...t&&{background:"transparent",border:"0 none",paddingLeft:0}})),Sh=y.button(({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,marginBottom:"4px",background:"none",border:"none"})),wh=y.div(Xe,({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,fontSize:e.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),kh=y.div(({theme:e,width:t})=>({width:t,minWidth:200,maxWidth:800,padding:15,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),Oh=y(bo)({marginLeft:4}),Th=y(gr)({marginLeft:4}),Ih=i(()=>n.createElement("span",null,"-"),"EmptyArg"),Hs=i(({text:e,simple:t})=>n.createElement(Ch,{simple:t},e),"ArgText"),Dh=(0,Ah.default)(1e3)(e=>{let t=e.split(/\r?\n/);return`${Math.max(...t.map(r=>r.length))}ch`}),Bh=i(e=>{if(!e)return[e];let t=e.split("|").map(r=>r.trim());return lu(t)},"getSummaryItems"),ui=i((e,t=!0)=>{let r=e;return t||(r=e.slice(0,ba)),r.map(a=>n.createElement(Hs,{key:a,text:a===""?'""':a}))},"renderSummaryItems"),_h=i(({value:e,initialExpandedArgs:t})=>{let{summary:r,detail:a}=e,[o,c]=R(!1),[l,u]=R(t||!1);if(r==null)return null;let s=typeof r.toString=="function"?r.toString():r;if(a==null){if(/[(){}[\]<>]/.test(s))return n.createElement(Hs,{text:s});let d=Bh(s),m=d.length;return m>ba?n.createElement(ii,{isExpanded:l},ui(d,l),n.createElement(Sh,{onClick:()=>u(!l)},l?"Show less...":`Show ${m-ba} more...`)):n.createElement(ii,null,ui(d))}return n.createElement(yn,{closeOnOutsideClick:!0,placement:"bottom",visible:o,onVisibleChange:d=>{c(d)},tooltip:n.createElement(kh,{width:Dh(a)},n.createElement(Vt,{language:"jsx",format:!1},a))},n.createElement(wh,{className:"sbdocs-expandable"},n.createElement("span",null,s),o?n.createElement(Oh,null):n.createElement(Th,null)))},"ArgSummary"),qn=i(({value:e,initialExpandedArgs:t})=>e==null?n.createElement(Ih,null):n.createElement(_h,{value:e,initialExpandedArgs:t}),"ArgValue"),Rh=y.span({fontWeight:"bold"}),Nh=y.span(({theme:e})=>({color:e.color.negative,fontFamily:e.typography.fonts.mono,cursor:"help"})),Lh=y.div(({theme:e})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:e.color.secondary}},code:{...Xe({theme:e}),fontSize:12,fontFamily:e.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Fh=y.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?Y(.1,e.color.defaultText):Y(.2,e.color.defaultText),marginTop:t?4:0})),Ph=y.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?Y(.1,e.color.defaultText):Y(.2,e.color.defaultText),marginTop:t?12:0,marginBottom:12})),jh=y.td(({expandable:e})=>({paddingLeft:e?"40px !important":"20px !important"})),Mh=i(e=>e&&{summary:typeof e=="string"?e:e.name},"toSummary"),Nr=i(e=>{let[t,r]=R(!1),{row:a,updateArgs:o,compact:c,expandable:l,initialExpandedArgs:u}=e,{name:s,description:d}=a,m=a.table||{},f=m.type||Mh(a.type),p=m.defaultValue||a.defaultValue,h=a.type?.required,g=d!=null&&d!=="";return n.createElement("tr",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},n.createElement(jh,{expandable:l??!1},n.createElement(Rh,null,s),h?n.createElement(Nh,{title:"Required"},"*"):null),c?null:n.createElement("td",null,g&&n.createElement(Lh,null,n.createElement(df,null,d)),m.jsDocTags!=null?n.createElement(n.Fragment,null,n.createElement(Ph,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:u})),n.createElement(xh,{tags:m.jsDocTags})):n.createElement(Fh,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:u}))),c?null:n.createElement("td",null,n.createElement(qn,{value:p,initialExpandedArgs:u})),o?n.createElement("td",null,n.createElement(Eh,{...e,isHovered:t})):null)},"ArgRow"),$h=y.div(({inAddonPanel:e,theme:t})=>({height:e?"100%":"auto",display:"flex",border:e?"none":`1px solid ${t.appBorderColor}`,borderRadius:e?0:t.appBorderRadius,padding:e?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:t.background.content})),Uh=y.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),Hh=i(({inAddonPanel:e})=>{let[t,r]=R(!0);return j(()=>{let a=setTimeout(()=>{r(!1)},100);return()=>clearTimeout(a)},[]),t?null:n.createElement($h,{inAddonPanel:e},n.createElement(Ht,{title:e?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated",description:n.createElement(n.Fragment,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically."),footer:n.createElement(Uh,null,e&&n.createElement(n.Fragment,null,n.createElement(Te,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs")),!e&&n.createElement(Te,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Learn how to set that up"))}))},"Empty"),Vh=y(ho)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?Y(.25,e.color.defaultText):Y(.3,e.color.defaultText),border:"none",display:"inline-block"})),zh=y(go)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?Y(.25,e.color.defaultText):Y(.3,e.color.defaultText),border:"none",display:"inline-block"})),qh=y.span(({theme:e})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),Gh=y.td(({theme:e})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s1-1,color:e.base==="light"?Y(.4,e.color.defaultText):Y(.6,e.color.defaultText),background:`${e.background.app} !important`,"& ~ td":{background:`${e.background.app} !important`}})),Wh=y.td(({theme:e})=>({position:"relative",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,background:e.background.app})),Kh=y.td({position:"relative"}),Yh=y.tr(({theme:e})=>({"&:hover > td":{backgroundColor:`${ht(.005,e.background.app)} !important`,boxShadow:`${e.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),si=y.button({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"}),Gn=i(({level:e="section",label:t,children:r,initialExpanded:a=!0,colSpan:o=3})=>{let[c,l]=R(a),u=e==="subsection"?Wh:Gh,s=r?.length||0,d=e==="subsection"?`${s} item${s!==1?"s":""}`:"",m=`${c?"Hide":"Show"} ${e==="subsection"?s:t} item${s!==1?"s":""}`;return n.createElement(n.Fragment,null,n.createElement(Yh,{title:m},n.createElement(u,{colSpan:1},n.createElement(si,{onClick:f=>l(!c),tabIndex:0},m),n.createElement(qh,null,c?n.createElement(Vh,null):n.createElement(zh,null),t)),n.createElement(Kh,{colSpan:o-1},n.createElement(si,{onClick:f=>l(!c),tabIndex:-1,style:{outline:"none"}},m),c?null:d)),c?r:null)},"SectionRow"),Jh=y.div(({theme:e})=>({width:"100%",borderSpacing:0,color:e.color.defaultText})),Lr=y.div(({theme:e})=>({display:"flex",borderBottom:`1px solid ${e.appBorderColor}`,"&:last-child":{borderBottom:0}})),me=y.div(({position:e,theme:t})=>{let r={display:"flex",flexDirection:"column",gap:5,padding:"10px 15px",alignItems:"flex-start"};switch(e){case"first":return{...r,width:"25%",paddingLeft:20};case"second":return{...r,width:"35%"};case"third":return{...r,width:"15%"};case"last":return{...r,width:"25%",paddingRight:20}}}),ue=y.div(({theme:e,width:t,height:r})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,width:t||"100%",height:r||16,borderRadius:3})),Xh=i(()=>n.createElement(Jh,null,n.createElement(Lr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Lr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Lr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Lr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"})))),"Skeleton"),Zh=y.table(({theme:e,compact:t,inAddonPanel:r})=>({"&&":{borderSpacing:0,color:e.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:e.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:r?0:25,marginBottom:r?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...t?null:{width:"35%"}},"td:nth-of-type(3)":{...t?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...t?null:{width:"25%"}},th:{color:e.base==="light"?Y(.25,e.color.defaultText):Y(.45,e.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:r?0:1,marginRight:r?0:1,tbody:{...r?null:{filter:e.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:e.background.content,borderTop:`1px solid ${e.appBorderColor}`},...r?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${e.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${e.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${e.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${e.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:e.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:e.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:e.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:e.appBorderRadius}}}}})),Qh=y(K)(({theme:e})=>({margin:"-4px -12px -4px 0"})),e2=y.span({display:"flex",justifyContent:"space-between"}),t2={alpha:i((e,t)=>(e.name??"").localeCompare(t.name??""),"alpha"),requiredFirst:i((e,t)=>+!!t.type?.required-+!!e.type?.required||(e.name??"").localeCompare(t.name??""),"requiredFirst"),none:null},r2=i((e,t)=>{let r={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!e)return r;Object.entries(e).forEach(([c,l])=>{let{category:u,subcategory:s}=l?.table||{};if(u){let d=r.sections[u]||{ungrouped:[],subsections:{}};if(!s)d.ungrouped.push({key:c,...l});else{let m=d.subsections[s]||[];m.push({key:c,...l}),d.subsections[s]=m}r.sections[u]=d}else if(s){let d=r.ungroupedSubsections[s]||[];d.push({key:c,...l}),r.ungroupedSubsections[s]=d}else r.ungrouped.push({key:c,...l})});let a=t2[t],o=i(c=>a?Object.keys(c).reduce((l,u)=>({...l,[u]:c[u].sort(a)}),{}):c,"sortSubsection");return{ungrouped:a?r.ungrouped.sort(a):r.ungrouped,ungroupedSubsections:o(r.ungroupedSubsections),sections:Object.keys(r.sections).reduce((c,l)=>({...c,[l]:{ungrouped:a?r.sections[l].ungrouped.sort(a):r.sections[l].ungrouped,subsections:o(r.sections[l].subsections)}}),{})}},"groupRows"),n2=i((e,t,r)=>{try{return il(e,t,r)}catch(a){return rl.warn(a.message),!1}},"safeIncludeConditionalArg"),a2=i(e=>{let{updateArgs:t,resetArgs:r,compact:a,inAddonPanel:o,initialExpandedArgs:c,sort:l="none",isLoading:u}=e;if("error"in e){let{error:A}=e;return n.createElement(Zu,null,A,"\xA0",n.createElement(Te,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read the docs"))}if(u)return n.createElement(Xh,null);let{rows:s,args:d,globals:m}="rows"in e?e:{rows:void 0,args:void 0,globals:void 0},f=r2(bu(s||{},A=>!A?.table?.disable&&n2(A,d||{},m||{})),l),p=f.ungrouped.length===0,h=Object.entries(f.sections).length===0,g=Object.entries(f.ungroupedSubsections).length===0;if(p&&h&&g)return n.createElement(Hh,{inAddonPanel:o});let b=1;t&&(b+=1),a||(b+=2);let E=Object.keys(f.sections).length>0,v={updateArgs:t,compact:a,inAddonPanel:o,initialExpandedArgs:c};return n.createElement(hn,null,n.createElement(Zh,{compact:a,inAddonPanel:o,className:"docblock-argstable sb-unstyled"},n.createElement("thead",{className:"docblock-argstable-head"},n.createElement("tr",null,n.createElement("th",null,n.createElement("span",null,"Name")),a?null:n.createElement("th",null,n.createElement("span",null,"Description")),a?null:n.createElement("th",null,n.createElement("span",null,"Default")),t?n.createElement("th",null,n.createElement(e2,null,"Control"," ",!u&&r&&n.createElement(Qh,{onClick:()=>r(),title:"Reset controls"},n.createElement(Er,{"aria-hidden":!0})))):null)),n.createElement("tbody",{className:"docblock-argstable-body"},f.ungrouped.map(A=>n.createElement(Nr,{key:A.key,row:A,arg:d&&d[A.key],...v})),Object.entries(f.ungroupedSubsections).map(([A,S])=>n.createElement(Gn,{key:A,label:A,level:"subsection",colSpan:b},S.map(O=>n.createElement(Nr,{key:O.key,row:O,arg:d&&d[O.key],expandable:E,...v})))),Object.entries(f.sections).map(([A,S])=>n.createElement(Gn,{key:A,label:A,level:"section",colSpan:b},S.ungrouped.map(O=>n.createElement(Nr,{key:O.key,row:O,arg:d&&d[O.key],...v})),Object.entries(S.subsections).map(([O,D])=>n.createElement(Gn,{key:O,label:O,level:"subsection",colSpan:b},D.map(k=>n.createElement(Nr,{key:k.key,row:k,arg:d&&d[k.key],expandable:E,...v})))))))))},"ArgsTable"),ya="addon-controls",Vs="controls",o2=xn({from:{transform:"translateY(40px)"},to:{transform:"translateY(0)"}}),l2=xn({from:{background:"var(--highlight-bg-color)"},to:{}}),i2=y.div({containerType:"size",position:"sticky",bottom:0,height:39,overflow:"hidden",zIndex:1}),u2=y(Ut)(({theme:e})=>({"--highlight-bg-color":e.base==="dark"?"#153B5B":"#E0F0FF",display:"flex",flexDirection:"row-reverse",alignItems:"center",justifyContent:"space-between",flexWrap:"wrap",gap:6,padding:"6px 10px",animation:`${o2} 300ms, ${l2} 2s`,background:e.background.bar,borderTop:`1px solid ${e.appBorderColor}`,fontSize:e.typography.size.s2,"@container (max-width: 799px)":{flexDirection:"row",justifyContent:"flex-end"}})),s2=y.div({display:"flex",flex:"99 0 auto",alignItems:"center",marginLeft:10,gap:6}),c2=y.div(({theme:e})=>({display:"flex",flex:"1 0 0",alignItems:"center",gap:2,color:e.color.mediumdark,fontSize:e.typography.size.s2})),Wn=y.div({"@container (max-width: 799px)":{lineHeight:0,textIndent:"-9999px","&::after":{content:"attr(data-short-label)",display:"block",lineHeight:"initial",textIndent:"0"}}}),d2=y(de.Input)(({theme:e})=>({"::placeholder":{color:e.color.mediumdark},"&:invalid:not(:placeholder-shown)":{boxShadow:`${e.color.negative} 0 0 0 1px inset`}})),p2=i(({saveStory:e,createStory:t,resetArgs:r})=>{let a=n.useRef(null),[o,c]=n.useState(!1),[l,u]=n.useState(!1),[s,d]=n.useState(""),[m,f]=n.useState(null),p=i(async()=>{o||(c(!0),await e().catch(()=>{}),c(!1))},"onSaveStory"),h=i(()=>{u(!0),d(""),setTimeout(()=>a.current?.focus(),0)},"onShowForm"),g=i(b=>{let E=b.target.value.replace(/^[^a-z]/i,"").replace(/[^a-z0-9-_ ]/gi,"").replaceAll(/([-_ ]+[a-z0-9])/gi,v=>v.toUpperCase().replace(/[-_ ]/g,""));d(E.charAt(0).toUpperCase()+E.slice(1))},"onChange");return n.createElement(i2,{id:"save-from-controls"},n.createElement(u2,null,n.createElement(c2,null,n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Save changes to story"})},n.createElement(K,{"aria-label":"Save changes to story",disabled:o,onClick:p},n.createElement(hr,null),n.createElement(Wn,{"data-short-label":"Save"},"Update story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Create new story with these settings"})},n.createElement(K,{"aria-label":"Create new story with these settings",onClick:h},n.createElement(fr,null),n.createElement(Wn,{"data-short-label":"New"},"Create new story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Reset changes"})},n.createElement(K,{"aria-label":"Reset changes",onClick:()=>r()},n.createElement(Er,null),n.createElement("span",null,"Reset")))),n.createElement(s2,null,n.createElement(Wn,{"data-short-label":"Unsaved changes"},"You modified this story. Do you want to save your changes?")),n.createElement(He,{width:350,open:l,onOpenChange:u},n.createElement(de,{onSubmit:i(async b=>{if(b.preventDefault(),!o)try{f(null),c(!0),await t(s.replace(/^[^a-z]/i,"").replaceAll(/[^a-z0-9]/gi,"")),u(!1),c(!1)}catch(E){f(E.message),c(!1)}},"onSubmitForm"),id:"create-new-story-form"},n.createElement(He.Content,null,n.createElement(He.Header,null,n.createElement(He.Title,null,"Create new story"),n.createElement(He.Description,null,"This will add a new story to your existing stories file.")),n.createElement(d2,{onChange:g,placeholder:"Story export name",readOnly:o,ref:a,value:s}),n.createElement(He.Actions,null,n.createElement(ge,{disabled:o||!s,size:"medium",type:"submit",variant:"solid"},"Create"),n.createElement(He.Dialog.Close,{asChild:!0},n.createElement(ge,{disabled:o,size:"medium",type:"reset"},"Cancel"))))),m&&n.createElement(He.Error,null,m))))},"SaveStory"),ci=i(e=>Object.entries(e).reduce((t,[r,a])=>a!==void 0?Object.assign(t,{[r]:a}):t,{}),"clean"),m2=y.div({display:"grid",gridTemplateRows:"1fr 39px",height:"100%",maxHeight:"100vh",overflowY:"auto"}),f2=i(({saveStory:e,createStory:t})=>{let[r,a]=R(!0),[o,c,l,u]=Yo(),[s]=Ve(),d=xr(),{expanded:m,sort:f,presetColors:p,disableSaveFromUI:h=!1}=et(Vs,{}),{path:g,previewInitialized:b}=Jo();j(()=>{b&&a(!1)},[b]);let E=Object.values(d).some(S=>S?.control),v=Object.entries(d).reduce((S,[O,D])=>{let k=D?.control;return typeof k!="object"||k?.type!=="color"||k?.presetColors?S[O]=D:S[O]={...D,control:{...k,presetColors:p}},S},{}),A=ce(()=>!!o&&!!u&&!ut(ci(o),ci(u)),[o,u]);return n.createElement(m2,null,n.createElement(a2,{key:g,compact:!m&&E,rows:v,args:o,globals:s,updateArgs:c,resetArgs:l,inAddonPanel:!0,sort:f,isLoading:r}),E&&A&&Qe.CONFIG_TYPE==="DEVELOPMENT"&&h!==!0&&n.createElement(p2,{resetArgs:l,saveStory:e,createStory:t}))},"ControlsPanel");function zs(){let e=ye().getSelectedPanel(),t=xr(),r=Object.values(t).filter(a=>a?.control&&!a?.table?.disable).length;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Controls"),r===0?null:n.createElement(ct,{compact:!0,status:e===ya?"active":"neutral"},r))}i(zs,"Title");var di=i(e=>JSON.stringify(e,(t,r)=>typeof r=="function"?"__sb_empty_function_arg__":r),"stringifyArgs"),Rw=ee.register(ya,e=>{if(globalThis?.FEATURES?.controls){let t=ee.getChannel(),r=i(async()=>{let o=e.getCurrentStoryData();if(o.type!=="story")throw new Error("Not a story");try{let c=await Cn(t,Sn,Cr,{args:di(Object.entries(o.args||{}).reduce((l,[u,s])=>(ut(s,o.initialArgs?.[u])||(l[u]=s),l),{})),csfId:o.id,importPath:o.importPath});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story saved",subHeadline:n.createElement(n.Fragment,null,"Updated story ",n.createElement("b",null,c.sourceStoryName),".")},duration:8e3})}catch(c){throw e.addNotification({id:"save-story-error",icon:n.createElement(vo,{color:vr.negative}),content:{headline:"Failed to save story",subHeadline:c?.message||"Check the Storybook process on the command line for more details."},duration:8e3}),c}},"saveStory"),a=i(async o=>{let c=e.getCurrentStoryData();if(c.type!=="story")throw new Error("Not a story");let l=await Cn(t,Sn,Cr,{args:c.args&&di(c.args),csfId:c.id,importPath:c.importPath,name:o});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story created",subHeadline:n.createElement(n.Fragment,null,"Added story ",n.createElement("b",null,l.newStoryName)," based on ",n.createElement("b",null,l.sourceStoryName),".")},duration:8e3,onClick:i(({onDismiss:u})=>{u(),e.selectStory(l.newStoryId)},"onClick")})},"createStory");ee.add(ya,{title:zs,type:be.PANEL,paramKey:Vs,render:i(({active:o})=>!o||!e.getCurrentStoryData()?null:n.createElement($t,{active:o},n.createElement(f2,{saveStory:r,createStory:a})),"render")}),t.on(Cr,o=>{if(!o.success)return;let c=e.getCurrentStoryData();c.type==="story"&&(e.resetStoryArgs(c),o.payload.newStoryId&&e.selectStory(o.payload.newStoryId))})}}),h2="actions",lr="storybook/actions",qs=`${lr}/panel`,Ea=`${lr}/action-event`,Gs=`${lr}/action-clear`;function Ws(){let e=ye().getSelectedPanel(),[{count:t},r]=St(lr,{count:0});return Ar({[Ea]:()=>{r(a=>({...a,count:a.count+1}))},[qt]:()=>{r(a=>({...a,count:0}))},[Gs]:()=>{r(a=>({...a,count:0}))}}),n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Actions"),t===0?null:n.createElement(ct,{compact:!0,status:e===qs?"active":"neutral"},t))}i(Ws,"Title");var g2=Object.create,Ya=Object.defineProperty,b2=Object.getOwnPropertyDescriptor,Ks=Object.getOwnPropertyNames,y2=Object.getPrototypeOf,E2=Object.prototype.hasOwnProperty,Ja=i((e,t)=>i(function(){return t||(0,e[Ks(e)[0]])((t={exports:{}}).exports,t),t.exports},"__require"),"__commonJS"),v2=i((e,t)=>{for(var r in t)Ya(e,r,{get:t[r],enumerable:!0})},"__export"),x2=i((e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Ks(t))!E2.call(e,o)&&o!==r&&Ya(e,o,{get:i(()=>t[o],"get"),enumerable:!(a=b2(t,o))||a.enumerable});return e},"__copyProps"),A2=i((e,t,r)=>(r=e!=null?g2(y2(e)):{},x2(t||!e||!e.__esModule?Ya(r,"default",{value:e,enumerable:!0}):r,e)),"__toESM"),C2=Ja({"node_modules/is-object/index.js"(e,t){"use strict";t.exports=i(function(r){return typeof r=="object"&&r!==null},"isObject")}}),S2=Ja({"node_modules/is-window/index.js"(e,t){"use strict";t.exports=function(r){if(r==null)return!1;var a=Object(r);return a===a.window}}}),w2=Ja({"node_modules/is-dom/index.js"(e,t){var r=C2(),a=S2();function o(c){return!r(c)||!a(window)||typeof window.Node!="function"?!1:typeof c.nodeType=="number"&&typeof c.nodeName=="string"}i(o,"isNode"),t.exports=o}}),Xr={};v2(Xr,{chromeDark:i(()=>k2,"chromeDark"),chromeLight:i(()=>O2,"chromeLight")});var k2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"rgb(36, 36, 36)",BASE_COLOR:"rgb(213, 213, 213)",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(227, 110, 236)",OBJECT_VALUE_NULL_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_REGEXP_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_STRING_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_NUMBER_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_BOOLEAN_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(85, 106, 242)",HTML_TAG_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(155, 187, 220)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(242, 151, 102)",HTML_COMMENT_COLOR:"rgb(137, 137, 137)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"rgb(145, 145, 145)",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"rgb(85, 85, 85)",TABLE_TH_BACKGROUND_COLOR:"rgb(44, 44, 44)",TABLE_TH_HOVER_COLOR:"rgb(48, 48, 48)",TABLE_SORT_ICON_COLOR:"black",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},O2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"white",BASE_COLOR:"black",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(136, 19, 145)",OBJECT_VALUE_NULL_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_REGEXP_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_STRING_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_NUMBER_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_BOOLEAN_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(13, 34, 170)",HTML_TAG_COLOR:"rgb(168, 148, 166)",HTML_TAGNAME_COLOR:"rgb(136, 18, 128)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(153, 69, 0)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(26, 26, 166)",HTML_COMMENT_COLOR:"rgb(35, 110, 37)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"#6e6e6e",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"#aaa",TABLE_TH_BACKGROUND_COLOR:"#eee",TABLE_TH_HOVER_COLOR:"hsla(0, 0%, 90%, 1)",TABLE_SORT_ICON_COLOR:"#6e6e6e",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},Ys=Ct([{},()=>{}]),Kn={WebkitTouchCallout:"none",WebkitUserSelect:"none",KhtmlUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",OUserSelect:"none",userSelect:"none"},qr=i(e=>({DOMNodePreview:{htmlOpenTag:{base:{color:e.HTML_TAG_COLOR},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM},htmlAttributeName:{color:e.HTML_ATTRIBUTE_NAME_COLOR},htmlAttributeValue:{color:e.HTML_ATTRIBUTE_VALUE_COLOR}},htmlCloseTag:{base:{color:e.HTML_TAG_COLOR},offsetLeft:{marginLeft:-e.TREENODE_PADDING_LEFT},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM}},htmlComment:{color:e.HTML_COMMENT_COLOR},htmlDoctype:{color:e.HTML_DOCTYPE_COLOR}},ObjectPreview:{objectDescription:{fontStyle:"italic"},preview:{fontStyle:"italic"},arrayMaxProperties:e.OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES,objectMaxProperties:e.OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES},ObjectName:{base:{color:e.OBJECT_NAME_COLOR},dimmed:{opacity:.6}},ObjectValue:{objectValueNull:{color:e.OBJECT_VALUE_NULL_COLOR},objectValueUndefined:{color:e.OBJECT_VALUE_UNDEFINED_COLOR},objectValueRegExp:{color:e.OBJECT_VALUE_REGEXP_COLOR},objectValueString:{color:e.OBJECT_VALUE_STRING_COLOR},objectValueSymbol:{color:e.OBJECT_VALUE_SYMBOL_COLOR},objectValueNumber:{color:e.OBJECT_VALUE_NUMBER_COLOR},objectValueBoolean:{color:e.OBJECT_VALUE_BOOLEAN_COLOR},objectValueFunctionPrefix:{color:e.OBJECT_VALUE_FUNCTION_PREFIX_COLOR,fontStyle:"italic"},objectValueFunctionName:{fontStyle:"italic"}},TreeView:{treeViewOutline:{padding:0,margin:0,listStyleType:"none"}},TreeNode:{treeNodeBase:{color:e.BASE_COLOR,backgroundColor:e.BASE_BACKGROUND_COLOR,lineHeight:e.TREENODE_LINE_HEIGHT,cursor:"default",boxSizing:"border-box",listStyle:"none",fontFamily:e.TREENODE_FONT_FAMILY,fontSize:e.TREENODE_FONT_SIZE},treeNodePreviewContainer:{},treeNodePlaceholder:{whiteSpace:"pre",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...Kn},treeNodeArrow:{base:{color:e.ARROW_COLOR,display:"inline-block",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...parseFloat(e.ARROW_ANIMATION_DURATION)>0?{transition:`transform ${e.ARROW_ANIMATION_DURATION} ease 0s`}:{},...Kn},expanded:{WebkitTransform:"rotateZ(90deg)",MozTransform:"rotateZ(90deg)",transform:"rotateZ(90deg)"},collapsed:{WebkitTransform:"rotateZ(0deg)",MozTransform:"rotateZ(0deg)",transform:"rotateZ(0deg)"}},treeNodeChildNodesContainer:{margin:0,paddingLeft:e.TREENODE_PADDING_LEFT}},TableInspector:{base:{color:e.BASE_COLOR,position:"relative",border:`1px solid ${e.TABLE_BORDER_COLOR}`,fontFamily:e.BASE_FONT_FAMILY,fontSize:e.BASE_FONT_SIZE,lineHeight:"120%",boxSizing:"border-box",cursor:"default"}},TableInspectorHeaderContainer:{base:{top:0,height:"17px",left:0,right:0,overflowX:"hidden"},table:{tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",height:"100%",width:"100%",margin:0}},TableInspectorDataContainer:{tr:{display:"table-row"},td:{boxSizing:"border-box",border:"none",height:"16px",verticalAlign:"top",padding:"1px 4px",WebkitUserSelect:"text",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px"},div:{position:"static",top:"17px",bottom:0,overflowY:"overlay",transform:"translateZ(0)",left:0,right:0,overflowX:"hidden"},table:{positon:"static",left:0,top:0,right:0,bottom:0,borderTop:"0 none transparent",margin:0,backgroundImage:e.TABLE_DATA_BACKGROUND_IMAGE,backgroundSize:e.TABLE_DATA_BACKGROUND_SIZE,tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",width:"100%",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorTH:{base:{position:"relative",height:"auto",textAlign:"left",backgroundColor:e.TABLE_TH_BACKGROUND_COLOR,borderBottom:`1px solid ${e.TABLE_BORDER_COLOR}`,fontWeight:"normal",verticalAlign:"middle",padding:"0 4px",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px",":hover":{backgroundColor:e.TABLE_TH_HOVER_COLOR}},div:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorLeftBorder:{none:{borderLeft:"none"},solid:{borderLeft:`1px solid ${e.TABLE_BORDER_COLOR}`}},TableInspectorSortIcon:{display:"block",marginRight:3,width:8,height:7,marginTop:-7,color:e.TABLE_SORT_ICON_COLOR,fontSize:12,...Kn}}),"createTheme"),va="chromeLight",Js=Ct(qr(Xr[va])),we=i(e=>dr(Js)[e],"useStyles"),Xa=i(e=>i(({theme:t=va,...r})=>{let a=ce(()=>{switch(Object.prototype.toString.call(t)){case"[object String]":return qr(Xr[t]);case"[object Object]":return qr(t);default:return qr(Xr[va])}},[t]);return n.createElement(Js.Provider,{value:a},n.createElement(e,{...r}))},"ThemeAcceptor"),"themeAcceptor"),T2=i(({expanded:e,styles:t})=>n.createElement("span",{style:{...t.base,...e?t.expanded:t.collapsed}},"\u25B6"),"Arrow"),I2=he(e=>{e={expanded:!0,nodeRenderer:i(({name:m})=>n.createElement("span",null,m),"nodeRenderer"),onClick:i(()=>{},"onClick"),shouldShowArrow:!1,shouldShowPlaceholder:!0,...e};let{expanded:t,onClick:r,children:a,nodeRenderer:o,title:c,shouldShowArrow:l,shouldShowPlaceholder:u}=e,s=we("TreeNode"),d=o;return n.createElement("li",{"aria-expanded":t,role:"treeitem",style:s.treeNodeBase,title:c},n.createElement("div",{style:s.treeNodePreviewContainer,onClick:r},l||cr.count(a)>0?n.createElement(T2,{expanded:t,styles:s.treeNodeArrow}):u&&n.createElement("span",{style:s.treeNodePlaceholder},"\xA0"),n.createElement(d,{...e})),n.createElement("ol",{role:"group",style:s.treeNodeChildNodesContainer},t?a:void 0))}),Zr="$",pi="*";function Qt(e,t){return!t(e).next().done}i(Qt,"hasChildNodes");var D2=i(e=>Array.from({length:e},(t,r)=>[Zr].concat(Array.from({length:r},()=>"*")).join(".")),"wildcardPathsFromLevel"),B2=i((e,t,r,a,o)=>{let c=[].concat(D2(a)).concat(r).filter(u=>typeof u=="string"),l=[];return c.forEach(u=>{let s=u.split("."),d=i((m,f,p)=>{if(p===s.length){l.push(f);return}let h=s[p];if(p===0)Qt(m,t)&&(h===Zr||h===pi)&&d(m,Zr,p+1);else if(h===pi)for(let{name:g,data:b}of t(m))Qt(b,t)&&d(b,`${f}.${g}`,p+1);else{let g=m[h];Qt(g,t)&&d(g,`${f}.${h}`,p+1)}},"populatePaths");d(e,"",0)}),l.reduce((u,s)=>(u[s]=!0,u),{...o})},"getExpandedPaths"),Xs=he(e=>{let{data:t,dataIterator:r,path:a,depth:o,nodeRenderer:c}=e,[l,u]=dr(Ys),s=Qt(t,r),d=!!l[a],m=H(()=>s&&u(f=>({...f,[a]:!d})),[s,u,a,d]);return n.createElement(I2,{expanded:d,onClick:m,shouldShowArrow:s,shouldShowPlaceholder:o>0,nodeRenderer:c,...e},d?[...r(t)].map(({name:f,data:p,...h})=>n.createElement(Xs,{name:f,data:p,depth:o+1,path:`${a}.${f}`,key:f,dataIterator:r,nodeRenderer:c,...h})):null)}),Zs=he(({name:e,data:t,dataIterator:r,nodeRenderer:a,expandPaths:o,expandLevel:c})=>{let l=we("TreeView"),u=R({}),[,s]=u;return pr(()=>s(d=>B2(t,r,o,c,d)),[t,r,o,c]),n.createElement(Ys.Provider,{value:u},n.createElement("ol",{role:"tree",style:l.treeViewOutline},n.createElement(Xs,{name:e,data:t,dataIterator:r,depth:0,path:Zr,nodeRenderer:a})))}),Za=i(({name:e,dimmed:t=!1,styles:r={}})=>{let a=we("ObjectName"),o={...a.base,...t?a.dimmed:{},...r};return n.createElement("span",{style:o},e)},"ObjectName"),er=i(({object:e,styles:t})=>{let r=we("ObjectValue"),a=i(o=>({...r[o],...t}),"mkStyle");switch(typeof e){case"bigint":return n.createElement("span",{style:a("objectValueNumber")},String(e),"n");case"number":return n.createElement("span",{style:a("objectValueNumber")},String(e));case"string":return n.createElement("span",{style:a("objectValueString")},'"',e,'"');case"boolean":return n.createElement("span",{style:a("objectValueBoolean")},String(e));case"undefined":return n.createElement("span",{style:a("objectValueUndefined")},"undefined");case"object":return e===null?n.createElement("span",{style:a("objectValueNull")},"null"):e instanceof Date?n.createElement("span",null,e.toString()):e instanceof RegExp?n.createElement("span",{style:a("objectValueRegExp")},e.toString()):Array.isArray(e)?n.createElement("span",null,`Array(${e.length})`):e.constructor?typeof e.constructor.isBuffer=="function"&&e.constructor.isBuffer(e)?n.createElement("span",null,`Buffer[${e.length}]`):n.createElement("span",null,e.constructor.name):n.createElement("span",null,"Object");case"function":return n.createElement("span",null,n.createElement("span",{style:a("objectValueFunctionPrefix")},"\u0192\xA0"),n.createElement("span",{style:a("objectValueFunctionName")},e.name,"()"));case"symbol":return n.createElement("span",{style:a("objectValueSymbol")},e.toString());default:return n.createElement("span",null)}},"ObjectValue"),Qs=Object.prototype.hasOwnProperty,_2=Object.prototype.propertyIsEnumerable;function Qr(e,t){let r=Object.getOwnPropertyDescriptor(e,t);if(r.get)try{return r.get()}catch{return r.get}return e[t]}i(Qr,"getPropertyValue");function xa(e,t){return e.length===0?[]:e.slice(1).reduce((r,a)=>r.concat([t,a]),[e[0]])}i(xa,"intersperse");var Aa=i(({data:e})=>{let t=we("ObjectPreview"),r=e;if(typeof r!="object"||r===null||r instanceof Date||r instanceof RegExp)return n.createElement(er,{object:r});if(Array.isArray(r)){let a=t.arrayMaxProperties,o=r.slice(0,a).map((l,u)=>n.createElement(er,{key:u,object:l}));r.length>a&&o.push(n.createElement("span",{key:"ellipsis"},"\u2026"));let c=r.length;return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c===0?"":`(${c})\xA0`),n.createElement("span",{style:t.preview},"[",xa(o,", "),"]"))}else{let a=t.objectMaxProperties,o=[];for(let l in r)if(Qs.call(r,l)){let u;o.length===a-1&&Object.keys(r).length>a&&(u=n.createElement("span",{key:"ellipsis"},"\u2026"));let s=Qr(r,l);if(o.push(n.createElement("span",{key:l},n.createElement(Za,{name:l||'""'}),":\xA0",n.createElement(er,{object:s}),u)),u)break}let c=r.constructor?r.constructor.name:"Object";return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c==="Object"?"":`${c} `),n.createElement("span",{style:t.preview},"{",xa(o,", "),"}"))}},"ObjectPreview"),R2=i(({name:e,data:t})=>typeof e=="string"?n.createElement("span",null,n.createElement(Za,{name:e}),n.createElement("span",null,": "),n.createElement(Aa,{data:t})):n.createElement(Aa,{data:t}),"ObjectRootLabel"),N2=i(({name:e,data:t,isNonenumerable:r=!1})=>{let a=t;return n.createElement("span",null,typeof e=="string"?n.createElement(Za,{name:e,dimmed:r}):n.createElement(Aa,{data:e}),n.createElement("span",null,": "),n.createElement(er,{object:a}))},"ObjectLabel"),L2=i((e,t)=>i(function*(r){if(!(typeof r=="object"&&r!==null||typeof r=="function"))return;let a=Array.isArray(r);if(!a&&r[Symbol.iterator]){let o=0;for(let c of r){if(Array.isArray(c)&&c.length===2){let[l,u]=c;yield{name:l,data:u}}else yield{name:o.toString(),data:c};o++}}else{let o=Object.getOwnPropertyNames(r);t===!0&&!a?o.sort():typeof t=="function"&&o.sort(t);for(let c of o)if(_2.call(r,c)){let l=Qr(r,c);yield{name:c||'""',data:l}}else if(e){let l;try{l=Qr(r,c)}catch{}l!==void 0&&(yield{name:c,data:l,isNonenumerable:!0})}e&&r!==Object.prototype&&(yield{name:"__proto__",data:Object.getPrototypeOf(r),isNonenumerable:!0})}},"objectIterator"),"createIterator"),F2=i(({depth:e,name:t,data:r,isNonenumerable:a})=>e===0?n.createElement(R2,{name:t,data:r}):n.createElement(N2,{name:t,data:r,isNonenumerable:a}),"defaultNodeRenderer"),P2=i(({showNonenumerable:e=!1,sortObjectKeys:t,nodeRenderer:r,...a})=>{let o=L2(e,t),c=r||F2;return n.createElement(Zs,{nodeRenderer:c,dataIterator:o,...a})},"ObjectInspector"),j2=Xa(P2);function ec(e){if(typeof e=="object"){let t=[];if(Array.isArray(e)){let a=e.length;t=[...Array(a).keys()]}else e!==null&&(t=Object.keys(e));let r=t.reduce((a,o)=>{let c=e[o];return typeof c=="object"&&c!==null&&Object.keys(c).reduce((l,u)=>(l.includes(u)||l.push(u),l),a),a},[]);return{rowHeaders:t,colHeaders:r}}}i(ec,"getHeaders");var M2=i(({rows:e,columns:t,rowsData:r})=>{let a=we("TableInspectorDataContainer"),o=we("TableInspectorLeftBorder");return n.createElement("div",{style:a.div},n.createElement("table",{style:a.table},n.createElement("colgroup",null),n.createElement("tbody",null,e.map((c,l)=>n.createElement("tr",{key:c,style:a.tr},n.createElement("td",{style:{...a.td,...o.none}},c),t.map(u=>{let s=r[l];return typeof s=="object"&&s!==null&&Qs.call(s,u)?n.createElement("td",{key:u,style:{...a.td,...o.solid}},n.createElement(er,{object:s[u]})):n.createElement("td",{key:u,style:{...a.td,...o.solid}})}))))))},"DataContainer"),$2=i(e=>n.createElement("div",{style:{position:"absolute",top:1,right:0,bottom:1,display:"flex",alignItems:"center"}},e.children),"SortIconContainer"),U2=i(({sortAscending:e})=>{let t=we("TableInspectorSortIcon"),r=e?"\u25B2":"\u25BC";return n.createElement("div",{style:t},r)},"SortIcon"),mi=i(({sortAscending:e=!1,sorted:t=!1,onClick:r=void 0,borderStyle:a={},children:o,...c})=>{let l=we("TableInspectorTH"),[u,s]=R(!1),d=H(()=>s(!0),[]),m=H(()=>s(!1),[]);return n.createElement("th",{...c,style:{...l.base,...a,...u?l.base[":hover"]:{}},onMouseEnter:d,onMouseLeave:m,onClick:r},n.createElement("div",{style:l.div},o),t&&n.createElement($2,null,n.createElement(U2,{sortAscending:e})))},"TH"),H2=i(({indexColumnText:e="(index)",columns:t=[],sorted:r,sortIndexColumn:a,sortColumn:o,sortAscending:c,onTHClick:l,onIndexTHClick:u})=>{let s=we("TableInspectorHeaderContainer"),d=we("TableInspectorLeftBorder");return n.createElement("div",{style:s.base},n.createElement("table",{style:s.table},n.createElement("tbody",null,n.createElement("tr",null,n.createElement(mi,{borderStyle:d.none,sorted:r&&a,sortAscending:c,onClick:u},e),t.map(m=>n.createElement(mi,{borderStyle:d.solid,key:m,sorted:r&&o===m,sortAscending:c,onClick:l.bind(null,m)},m))))))},"HeaderContainer"),V2=i(({data:e,columns:t})=>{let r=we("TableInspector"),[{sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l},u]=R({sorted:!1,sortIndexColumn:!1,sortColumn:void 0,sortAscending:!1}),s=H(()=>{u(({sortIndexColumn:g,sortAscending:b})=>({sorted:!0,sortIndexColumn:!0,sortColumn:void 0,sortAscending:g?!b:!0}))},[]),d=H(g=>{u(({sortColumn:b,sortAscending:E})=>({sorted:!0,sortIndexColumn:!1,sortColumn:g,sortAscending:g===b?!E:!0}))},[]);if(typeof e!="object"||e===null)return n.createElement("div",null);let{rowHeaders:m,colHeaders:f}=ec(e);t!==void 0&&(f=t);let p=m.map(g=>e[g]),h;if(c!==void 0?h=p.map((g,b)=>typeof g=="object"&&g!==null?[g[c],b]:[void 0,b]):o&&(h=m.map((g,b)=>[m[b],b])),h!==void 0){let g=i((E,v)=>(A,S)=>{let O=E(A),D=E(S),k=typeof O,I=typeof D,F=i((V,z)=>V<z?-1:V>z?1:0,"lt"),$;if(k===I)$=F(O,D);else{let V={string:0,number:1,object:2,symbol:3,boolean:4,undefined:5,function:6};$=F(V[k],V[I])}return v||($=-$),$},"comparator"),b=h.sort(g(E=>E[0],l)).map(E=>E[1]);m=b.map(E=>m[E]),p=b.map(E=>p[E])}return n.createElement("div",{style:r.base},n.createElement(H2,{columns:f,sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l,onTHClick:d,onIndexTHClick:s}),n.createElement(M2,{rows:m,columns:f,rowsData:p}))},"TableInspector"),z2=Xa(V2),q2=80,tc=i(e=>e.childNodes.length===0||e.childNodes.length===1&&e.childNodes[0].nodeType===Node.TEXT_NODE&&e.textContent.length<q2,"shouldInline"),G2=i(({tagName:e,attributes:t,styles:r})=>n.createElement("span",{style:r.base},"<",n.createElement("span",{style:r.tagName},e),(()=>{if(t){let a=[];for(let o=0;o<t.length;o++){let c=t[o];a.push(n.createElement("span",{key:o}," ",n.createElement("span",{style:r.htmlAttributeName},c.name),'="',n.createElement("span",{style:r.htmlAttributeValue},c.value),'"'))}return a}})(),">"),"OpenTag"),fi=i(({tagName:e,isChildNode:t=!1,styles:r})=>n.createElement("span",{style:Object.assign({},r.base,t&&r.offsetLeft)},"</",n.createElement("span",{style:r.tagName},e),">"),"CloseTag"),W2={1:"ELEMENT_NODE",3:"TEXT_NODE",7:"PROCESSING_INSTRUCTION_NODE",8:"COMMENT_NODE",9:"DOCUMENT_NODE",10:"DOCUMENT_TYPE_NODE",11:"DOCUMENT_FRAGMENT_NODE"},K2=i(({isCloseTag:e,data:t,expanded:r})=>{let a=we("DOMNodePreview");if(e)return n.createElement(fi,{styles:a.htmlCloseTag,isChildNode:!0,tagName:t.tagName});switch(t.nodeType){case Node.ELEMENT_NODE:return n.createElement("span",null,n.createElement(G2,{tagName:t.tagName,attributes:t.attributes,styles:a.htmlOpenTag}),tc(t)?t.textContent:!r&&"\u2026",!r&&n.createElement(fi,{tagName:t.tagName,styles:a.htmlCloseTag}));case Node.TEXT_NODE:return n.createElement("span",null,t.textContent);case Node.CDATA_SECTION_NODE:return n.createElement("span",null,"<![CDATA["+t.textContent+"]]>");case Node.COMMENT_NODE:return n.createElement("span",{style:a.htmlComment},"<!--",t.textContent,"-->");case Node.PROCESSING_INSTRUCTION_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_TYPE_NODE:return n.createElement("span",{style:a.htmlDoctype},"<!DOCTYPE ",t.name,t.publicId?` PUBLIC "${t.publicId}"`:"",!t.publicId&&t.systemId?" SYSTEM":"",t.systemId?` "${t.systemId}"`:"",">");case Node.DOCUMENT_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_FRAGMENT_NODE:return n.createElement("span",null,t.nodeName);default:return n.createElement("span",null,W2[t.nodeType])}},"DOMNodePreview"),Y2=i(function*(e){if(e&&e.childNodes){if(tc(e))return;for(let t=0;t<e.childNodes.length;t++){let r=e.childNodes[t];r.nodeType===Node.TEXT_NODE&&r.textContent.trim().length===0||(yield{name:`${r.tagName}[${t}]`,data:r})}e.tagName&&(yield{name:"CLOSE_TAG",data:{tagName:e.tagName},isCloseTag:!0})}},"domIterator"),J2=i(e=>n.createElement(Zs,{nodeRenderer:K2,dataIterator:Y2,...e}),"DOMInspector"),X2=Xa(J2),Z2=A2(w2()),Q2=i(({table:e=!1,data:t,...r})=>e?n.createElement(z2,{data:t,...r}):(0,Z2.default)(t)?n.createElement(X2,{data:t,...r}):n.createElement(j2,{data:t,...r}),"Inspector"),e0=y.div({display:"flex",padding:0,borderLeft:"5px solid transparent",borderBottom:"1px solid transparent",transition:"all 0.1s",alignItems:"flex-start",whiteSpace:"pre"}),t0=y.div(({theme:e})=>({backgroundColor:Xt(.5,e.appBorderColor),color:e.color.inverseText,fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:1,padding:"1px 5px",borderRadius:20,margin:"2px 0px"})),r0=y.div({flex:1,padding:"0 0 0 5px"}),rc=co(({children:e,className:t},r)=>n.createElement(gn,{ref:r,horizontal:!0,vertical:!0,className:t},e));rc.displayName="UnstyledWrapped";var n0=y(rc)({margin:0,padding:"10px 5px 20px"}),a0=Go(({theme:e,...t})=>n.createElement(Q2,{theme:e.addonActionsTheme||"chromeLight",table:!1,...t})),o0=i(({actions:e,onClear:t})=>{let r=Q(null),a=r.current,o=a&&a.scrollHeight-a.scrollTop===a.clientHeight;return j(()=>{o&&(r.current.scrollTop=r.current.scrollHeight)},[o,e.length]),n.createElement(Oe,null,n.createElement(n0,{ref:r},e.map(c=>n.createElement(e0,{key:c.id},c.count>1&&n.createElement(t0,null,c.count),n.createElement(r0,null,n.createElement(a0,{sortObjectKeys:!0,showNonenumerable:!1,name:c.data.name,data:c.data.args??c.data}))))),n.createElement(Mt,{actionItems:[{title:"Clear",onClick:t}]}))},"ActionLogger"),l0=i((e,t)=>{try{return ut(e,t)}catch{return!1}},"safeDeepEqual"),nc=class extends Re{constructor(t){super(t),this.handleStoryChange=i(()=>{let{actions:r}=this.state;r.length>0&&r[0].options.clearOnStoryChange&&this.clearActions()},"handleStoryChange"),this.addAction=i(r=>{this.setState(a=>{let o=[...a.actions],c=o.length&&o[o.length-1];return c&&l0(c.data,r.data)?c.count++:(r.count=1,o.push(r)),{actions:o.slice(0,r.options.limit)}})},"addAction"),this.clearActions=i(()=>{let{api:r}=this.props;r.emit(Gs),this.setState({actions:[]})},"clearActions"),this.mounted=!1,this.state={actions:[]}}componentDidMount(){this.mounted=!0;let{api:t}=this.props;t.on(Ea,this.addAction),t.on(qt,this.handleStoryChange)}componentWillUnmount(){this.mounted=!1;let{api:t}=this.props;t.off(qt,this.handleStoryChange),t.off(Ea,this.addAction)}render(){let{actions:t=[]}=this.state,{active:r}=this.props,a={actions:t,onClear:this.clearActions};return r?n.createElement(o0,{...a}):null}};i(nc,"ActionLogger");var i0=nc,ck=ee.register(lr,e=>{globalThis?.FEATURES?.actions&&ee.add(qs,{title:Ws,type:be.PANEL,render:i(({active:t})=>n.createElement(i0,{api:e,active:!!t}),"render"),paramKey:h2})}),sn="storybook/interactions",ac=`${sn}/panel`,u0="writing-tests/integrations/vitest-addon",s0=`${u0}#what-happens-when-there-are-different-test-results-in-multiple-environments`,c0="writing-stories/play-function#writing-stories-with-the-play-function",De="internal_render_call",xt="storybook/a11y",yk=`${xt}/panel`,Ek=`${xt}/result`,vk=`${xt}/request`,xk=`${xt}/running`,Ak=`${xt}/error`,Ck=`${xt}/manual`,Sk=`${xt}/select`,d0="writing-tests/accessibility-testing",wk=`${d0}#why-are-my-tests-failing-in-different-environments`,oc="storybook/test",kk=`${oc}/test-provider`,p0="STORYBOOK_ADDON_TEST_CHANNEL",m0="writing-tests/integrations/vitest-addon",Ok=`${m0}#what-happens-if-vitest-itself-has-an-error`,f0={id:oc,initialState:{config:{coverage:!1,a11y:!1},watching:!1,cancelling:!1,fatalError:void 0,indexUrl:void 0,previewAnnotations:[],currentRun:{triggeredBy:void 0,config:{coverage:!1,a11y:!1},componentTestCount:{success:0,error:0},a11yCount:{success:0,warning:0,error:0},storyIds:void 0,totalTestCount:void 0,startedAt:void 0,finishedAt:void 0,unhandledErrors:[],coverageSummary:void 0}}},Tk=`UNIVERSAL_STORE:${f0.id}`,h0="storybook/component-test",ft={CALL:"storybook/instrumenter/call",SYNC:"storybook/instrumenter/sync",START:"storybook/instrumenter/start",BACK:"storybook/instrumenter/back",GOTO:"storybook/instrumenter/goto",NEXT:"storybook/instrumenter/next",END:"storybook/instrumenter/end"},g0=Ae(Ep(),1);function lc({onlyFirst:e=!1}={}){let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}i(lc,"ansiRegex");var b0=lc();function ic(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(b0,"")}i(ic,"stripAnsi");function uc(e){return Qa(e)||eo(e)}i(uc,"isTestAssertionError");function Qa(e){return e&&typeof e=="object"&&"name"in e&&typeof e.name=="string"&&e.name==="AssertionError"}i(Qa,"isChaiError");function eo(e){return e&&typeof e=="object"&&"message"in e&&typeof e.message=="string"&&ic(e.message).startsWith("expect(")}i(eo,"isJestError");function sc(e){return new g0.default({escapeXML:!0,fg:e.color.defaultText,bg:e.background.content})}i(sc,"createAnsiToHtmlFilter");function cn(){let e=Ie();return sc(e)}i(cn,"useAnsiToHtmlFilter");var y0=y.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),E0=i(()=>{let[e,t]=R(!0),r=ye().getDocsUrl({subpath:c0,versioned:!0,renderer:!0});return j(()=>{let a=setTimeout(()=>{t(!1)},100);return()=>clearTimeout(a)},[]),e?null:n.createElement("div",null,n.createElement(Ht,{title:"Interactions",description:n.createElement(n.Fragment,null,"Interactions allow you to verify the functional aspects of UIs. Write a play function for your story and you'll see it run here."),footer:n.createElement(y0,null,n.createElement(Te,{href:r,target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs"))}))},"Empty"),v0=Ae(La()),x0=Ae(Fa());function en(e){var t,r,a="";if(e)if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=en(e[t]))&&(a&&(a+=" "),a+=r);else for(t in e)e[t]&&(r=en(t))&&(a&&(a+=" "),a+=r);else typeof e!="boolean"&&!e.call&&(a&&(a+=" "),a+=e);return a}i(en,"toVal");function Pe(){for(var e=0,t,r="";e<arguments.length;)(t=en(arguments[e++]))&&(r&&(r+=" "),r+=t);return r}i(Pe,"default");var to=i(e=>Array.isArray(e)||ArrayBuffer.isView(e)&&!(e instanceof DataView),"isArray"),cc=i(e=>e!==null&&typeof e=="object"&&!to(e)&&!(e instanceof Date)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof WeakMap)&&!(e instanceof WeakSet),"isObject"),A0=i(e=>cc(e)||to(e)||typeof e=="function"||e instanceof Promise,"isKnownObject"),dc=i(e=>{let t=/unique/;return Promise.race([e,t]).then(r=>r===t?["pending"]:["fulfilled",r],r=>["rejected",r])},"getPromiseState"),qe=i(async(e,t,r,a,o,c)=>{let l={key:e,depth:r,value:t,type:"value",parent:void 0};if(t&&A0(t)&&r<100){let u=[],s="object";if(to(t)){for(let d=0;d<t.length;d++)u.push(async()=>{let m=await qe(d.toString(),t[d],r+1,a);return m.parent=l,m});s="array"}else{let d=Object.getOwnPropertyNames(t);a&&d.sort();for(let m=0;m<d.length;m++){let f;try{f=t[d[m]]}catch{}u.push(async()=>{let p=await qe(d[m],f,r+1,a);return p.parent=l,p})}if(typeof t=="function"&&(s="function"),t instanceof Promise){let[m,f]=await dc(t);u.push(async()=>{let p=await qe("<state>",m,r+1,a);return p.parent=l,p}),m!=="pending"&&u.push(async()=>{let p=await qe("<value>",f,r+1,a);return p.parent=l,p}),s="promise"}if(t instanceof Map){let m=Array.from(t.entries()).map(f=>{let[p,h]=f;return{"<key>":p,"<value>":h}});u.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),u.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),s="map"}if(t instanceof Set){let m=Array.from(t.entries()).map(f=>f[1]);u.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),u.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),s="set"}}t!==Object.prototype&&c&&u.push(async()=>{let d=await qe("<prototype>",Object.getPrototypeOf(t),r+1,a,!0);return d.parent=l,d}),l.type=s,l.children=u,l.isPrototype=o}return l},"buildAST"),C0=i((e,t,r)=>qe("root",e,0,t===!1?t:!0,void 0,r===!1?r:!0),"parse"),hi=Ae(Ap()),S0=Ae(Sp()),w0=["children"],Ca=n.createContext({theme:"chrome",colorScheme:"light"}),k0=i(e=>{let{children:t}=e,r=(0,S0.default)(e,w0),a=n.useContext(Ca);return n.createElement(Ca.Provider,{value:(0,hi.default)((0,hi.default)({},a),r)},t)},"ThemeProvider"),dn=i((e,t={})=>{let r=n.useContext(Ca),a=e.theme||r.theme||"chrome",o=e.colorScheme||r.colorScheme||"light",c=Pe(t[a],t[o]);return{currentColorScheme:o,currentTheme:a,themeClass:c}},"useTheme"),gi=Ae(kp()),Yn=Ae(Op()),O0=Ae(Ip()),T0=n.createContext({isChild:!1,depth:0,hasHover:!0}),Jn=T0,Ee={tree:"Tree-tree-fbbbe38",item:"Tree-item-353d6f3",group:"Tree-group-d3c3d8a",label:"Tree-label-d819155",focusWhite:"Tree-focusWhite-f1e00c2",arrow:"Tree-arrow-03ab2e7",hover:"Tree-hover-3cc4e5d",open:"Tree-open-3f1a336",dark:"Tree-dark-1b4aa00",chrome:"Tree-chrome-bcbcac6",light:"Tree-light-09174ee"},I0=["theme","hover","colorScheme","children","label","className","onUpdate","onSelect","open"],tn=i(e=>{let{theme:t,hover:r,colorScheme:a,children:o,label:c,className:l,onUpdate:u,onSelect:s,open:d}=e,m=(0,O0.default)(e,I0),{themeClass:f,currentTheme:p}=dn({theme:t,colorScheme:a},Ee),[h,g]=R(d);j(()=>{g(d)},[d]);let b=i(T=>{g(T),u&&u(T)},"updateState"),E=n.Children.count(o)>0,v=i((T,_)=>{if(T.isSameNode(_||null))return;T.querySelector('[tabindex="-1"]')?.focus(),T.setAttribute("aria-selected","true"),_?.removeAttribute("aria-selected")},"updateFocus"),A=i((T,_)=>{let N=T;for(;N&&N.parentElement;){if(N.getAttribute("role")===_)return N;N=N.parentElement}return null},"getParent"),S=i(T=>{let _=A(T,"tree");return _?Array.from(_.querySelectorAll("li")):[]},"getListElements"),O=i(T=>{let _=A(T,"group"),N=_?.previousElementSibling;if(N&&N.getAttribute("tabindex")==="-1"){let q=N.parentElement,Z=T.parentElement;v(q,Z)}},"moveBack"),D=i((T,_)=>{let N=S(T);N.forEach(q=>{q.removeAttribute("aria-selected")}),_==="start"&&N[0]&&v(N[0]),_==="end"&&N[N.length-1]&&v(N[N.length-1])},"moveHome"),k=i((T,_)=>{let N=S(T)||[];for(let q=0;q<N.length;q++){let Z=N[q];if(Z.getAttribute("aria-selected")==="true"){_==="up"&&N[q-1]?v(N[q-1],Z):_==="down"&&N[q+1]&&v(N[q+1],Z);return}}v(N[0])},"moveFocusAdjacent"),I=i((T,_)=>{let N=T.target;(T.key==="Enter"||T.key===" ")&&b(!h),T.key==="ArrowRight"&&h&&!_?k(N,"down"):T.key==="ArrowRight"&&b(!0),T.key==="ArrowLeft"&&(!h||_)?O(N):T.key==="ArrowLeft"&&b(!1),T.key==="ArrowDown"&&k(N,"down"),T.key==="ArrowUp"&&k(N,"up"),T.key==="Home"&&D(N,"start"),T.key==="End"&&D(N,"end")},"handleKeypress"),F=i((T,_)=>{let N=T.target,q=A(N,"treeitem"),Z=S(N)||[],Ft=!1;for(let At=0;At<Z.length;At++){let Pt=Z[At];if(Pt.getAttribute("aria-selected")==="true"){q&&(Ft=!0,v(q,Pt));break}}!Ft&&q&&v(q),_||b(!h)},"handleClick"),$=i(T=>{let _=T.currentTarget;!_.contains(document.activeElement)&&_.getAttribute("role")==="tree"&&_.setAttribute("tabindex","0")},"handleBlur"),V=i(T=>{let _=T.target;if(_.getAttribute("role")==="tree"){let N=_.querySelector('[aria-selected="true"]');N?v(N):k(_,"down"),_.setAttribute("tabindex","-1")}},"handleFocus"),z=i(()=>{s?.()},"handleButtonFocus"),W=i(T=>{let _=T*.9+.3;return{paddingLeft:`${_}em`,width:`calc(100% - ${_}em)`}},"getPaddingStyles"),{isChild:M,depth:x,hasHover:C}=n.useContext(Jn),w=C?r:!1;if(!M)return n.createElement("ul",(0,Yn.default)({role:"tree",tabIndex:0,className:Pe(Ee.tree,Ee.group,f,l),onFocus:V,onBlur:$},m),n.createElement(Jn.Provider,{value:{isChild:!0,depth:0,hasHover:w}},n.createElement(tn,e)));if(!E)return n.createElement("li",(0,Yn.default)({role:"treeitem",className:Ee.item},m),n.createElement("div",{role:"button",className:Pe(Ee.label,{[Ee.hover]:w,[Ee.focusWhite]:p==="firefox"}),tabIndex:-1,style:W(x),onKeyDown:i(T=>{I(T,M)},"onKeyDown"),onClick:i(T=>F(T,!0),"onClick"),onFocus:z},n.createElement("span",null,c)));let B=Pe(Ee.arrow,{[Ee.open]:h});return n.createElement("li",{role:"treeitem","aria-expanded":h,className:Ee.item},n.createElement("div",{role:"button",tabIndex:-1,className:Pe(Ee.label,{[Ee.hover]:w,[Ee.focusWhite]:p==="firefox"}),style:W(x),onClick:i(T=>F(T),"onClick"),onKeyDown:i(T=>I(T),"onKeyDown"),onFocus:z},n.createElement("span",null,n.createElement("span",{"aria-hidden":!0,className:B}),n.createElement("span",null,c))),n.createElement("ul",(0,Yn.default)({role:"group",className:Pe(l,Ee.group)},m),h&&n.Children.map(o,T=>n.createElement(Jn.Provider,{value:{isChild:!0,depth:x+1,hasHover:w}},T))))},"Tree");tn.defaultProps={open:!1,hover:!0};var D0=Ae(La()),B0=Ae(Fa()),J={"object-inspector":"ObjectInspector-object-inspector-0c33e82",objectInspector:"ObjectInspector-object-inspector-0c33e82","object-label":"ObjectInspector-object-label-b81482b",objectLabel:"ObjectInspector-object-label-b81482b",text:"ObjectInspector-text-25f57f3",key:"ObjectInspector-key-4f712bb",value:"ObjectInspector-value-f7ec2e5",string:"ObjectInspector-string-c496000",regex:"ObjectInspector-regex-59d45a3",error:"ObjectInspector-error-b818698",boolean:"ObjectInspector-boolean-2dd1642",number:"ObjectInspector-number-a6daabb",undefined:"ObjectInspector-undefined-3a68263",null:"ObjectInspector-null-74acb50",function:"ObjectInspector-function-07bbdcd","function-decorator":"ObjectInspector-function-decorator-3d22c24",functionDecorator:"ObjectInspector-function-decorator-3d22c24",prototype:"ObjectInspector-prototype-f2449ee",dark:"ObjectInspector-dark-0c96c97",chrome:"ObjectInspector-chrome-2f3ca98",light:"ObjectInspector-light-78bef54"},_0=["ast","theme","showKey","colorScheme","className"],ve=i((e,t,r,a,o)=>{let c=e.includes("-")?`"${e}"`:e,l=o<=0;return n.createElement("span",{className:J.text},!l&&a&&n.createElement(n.Fragment,null,n.createElement("span",{className:J.key},c),n.createElement("span",null,":\xA0")),n.createElement("span",{className:r},t))},"buildValue"),pc=i(e=>{let{ast:t,theme:r,showKey:a,colorScheme:o,className:c}=e,l=(0,B0.default)(e,_0),{themeClass:u}=dn({theme:r,colorScheme:o},J),[s,d]=R(n.createElement("span",null)),m=n.createElement("span",null);return j(()=>{t.value instanceof Promise&&i(async f=>{d(ve(t.key,`Promise { "${await dc(f)}" }`,J.key,a,t.depth))},"waitForPromiseResult")(t.value)},[t,a]),typeof t.value=="number"||typeof t.value=="bigint"?m=ve(t.key,String(t.value),J.number,a,t.depth):typeof t.value=="boolean"?m=ve(t.key,String(t.value),J.boolean,a,t.depth):typeof t.value=="string"?m=ve(t.key,`"${t.value}"`,J.string,a,t.depth):typeof t.value>"u"?m=ve(t.key,"undefined",J.undefined,a,t.depth):typeof t.value=="symbol"?m=ve(t.key,t.value.toString(),J.string,a,t.depth):typeof t.value=="function"?m=ve(t.key,`${t.value.name}()`,J.key,a,t.depth):typeof t.value=="object"&&(t.value===null?m=ve(t.key,"null",J.null,a,t.depth):Array.isArray(t.value)?m=ve(t.key,`Array(${t.value.length})`,J.key,a,t.depth):t.value instanceof Date?m=ve(t.key,`Date ${t.value.toString()}`,J.value,a,t.depth):t.value instanceof RegExp?m=ve(t.key,t.value.toString(),J.regex,a,t.depth):t.value instanceof Error?m=ve(t.key,t.value.toString(),J.error,a,t.depth):cc(t.value)?m=ve(t.key,"{\u2026}",J.key,a,t.depth):m=ve(t.key,t.value.constructor.name,J.key,a,t.depth)),n.createElement("span",(0,D0.default)({className:Pe(u,c)},l),s,m)},"ObjectValue");pc.defaultProps={showKey:!0};var mc=pc,Tt=Ae(La()),R0=Ae(Fa()),N0=["ast","theme","previewMax","open","colorScheme","className"],ir=i((e,t,r)=>{let a=[];for(let o=0;o<e.length;o++){let c=e[o];if(c.isPrototype||(a.push(n.createElement(mc,{key:c.key,ast:c,showKey:r})),o<e.length-1?a.push(", "):a.push(" ")),c.isPrototype&&o===e.length-1&&(a.pop(),a.push(" ")),o===t-1&&e.length>t){a.push("\u2026 ");break}}return a},"buildPreview"),L0=i((e,t,r,a)=>{let o=e.value.length;return t?n.createElement("span",null,"Array(",o,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Array":""}(${o}) [ `),ir(e.children,r,!1),n.createElement("span",null,"]"))},"getArrayLabel"),F0=i((e,t,r,a)=>e.isPrototype?n.createElement("span",null,`Object ${a==="firefox"?"{ \u2026 }":""}`):t?n.createElement("span",null,"{\u2026}"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Object ":""}{ `),ir(e.children,r,!0),n.createElement("span",null,"}")),"getObjectLabel"),P0=i((e,t,r)=>t?n.createElement("span",null,`Promise { "${String(e.children[0].value)}" }`):n.createElement(n.Fragment,null,n.createElement("span",null,"Promise { "),ir(e.children,r,!0),n.createElement("span",null,"}")),"getPromiseLabel"),j0=i((e,t,r,a)=>{let{size:o}=e.value;return t?n.createElement("span",null,`Map(${o})`):n.createElement(n.Fragment,null,n.createElement("span",null,`Map${a==="chrome"?`(${o})`:""} { `),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getMapLabel"),M0=i((e,t,r)=>{let{size:a}=e.value;return t?n.createElement("span",null,"Set(",a,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`Set(${e.value.size}) {`),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getSetLabel"),fc=i(e=>{let{ast:t,theme:r,previewMax:a,open:o,colorScheme:c,className:l}=e,u=(0,R0.default)(e,N0),{themeClass:s,currentTheme:d}=dn({theme:r,colorScheme:c},J),m=t.isPrototype||!1,f=Pe(J.objectLabel,s,l,{[J.prototype]:m}),p=t.depth<=0,h=i(()=>n.createElement("span",{className:m?J.prototype:J.key},p?"":`${t.key}: `),"Key");return t.type==="array"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),L0(t,o,a,d)):t.type==="function"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),d==="chrome"&&n.createElement("span",{className:J.functionDecorator},"\u0192 "),n.createElement("span",{className:Pe({[J.function]:!m})},`${t.value.name}()`)):t.type==="promise"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),P0(t,o,a)):t.type==="map"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),j0(t,o,a,d)):t.type==="set"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),M0(t,o,a)):n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),F0(t,o,a,d))},"ObjectLabel");fc.defaultProps={previewMax:8,open:!1};var $0=fc,ro=i(e=>{let{ast:t,expandLevel:r,depth:a}=e,[o,c]=R(),[l,u]=R(a<r);return j(()=>{i(async()=>{if(t.type!=="value"){let s=t.children.map(f=>f()),d=await Promise.all(s),m=(0,gi.default)((0,gi.default)({},t),{},{children:d});c(m)}},"resolve")()},[t]),o?n.createElement(tn,{hover:!1,open:l,label:n.createElement($0,{open:l,ast:o}),onSelect:i(()=>{var s;(s=e.onSelect)===null||s===void 0||s.call(e,t)},"onSelect"),onUpdate:i(s=>{u(s)},"onUpdate")},o.children.map(s=>n.createElement(ro,{key:s.key,ast:s,depth:a+1,expandLevel:r,onSelect:e.onSelect}))):n.createElement(tn,{hover:!1,label:n.createElement(mc,{ast:t}),onSelect:i(()=>{var s;(s=e.onSelect)===null||s===void 0||s.call(e,t)},"onSelect")})},"ObjectInspectorItem");ro.defaultProps={expandLevel:0,depth:0};var U0=ro,H0=["data","expandLevel","sortKeys","includePrototypes","className","theme","colorScheme","onSelect"],hc=i(e=>{let{data:t,expandLevel:r,sortKeys:a,includePrototypes:o,className:c,theme:l,colorScheme:u,onSelect:s}=e,d=(0,x0.default)(e,H0),[m,f]=R(void 0),{themeClass:p,currentTheme:h,currentColorScheme:g}=dn({theme:l,colorScheme:u},J);return j(()=>{i(async()=>{f(await C0(t,a,o))},"runParser")()},[t,a,o]),n.createElement("div",(0,v0.default)({className:Pe(J.objectInspector,c,p)},d),m&&n.createElement(k0,{theme:h,colorScheme:g},n.createElement(U0,{ast:m,expandLevel:r,onSelect:s})))},"ObjectInspector");hc.defaultProps={expandLevel:0,sortKeys:!0,includePrototypes:!0};var V0={base:"#444",nullish:"#7D99AA",string:"#16B242",number:"#5D40D0",boolean:"#f41840",objectkey:"#698394",instance:"#A15C20",function:"#EA7509",muted:"#7D99AA",tag:{name:"#6F2CAC",suffix:"#1F99E5"},date:"#459D9C",error:{name:"#D43900",message:"#444"},regex:{source:"#A15C20",flags:"#EA7509"},meta:"#EA7509",method:"#0271B6"},z0={base:"#eee",nullish:"#aaa",string:"#5FE584",number:"#6ba5ff",boolean:"#ff4191",objectkey:"#accfe6",instance:"#E3B551",function:"#E3B551",muted:"#aaa",tag:{name:"#f57bff",suffix:"#8EB5FF"},date:"#70D4D3",error:{name:"#f40",message:"#eee"},regex:{source:"#FAD483",flags:"#E3B551"},meta:"#FAD483",method:"#5EC1FF"},se=i(()=>{let{base:e}=Ie();return e==="dark"?z0:V0},"useThemeColors"),q0=/[^A-Z0-9]/i,bi=/[\s.,…]+$/gm,gc=i((e,t)=>{if(e.length<=t)return e;for(let r=t-1;r>=0;r-=1)if(q0.test(e[r])&&r>10)return`${e.slice(0,r).replace(bi,"")}\u2026`;return`${e.slice(0,t).replace(bi,"")}\u2026`},"ellipsize"),G0=i(e=>{try{return JSON.stringify(e,null,1)}catch{return String(e)}},"stringify"),bc=i((e,t)=>e.flatMap((r,a)=>a===e.length-1?[r]:[r,n.cloneElement(t,{key:`sep${a}`})]),"interleave"),Et=i(({value:e,nested:t,showObjectInspector:r,callsById:a,...o})=>{switch(!0){case e===null:return n.createElement(W0,{...o});case e===void 0:return n.createElement(K0,{...o});case Array.isArray(e):return n.createElement(Z0,{...o,value:e,callsById:a});case typeof e=="string":return n.createElement(Y0,{...o,value:e});case typeof e=="number":return n.createElement(J0,{...o,value:e});case typeof e=="boolean":return n.createElement(X0,{...o,value:e});case Object.prototype.hasOwnProperty.call(e,"__date__"):return n.createElement(ng,{...o,...e.__date__});case Object.prototype.hasOwnProperty.call(e,"__error__"):return n.createElement(ag,{...o,...e.__error__});case Object.prototype.hasOwnProperty.call(e,"__regexp__"):return n.createElement(og,{...o,...e.__regexp__});case Object.prototype.hasOwnProperty.call(e,"__function__"):return n.createElement(tg,{...o,...e.__function__});case Object.prototype.hasOwnProperty.call(e,"__symbol__"):return n.createElement(lg,{...o,...e.__symbol__});case Object.prototype.hasOwnProperty.call(e,"__element__"):return n.createElement(rg,{...o,...e.__element__});case Object.prototype.hasOwnProperty.call(e,"__class__"):return n.createElement(eg,{...o,...e.__class__});case Object.prototype.hasOwnProperty.call(e,"__callId__"):return n.createElement(no,{call:a?.get(e.__callId__),callsById:a});case Object.prototype.toString.call(e)==="[object Object]":return n.createElement(Q0,{value:e,showInspector:r,callsById:a,...o});default:return n.createElement(ig,{value:e,...o})}},"Node"),W0=i(e=>{let t=se();return n.createElement("span",{style:{color:t.nullish},...e},"null")},"NullNode"),K0=i(e=>{let t=se();return n.createElement("span",{style:{color:t.nullish},...e},"undefined")},"UndefinedNode"),Y0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.string},...t},JSON.stringify(gc(e,50)))},"StringNode"),J0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.number},...t},e)},"NumberNode"),X0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.boolean},...t},String(e))},"BooleanNode"),Z0=i(({value:e,nested:t=!1,callsById:r})=>{let a=se();if(t)return n.createElement("span",{style:{color:a.base}},"[\u2026]");let o=e.slice(0,3).map((l,u)=>n.createElement(Et,{key:`${u}--${JSON.stringify(l)}`,value:l,nested:!0,callsById:r})),c=bc(o,n.createElement("span",null,", "));return e.length<=3?n.createElement("span",{style:{color:a.base}},"[",c,"]"):n.createElement("span",{style:{color:a.base}},"(",e.length,") [",c,", \u2026]")},"ArrayNode"),Q0=i(({showInspector:e,value:t,callsById:r,nested:a=!1})=>{let o=Ie().base==="dark",c=se();if(e)return n.createElement(n.Fragment,null,n.createElement(hc,{id:"interactions-object-inspector",data:t,includePrototypes:!1,colorScheme:o?"dark":"light"}));if(a)return n.createElement("span",{style:{color:c.base}},"{\u2026}");let l=bc(Object.entries(t).slice(0,2).map(([u,s])=>n.createElement(Oe,{key:u},n.createElement("span",{style:{color:c.objectkey}},u,": "),n.createElement(Et,{value:s,callsById:r,nested:!0}))),n.createElement("span",null,", "));return Object.keys(t).length<=2?n.createElement("span",{style:{color:c.base}},"{ ",l," }"):n.createElement("span",{style:{color:c.base}},"(",Object.keys(t).length,") ","{ ",l,", \u2026 }")},"ObjectNode"),eg=i(({name:e})=>{let t=se();return n.createElement("span",{style:{color:t.instance}},e)},"ClassNode"),tg=i(({name:e})=>{let t=se();return e?n.createElement("span",{style:{color:t.function}},e):n.createElement("span",{style:{color:t.nullish,fontStyle:"italic"}},"anonymous")},"FunctionNode"),rg=i(({prefix:e,localName:t,id:r,classNames:a=[],innerText:o})=>{let c=e?`${e}:${t}`:t,l=se();return n.createElement("span",{style:{wordBreak:"keep-all"}},n.createElement("span",{key:`${c}_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_tag`,style:{color:l.tag.name}},c),n.createElement("span",{key:`${c}_suffix`,style:{color:l.tag.suffix}},r?`#${r}`:a.reduce((u,s)=>`${u}.${s}`,"")),n.createElement("span",{key:`${c}_gt`,style:{color:l.muted}},">"),!r&&a.length===0&&o&&n.createElement(n.Fragment,null,n.createElement("span",{key:`${c}_text`},o),n.createElement("span",{key:`${c}_close_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_close_tag`,style:{color:l.tag.name}},"/",c),n.createElement("span",{key:`${c}_close_gt`,style:{color:l.muted}},">")))},"ElementNode"),ng=i(({value:e})=>{let t=new Date(e);isNaN(Number(t))&&(tt.warn("Invalid date value:",e),t=null);let r=se();if(!t)return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},"Invalid date");let[a,o,c]=t.toISOString().split(/[T.Z]/);return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},a,n.createElement("span",{style:{opacity:.7}},"T"),o==="00:00:00"?n.createElement("span",{style:{opacity:.7}},o):o,c==="000"?n.createElement("span",{style:{opacity:.7}},".",c):`.${c}`,n.createElement("span",{style:{opacity:.7}},"Z"))},"DateNode"),ag=i(({name:e,message:t})=>{let r=se();return n.createElement("span",{style:{color:r.error.name}},e,t&&": ",t&&n.createElement("span",{style:{color:r.error.message},title:t.length>50?t:""},gc(t,50)))},"ErrorNode"),og=i(({flags:e,source:t})=>{let r=se();return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.regex.flags}},"/",n.createElement("span",{style:{color:r.regex.source}},t),"/",e)},"RegExpNode"),lg=i(({description:e})=>{let t=se();return n.createElement("span",{style:{whiteSpace:"nowrap",color:t.instance}},"Symbol(",e&&n.createElement("span",{style:{color:t.meta}},'"',e,'"'),")")},"SymbolNode"),ig=i(({value:e})=>{let t=se();return n.createElement("span",{style:{color:t.meta}},G0(e))},"OtherNode"),ug=i(({label:e})=>{let t=se(),{typography:r}=Ie();return n.createElement("span",{style:{color:t.base,fontFamily:r.fonts.base,fontSize:r.size.s2-1}},e)},"StepNode"),no=i(({call:e,callsById:t})=>{if(!e)return null;if(e.method==="step"&&e.path?.length===0)return n.createElement(ug,{label:e.args[0]});let r=e.path?.flatMap((c,l)=>{let u=c.__callId__;return[u?n.createElement(no,{key:`elem${l}`,call:t?.get(u),callsById:t}):n.createElement("span",{key:`elem${l}`},c),n.createElement("wbr",{key:`wbr${l}`}),n.createElement("span",{key:`dot${l}`},".")]}),a=e.args?.flatMap((c,l,u)=>{let s=n.createElement(Et,{key:`node${l}`,value:c,callsById:t});return l<u.length-1?[s,n.createElement("span",{key:`comma${l}`},",\xA0"),n.createElement("wbr",{key:`wbr${l}`})]:[s]}),o=se();return n.createElement(n.Fragment,null,n.createElement("span",{style:{color:o.base}},r),n.createElement("span",{style:{color:o.method}},e.method),n.createElement("span",{style:{color:o.base}},"(",n.createElement("wbr",null),a,n.createElement("wbr",null),")"))},"MethodCall"),yi=i((e,t=0)=>{for(let r=t,a=1;r<e.length;r+=1)if(e[r]==="("?a+=1:e[r]===")"&&(a-=1),a===0)return e.slice(t,r);return""},"getParams"),Xn=i(e=>{try{return e==="undefined"?void 0:JSON.parse(e)}catch{return e}},"parseValue"),sg=y.span(({theme:e})=>({color:e.base==="light"?e.color.positiveText:e.color.positive})),cg=y.span(({theme:e})=>({color:e.base==="light"?e.color.negativeText:e.color.negative})),Zn=i(({value:e,parsed:t})=>t?n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#D43900"}}):n.createElement(cg,null,e),"Received"),Qn=i(({value:e,parsed:t})=>t?typeof e=="string"&&e.startsWith("called with")?n.createElement(n.Fragment,null,e):n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#16B242"}}):n.createElement(sg,null,e),"Expected"),Ei=i(({message:e,style:t={}})=>{let r=cn(),a=e.split(`
`);return n.createElement("pre",{style:{margin:0,padding:"8px 10px 8px 36px",fontSize:Le.size.s1,...t}},a.flatMap((o,c)=>{if(o.startsWith("expect(")){let f=yi(o,7),p=f?7+f.length:0,h=f&&o.slice(p).match(/\.(to|last|nth)[A-Z]\w+\(/);if(h){let g=p+(h.index??0)+h[0].length,b=yi(o,g);if(b)return["expect(",n.createElement(Zn,{key:`received_${f}`,value:f}),o.slice(p,g),n.createElement(Qn,{key:`expected_${b}`,value:b}),o.slice(g+b.length),n.createElement("br",{key:`br${c}`})]}}if(o.match(/^\s*- /))return[n.createElement(Qn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];if(o.match(/^\s*\+ /)||o.match(/^Received: $/))return[n.createElement(Zn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];let[,l,u]=o.match(/^(Expected|Received): (.*)$/)||[];if(l&&u)return l==="Expected"?["Expected: ",n.createElement(Qn,{key:o+c,value:Xn(u),parsed:!0}),n.createElement("br",{key:`br${c}`})]:["Received: ",n.createElement(Zn,{key:o+c,value:Xn(u),parsed:!0}),n.createElement("br",{key:`br${c}`})];let[,s,d]=o.match(/(Expected number|Received number|Number) of calls: (\d+)$/i)||[];if(s&&d)return[`${s} of calls: `,n.createElement(Et,{key:o+c,value:Number(d)}),n.createElement("br",{key:`br${c}`})];let[,m]=o.match(/^Received has value: (.+)$/)||[];return m?["Received has value: ",n.createElement(Et,{key:o+c,value:Xn(m)}),n.createElement("br",{key:`br${c}`})]:[n.createElement("span",{key:o+c,dangerouslySetInnerHTML:{__html:r.toHtml(o)}}),n.createElement("br",{key:`br${c}`})]}))},"MatcherResult"),dg=y.div({width:14,height:14,display:"flex",alignItems:"center",justifyContent:"center"}),yc=i(({status:e})=>{let t=Ie();switch(e){case"done":return n.createElement(hr,{color:t.color.positive,"data-testid":"icon-done"});case"error":return n.createElement(No,{color:t.color.negative,"data-testid":"icon-error"});case"active":return n.createElement(Do,{color:t.color.secondary,"data-testid":"icon-active"});case"waiting":return n.createElement(dg,{"data-testid":"icon-waiting"},n.createElement(br,{color:Y(.5,"#CCCCCC"),size:6}));default:return null}},"StatusIcon"),pg=y.div({fontFamily:Le.fonts.mono,fontSize:Le.size.s1,overflowWrap:"break-word",inlineSize:"calc( 100% - 40px )"}),mg=y("div",{shouldForwardProp:i(e=>!["call","pausedAt"].includes(e.toString()),"shouldForwardProp")})(({theme:e,call:t})=>({position:"relative",display:"flex",flexDirection:"column",borderBottom:`1px solid ${e.appBorderColor}`,fontFamily:Le.fonts.base,fontSize:13,...t.status==="error"&&{backgroundColor:e.base==="dark"?Y(.93,e.color.negative):e.background.warning},paddingLeft:(t.ancestors?.length??0)*20}),({theme:e,call:t,pausedAt:r})=>r===t.id&&{"&::before":{content:'""',position:"absolute",top:-5,zIndex:1,borderTop:"4.5px solid transparent",borderLeft:`7px solid ${e.color.warning}`,borderBottom:"4.5px solid transparent"},"&::after":{content:'""',position:"absolute",top:-1,zIndex:1,width:"100%",borderTop:`1.5px solid ${e.color.warning}`}}),fg=y.div(({theme:e,isInteractive:t})=>({display:"flex","&:hover":t?{}:{background:e.background.hoverable}})),hg=y("button",{shouldForwardProp:i(e=>!["call"].includes(e.toString()),"shouldForwardProp")})(({theme:e,disabled:t,call:r})=>({flex:1,display:"grid",background:"none",border:0,gridTemplateColumns:"15px 1fr",alignItems:"center",minHeight:40,margin:0,padding:"8px 15px",textAlign:"start",cursor:t||r.status==="error"?"default":"pointer","&:focus-visible":{outline:0,boxShadow:`inset 3px 0 0 0 ${r.status==="error"?e.color.warning:e.color.secondary}`,background:r.status==="error"?"transparent":e.background.hoverable},"& > div":{opacity:r.status==="waiting"?.5:1}})),gg=y.div({display:"flex",alignItems:"center",padding:6}),bg=y(K)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),yg=y(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),ea=y("div")(({theme:e})=>({padding:"8px 10px 8px 36px",fontSize:Le.size.s1,color:e.color.defaultText,pre:{margin:0,padding:0}})),Eg=y.span(({theme:e})=>({color:e.base==="dark"?"#5EC1FF":"#0271B6"})),vg=y.span(({theme:e})=>({color:e.base==="dark"?"#eee":"#444"})),xg=y.p(({theme:e})=>({color:e.base==="dark"?e.color.negative:e.color.negativeText,fontSize:e.typography.size.s2,maxWidth:500,textWrap:"balance"})),Ag=i(({exception:e})=>{let t=cn();if(!e)return null;if(e.callId===De)return P(ea,null,P("pre",null,P(Eg,null,e.name,":")," ",P(vg,null,e.message)),P(xg,null,"The component failed to render properly. Automated component tests will not run until this is resolved. Check the full error message in Storybook\u2019s canvas to debug."));if(eo(e))return P(Ei,{...e});if(Qa(e))return P(ea,null,P(Ei,{message:`${e.message}${e.diff?`

${e.diff}`:""}`,style:{padding:0}}),P("p",null,"See the full stack trace in the browser console."));let r=e.message.split(`

`),a=r.length>1;return P(ea,null,P("pre",{dangerouslySetInnerHTML:{__html:t.toHtml(r[0])}}),a&&P("p",null,"See the full stack trace in the browser console."))},"Exception"),Cg=i(({call:e,callsById:t,controls:r,controlStates:a,childCallIds:o,isHidden:c,isCollapsed:l,toggleCollapsed:u,pausedAt:s})=>{let[d,m]=R(!1),f=!a.goto||!e.interceptable||!!e.ancestors?.length;return c||e.id===De?null:P(mg,{call:e,pausedAt:s},P(fg,{isInteractive:f},P(hg,{"aria-label":"Interaction step",call:e,onClick:()=>r.goto(e.id),disabled:f,onMouseEnter:()=>a.goto&&m(!0),onMouseLeave:()=>a.goto&&m(!1)},P(yc,{status:d?"active":e.status}),P(pg,{style:{marginLeft:6,marginBottom:1}},P(no,{call:e,callsById:t}))),P(gg,null,(o?.length??0)>0&&P(oe,{hasChrome:!1,tooltip:P(yg,{note:`${l?"Show":"Hide"} interactions`})},P(bg,{onClick:u},P(So,null))))),e.status==="error"&&e.exception?.callId===e.id&&P(Ag,{exception:e.exception}))},"Interaction"),Sg={done:"positive",error:"negative",active:"warning",waiting:"warning"},wg=y.div(({theme:e,status:t})=>({padding:"4px 6px 4px 8px",borderRadius:"4px",backgroundColor:e.color[Sg[t]],color:"white",fontFamily:Le.fonts.base,textTransform:"uppercase",fontSize:Le.size.s1,letterSpacing:3,fontWeight:Le.weight.bold,width:65,textAlign:"center"})),kg={done:"Pass",error:"Fail",active:"Runs",waiting:"Runs"},Og=i(({status:e})=>{let t=kg[e];return n.createElement(wg,{"aria-label":"Status of the test run",status:e},t)},"StatusBadge"),Tg=y.div(({theme:e})=>({boxShadow:`${e.appBorderColor} 0 -1px 0 0 inset`,background:e.background.app,position:"sticky",top:0,zIndex:1})),Ig=y.nav(({theme:e})=>({height:40,display:"flex",alignItems:"center",justifyContent:"space-between",paddingLeft:15})),Dg=y(ge)(({theme:e})=>({borderRadius:4,padding:6,color:e.textMutedColor,"&:not(:disabled)":{"&:hover,&:focus-visible":{color:e.color.secondary}}})),Jt=y(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),tr=y(K)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),Bg=y(bn)({marginTop:0}),_g=y(fn)(({theme:e})=>({color:e.textMutedColor,justifyContent:"flex-end",textAlign:"right",whiteSpace:"nowrap",marginTop:"auto",marginBottom:1,paddingRight:15,fontSize:13})),vi=y.div({display:"flex",alignItems:"center"}),Rg=y(tr)({marginLeft:9}),Ng=y(Dg)({marginLeft:9,marginRight:9,marginBottom:1,lineHeight:"12px"}),Lg=y(tr)(({theme:e,animating:t,disabled:r})=>({opacity:r?.5:1,svg:{animation:t?`${e.animation.rotate360} 200ms ease-out`:void 0}})),Fg=i(({controls:e,controlStates:t,status:r,storyFileName:a,onScrollToEnd:o})=>{let c=r==="error"?"Scroll to error":"Scroll to end";return n.createElement(Tg,null,n.createElement(Ut,null,n.createElement(Ig,{"aria-label":"Component tests toolbar"},n.createElement(vi,null,n.createElement(Og,{status:r}),n.createElement(Ng,{onClick:o,disabled:!o},c),n.createElement(Bg,null),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to start"})},n.createElement(Rg,{"aria-label":"Go to start",onClick:e.start,disabled:!t.start},n.createElement(_o,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go back"})},n.createElement(tr,{"aria-label":"Go back",onClick:e.back,disabled:!t.back},n.createElement(Io,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go forward"})},n.createElement(tr,{"aria-label":"Go forward",onClick:e.next,disabled:!t.next},n.createElement(Bo,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to end"})},n.createElement(tr,{"aria-label":"Go to end",onClick:e.end,disabled:!t.end},n.createElement(xo,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Rerun"})},n.createElement(Lg,{"aria-label":"Rerun",onClick:e.rerun},n.createElement(Fo,null)))),a&&n.createElement(vi,null,n.createElement(_g,null,a)))))},"Subnav"),Pg=y.div(({theme:{color:e,typography:t,background:r}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:r.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative",code:{fontSize:`${t.size.s1-1}px`,color:"inherit",margin:"0 0.2em",padding:"0 0.2em",background:"rgba(255, 255, 255, 0.8)",borderRadius:"2px",boxShadow:"0 0 0 1px rgba(0, 0, 0, 0.1)"}})),jg=i(({browserTestStatus:e})=>{let t=ye().getDocsUrl({subpath:s0,versioned:!0,renderer:!0}),[r,a]=e==="error"?["the CLI","this browser"]:["this browser","the CLI"];return n.createElement(Pg,null,"This interaction test passed in ",r,", but the tests failed in ",a,"."," ",n.createElement(Te,{href:t,target:"_blank",withArrow:!0},"Learn what could cause this"))},"TestDiscrepancyMessage"),Mg=y.div(({theme:e})=>({height:"100%",background:e.background.content})),xi=y.div(({theme:e})=>({borderBottom:`1px solid ${e.appBorderColor}`,backgroundColor:e.base==="dark"?Y(.93,e.color.negative):e.background.warning,padding:15,fontSize:e.typography.size.s2-1,lineHeight:"19px"})),ta=y.code(({theme:e})=>({margin:"0 1px",padding:3,fontSize:e.typography.size.s1-1,lineHeight:1,verticalAlign:"top",background:"rgba(0, 0, 0, 0.05)",border:`1px solid ${e.appBorderColor}`,borderRadius:3})),Ai=y.div({paddingBottom:4,fontWeight:"bold"}),$g=y.p({margin:0,padding:"0 0 20px"}),Ci=y.pre(({theme:e})=>({margin:0,padding:0,"&:not(:last-child)":{paddingBottom:16},fontSize:e.typography.size.s1-1})),Ug=he(i(function({calls:e,controls:t,controlStates:r,interactions:a,fileName:o,hasException:c,caughtException:l,unhandledErrors:u,isPlaying:s,pausedAt:d,onScrollToEnd:m,endRef:f,hasResultMismatch:p,browserTestStatus:h}){let g=cn();return P(Mg,null,p&&P(jg,{browserTestStatus:h}),(a.length>0||c)&&P(Fg,{controls:t,controlStates:r,status:h,storyFileName:o,onScrollToEnd:m}),P("div",{"aria-label":"Interactions list"},a.map(b=>P(Cg,{key:b.id,call:b,callsById:e,controls:t,controlStates:r,childCallIds:b.childCallIds,isHidden:b.isHidden,isCollapsed:b.isCollapsed,toggleCollapsed:b.toggleCollapsed,pausedAt:d}))),l&&!uc(l)&&P(xi,null,P(Ai,null,"Caught exception in ",P(ta,null,"play")," function"),P(Ci,{"data-chromatic":"ignore",dangerouslySetInnerHTML:{__html:g.toHtml(Sa(l))}})),u&&P(xi,null,P(Ai,null,"Unhandled Errors"),P($g,null,"Found ",u.length," unhandled error",u.length>1?"s":""," ","while running the play function. This might cause false positive assertions. Resolve unhandled errors or ignore unhandled errors with setting the",P(ta,null,"test.dangerouslyIgnoreUnhandledErrors")," ","parameter to ",P(ta,null,"true"),"."),u.map((b,E)=>P(Ci,{key:E,"data-chromatic":"ignore"},Sa(b)))),P("div",{ref:f}),!s&&!l&&!a.some(b=>b.id!==De)&&P(E0,null))},"InteractionsPanel"));function Sa(e){return e.stack||`${e.name}: ${e.message}`}i(Sa,"printSerializedError");var Fr={start:!1,back:!1,goto:!1,next:!1,end:!1},Hg={done:"status-value:success",error:"status-value:error",active:"status-value:pending",waiting:"status-value:pending"},Pr=i(({log:e,calls:t,collapsed:r,setCollapsed:a})=>{let o=new Map,c=new Map;return e.map(({callId:l,ancestors:u,status:s})=>{let d=!1;return u.forEach(m=>{r.has(m)&&(d=!0),c.set(m,(c.get(m)||[]).concat(l))}),{...t.get(l),status:s,isHidden:d}}).map(l=>{let u=l.status==="error"&&l.ancestors&&o.get(l.ancestors.slice(-1)[0])?.status==="active"?"active":l.status;return o.set(l.id,{...l,status:u}),{...l,status:u,childCallIds:c.get(l.id),isCollapsed:r.has(l.id),toggleCollapsed:i(()=>a(s=>(s.has(l.id)?s.delete(l.id):s.add(l.id),new Set(s))),"toggleCollapsed")}})},"getInteractions"),ra=i((e,t)=>({id:De,method:"render",args:[],cursor:0,storyId:e,ancestors:[],path:[],interceptable:!0,retain:!1,exception:t}),"getInternalRenderCall"),jr=i(e=>({callId:De,status:e,ancestors:[]}),"getInternalRenderLogItem"),Vg=he(i(function({storyId:e}){let{statusValue:t,testRunId:r}=Ko(M=>{let x=M[e]?.[h0];return{statusValue:x?.value,testRunId:x?.data?.testRunId}}),[a,o]=St(sn,{controlStates:Fr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0}),[c,l]=R(void 0),[u,s]=R(new Set),[d,m]=R(!1),{controlStates:f=Fr,isErrored:p=!1,pausedAt:h=void 0,interactions:g=[],isPlaying:b=!1,caughtException:E=void 0,unhandledErrors:v=void 0}=a,A=Q([jr("active")]),S=Q(new Map([[De,ra(e)]])),O=i(({status:M,...x})=>S.current.set(x.id,x),"setCall"),D=Q();j(()=>{let M;return Qe.IntersectionObserver&&(M=new Qe.IntersectionObserver(([x])=>l(x.isIntersecting?void 0:x.target),{root:Qe.document.querySelector("#panel-tab-content")}),D.current&&M.observe(D.current)),()=>M?.disconnect()},[]);let k=Ar({[ft.CALL]:O,[ft.SYNC]:M=>{A.current=[jr("done"),...M.logItems],o(x=>{let C=Pr({log:A.current,calls:S.current,collapsed:u,setCollapsed:s}),w=C.filter(({id:B,method:T})=>B!==De&&T!=="step").length;return{...x,controlStates:M.controlStates,pausedAt:M.pausedAt,interactions:C,interactionsCount:w}})},[Qo]:M=>{if(M.newPhase==="preparing")A.current=[jr("active")],S.current.set(De,ra(e)),o({controlStates:Fr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0});else{let x=Pr({log:A.current,calls:S.current,collapsed:u,setCollapsed:s}),C=x.filter(({id:w,method:B})=>w!==De&&B!=="step").length;o(w=>({...w,interactions:x,interactionsCount:C,isPlaying:M.newPhase==="playing",pausedAt:void 0}))}},[el]:M=>{A.current=[jr("error")],S.current.set(De,ra(e,{...M,callId:De}));let x=Pr({log:A.current,calls:S.current,collapsed:u,setCollapsed:s});o(C=>({...C,isErrored:!0,hasException:!0,caughtException:void 0,controlStates:Fr,pausedAt:void 0,interactions:x,interactionsCount:0}))},[Zo]:M=>{o(x=>({...x,caughtException:M,hasException:!0}))},[tl]:M=>{o(x=>({...x,unhandledErrors:M,hasException:!0}))}},[u]);j(()=>{o(M=>{let x=Pr({log:A.current,calls:S.current,collapsed:u,setCollapsed:s}),C=x.filter(({id:w,method:B})=>w!==De&&B!=="step").length;return{...M,interactions:x,interactionsCount:C}})},[o,u]);let I=ce(()=>({start:i(()=>k(ft.START,{storyId:e}),"start"),back:i(()=>k(ft.BACK,{storyId:e}),"back"),goto:i(M=>k(ft.GOTO,{storyId:e,callId:M}),"goto"),next:i(()=>k(ft.NEXT,{storyId:e}),"next"),end:i(()=>k(ft.END,{storyId:e}),"end"),rerun:i(()=>{k(Xo,{storyId:e})},"rerun")}),[k,e]),F=et("fileName",""),[$]=F.toString().split("/").slice(-1),V=i(()=>c?.scrollIntoView({behavior:"smooth",block:"end"}),"scrollToTarget"),z=!!E||!!v||g.some(M=>M.status==="error"),W=ce(()=>!b&&(g.length>0||z)?z?"error":"done":b?"active":void 0,[b,g,z]);return j(()=>{if(W&&t&&t!=="status-value:pending"&&t!==Hg[W]){let M=setTimeout(()=>m(x=>(x||k(p0,{type:"test-discrepancy",payload:{browserStatus:W==="done"?"PASS":"FAIL",cliStatus:W==="done"?"FAIL":"PASS",storyId:e,testRunId:r}}),!0)),2e3);return()=>clearTimeout(M)}else m(!1)},[k,W,t,e,r]),n.createElement(Oe,{key:"component-tests"},n.createElement(Ug,{hasResultMismatch:d,browserTestStatus:W,calls:S.current,controls:I,controlStates:f,interactions:g,fileName:$,hasException:z,caughtException:E,unhandledErrors:v,isErrored:p,isPlaying:b,pausedAt:h,endRef:D,onScrollToEnd:c&&V}))},"PanelMemoized"));function Ec(){let e=ye().getSelectedPanel(),[t={}]=St(sn),{isErrored:r,hasException:a,interactionsCount:o}=t;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Interactions"),o&&!r&&!a?n.createElement(ct,{compact:!0,status:e===ac?"active":"neutral"},o):null,r||a?n.createElement(yc,{status:"error"}):null)}i(Ec,"PanelTitle");var m3=ee.register(sn,()=>{if(globalThis?.FEATURES?.interactions){let e=i(({state:t})=>({storyId:t.storyId}),"filter");ee.add(ac,{type:be.PANEL,title:i(()=>n.createElement(Ec,null),"title"),match:i(({viewMode:t})=>t==="story","match"),render:i(({active:t})=>n.createElement($t,{active:!!t},n.createElement(Wo,{filter:e},({storyId:r})=>n.createElement(Vg,{storyId:r}))),"render")})}}),wa="storybook/background",Gr="backgrounds",v3={UPDATE:`${wa}/update`},zg={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}},qg=he(i(function(){let e=et(Gr),[t,r,a]=Ve(),[o,c]=R(!1),{options:l=zg,disable:u=!0}=e||{};if(u)return null;let s=t[Gr]||{},d=s.value,m=s.grid||!1,f=l[d],p=!!a?.[Gr],h=Object.keys(l).length;return n.createElement(Gg,{length:h,backgroundMap:l,item:f,updateGlobals:r,backgroundName:d,setIsTooltipVisible:c,isLocked:p,isGridActive:m,isTooltipVisible:o})},"BackgroundSelector")),Gg=he(i(function(e){let{item:t,length:r,updateGlobals:a,setIsTooltipVisible:o,backgroundMap:c,backgroundName:l,isLocked:u,isGridActive:s,isTooltipVisible:d}=e,m=H(f=>{a({[Gr]:f})},[a]);return n.createElement(Oe,null,n.createElement(K,{key:"grid",active:s,disabled:u,title:"Apply a grid to the preview",onClick:()=>m({value:l,grid:!s})},n.createElement(Ao,null)),r>0?n.createElement(oe,{key:"background",placement:"top",closeOnOutsideClick:!0,tooltip:({onHide:f})=>n.createElement(zt,{links:[...t?[{id:"reset",title:"Reset background",icon:n.createElement(yr,null),onClick:i(()=>{m(void 0),f()},"onClick")}]:[],...Object.entries(c).map(([p,h])=>({id:p,title:h.name,icon:n.createElement(br,{color:h?.value||"grey"}),active:p===l,onClick:i(()=>{m({value:p,grid:s}),f()},"onClick")}))].flat()}),onVisibleChange:o},n.createElement(K,{disabled:u,key:"background",title:"Change the background of the preview",active:!!t||d},n.createElement(To,null))):null)},"PureTool")),x3=ee.register(wa,()=>{globalThis?.FEATURES?.backgrounds&&ee.add(wa,{title:"Backgrounds",type:be.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(qg,null),"render")})}),Bt="storybook/measure-addon",vc=`${Bt}/tool`,T3={RESULT:`${Bt}/result`,REQUEST:`${Bt}/request`,CLEAR:`${Bt}/clear`},Wg=i(()=>{let[e,t]=Ve(),{measureEnabled:r}=e||{},a=ye(),o=H(()=>t({measureEnabled:!r}),[t,r]);return j(()=>{a.setAddonShortcut(Bt,{label:"Toggle Measure [M]",defaultShortcut:["M"],actionName:"measure",showInMenu:!1,action:o})},[o,a]),n.createElement(K,{key:vc,active:r,title:"Enable measure",onClick:o},n.createElement(Ro,null))},"Tool"),I3=ee.register(Bt,()=>{globalThis?.FEATURES?.measure&&ee.add(vc,{type:be.TOOL,title:"Measure",match:i(({viewMode:e,tabId:t})=>e==="story"&&!t,"match"),render:i(()=>n.createElement(Wg,null),"render")})}),ka="storybook/outline",Si="outline",Kg=he(i(function(){let[e,t]=Ve(),r=ye(),a=[!0,"true"].includes(e[Si]),o=H(()=>t({[Si]:!a}),[a]);return j(()=>{r.setAddonShortcut(ka,{label:"Toggle Outline",defaultShortcut:["alt","O"],actionName:"outline",showInMenu:!1,action:o})},[o,r]),n.createElement(K,{key:"outline",active:a,title:"Apply outlines to the preview",onClick:o},n.createElement(Oo,null))},"OutlineSelector")),F3=ee.register(ka,()=>{globalThis?.FEATURES?.outline&&ee.add(ka,{title:"Outline",type:be.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(Kg,null),"render")})}),_t="storybook/viewport",Wr="viewport",V3=`${_t}/panel`,Yg=`${_t}/tool`,Jg={mobile1:{name:"Small mobile",styles:{height:"568px",width:"320px"},type:"mobile"},mobile2:{name:"Large mobile",styles:{height:"896px",width:"414px"},type:"mobile"},tablet:{name:"Tablet",styles:{height:"1112px",width:"834px"},type:"tablet"},desktop:{name:"Desktop",styles:{height:"1024px",width:"1280px"},type:"desktop"}},rr={name:"Reset viewport",styles:{height:"100%",width:"100%"},type:"desktop"},xc=i((e,t)=>e.indexOf(t),"getCurrentViewportIndex"),Xg=i((e,t)=>{let r=xc(e,t);return r===e.length-1?e[0]:e[r+1]},"getNextViewport"),Zg=i((e,t)=>{let r=xc(e,t);return r<1?e[e.length-1]:e[r-1]},"getPreviousViewport"),Qg=i(async(e,t,r,a)=>{await e.setAddonShortcut(_t,{label:"Previous viewport",defaultShortcut:["alt","shift","V"],actionName:"previous",action:i(()=>{r({viewport:Zg(a,t)})},"action")}),await e.setAddonShortcut(_t,{label:"Next viewport",defaultShortcut:["alt","V"],actionName:"next",action:i(()=>{r({viewport:Xg(a,t)})},"action")}),await e.setAddonShortcut(_t,{label:"Reset viewport",defaultShortcut:["alt","control","V"],actionName:"reset",action:i(()=>{r({viewport:{value:void 0,isRotated:!1}})},"action")})},"registerShortcuts"),eb=y.div({display:"inline-flex",alignItems:"center"}),wi=y.div(({theme:e})=>({display:"inline-block",textDecoration:"none",padding:10,fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,lineHeight:"1",height:40,border:"none",borderTop:"3px solid transparent",borderBottom:"3px solid transparent",background:"transparent"})),tb=y(K)(()=>({display:"inline-flex",alignItems:"center"})),rb=y.div(({theme:e})=>({fontSize:e.typography.size.s2-1,marginLeft:10})),nb={desktop:n.createElement(fo,null),mobile:n.createElement(ko,null),tablet:n.createElement(Po,null),other:n.createElement(Oe,null)},ab=i(({api:e})=>{let t=et(Wr),[r,a,o]=Ve(),[c,l]=R(!1),{options:u=Jg,disable:s}=t||{},d=r?.[Wr]||{},m=typeof d=="string"?d:d.value,f=typeof d=="string"?!1:d.isRotated,p=u[m]||rr,h=c||p!==rr,g=Wr in o,b=Object.keys(u).length;if(j(()=>{Qg(e,m,a,Object.keys(u))},[u,m,a,e]),p.styles===null||!u||b<1)return null;if(typeof p.styles=="function")return console.warn("Addon Viewport no longer supports dynamic styles using a function, use css calc() instead"),null;let E=f?p.styles.height:p.styles.width,v=f?p.styles.width:p.styles.height;return s?null:n.createElement(ob,{item:p,updateGlobals:a,viewportMap:u,viewportName:m,isRotated:f,setIsTooltipVisible:l,isLocked:g,isActive:h,width:E,height:v})},"ViewportTool"),ob=n.memo(i(function(e){let{item:t,viewportMap:r,viewportName:a,isRotated:o,updateGlobals:c,setIsTooltipVisible:l,isLocked:u,isActive:s,width:d,height:m}=e,f=H(p=>c({[Wr]:p}),[c]);return n.createElement(Oe,null,n.createElement(oe,{placement:"bottom",tooltip:({onHide:p})=>n.createElement(zt,{links:[...length>0&&t!==rr?[{id:"reset",title:"Reset viewport",icon:n.createElement(yr,null),onClick:i(()=>{f(void 0),p()},"onClick")}]:[],...Object.entries(r).map(([h,g])=>({id:h,title:g.name,icon:nb[g.type],active:h===a,onClick:i(()=>{f({value:h,isRotated:!1}),p()},"onClick")}))].flat()}),closeOnOutsideClick:!0,onVisibleChange:l},n.createElement(tb,{disabled:u,key:"viewport",title:"Change the size of the preview",active:s,onDoubleClick:()=>{f({value:void 0,isRotated:!1})}},n.createElement(Co,null),t!==rr?n.createElement(rb,null,t.name," ",o?"(L)":"(P)"):null)),n.createElement(Ho,{styles:{'iframe[data-is-storybook="true"]':{width:d,height:m}}}),t!==rr?n.createElement(eb,null,n.createElement(wi,{title:"Viewport width"},d.replace("px","")),u?"/":n.createElement(K,{key:"viewport-rotate",title:"Rotate viewport",onClick:()=>{f({value:a,isRotated:!o})}},n.createElement(jo,null)),n.createElement(wi,{title:"Viewport height"},m.replace("px",""))):null)},"PureTool")),K3=ee.register(_t,e=>{globalThis?.FEATURES?.viewport&&ee.add(Yg,{title:"viewport / media-queries",type:be.TOOL,match:i(({viewMode:t,tabId:r})=>t==="story"&&!r,"match"),render:i(()=>P(ab,{api:e}),"render")})}),lb="tag-filters",ib="static-filter",Y3=ee.register(lb,e=>{let t=Object.entries(Qe.TAGS_OPTIONS??{}).reduce((r,a)=>{let[o,c]=a;return c.excludeFromSidebar&&(r[o]=!0),r},{});e.experimental_setFilter(ib,r=>{let a=r.tags??[];return(a.includes("dev")||r.type==="docs")&&a.filter(o=>t[o]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
