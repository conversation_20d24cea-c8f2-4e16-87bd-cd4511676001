{"generatedAt": 1749493680413, "userSince": 1748897778474, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.0", "@storybook/addon-vitest": "9.0.4", "@vitest/browser": "3.2.0", "@vitest/coverage-v8": "3.2.0", "playwright": "1.52.0", "vitest": "3.2.0"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 2, "storybookVersion": "9.0.4", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.0.4"}, "eslint-plugin-storybook": {"version": "9.0.4"}, "storybook": {"version": "9.0.4"}}, "addons": {"@storybook/addon-onboarding": {"version": "9.0.4"}, "@chromatic-com/storybook": {"version": "4.0.0"}, "@storybook/addon-docs": {"version": "9.0.4"}, "@storybook/addon-a11y": {"version": "9.0.4"}, "@storybook/addon-vitest": {"version": "9.0.4"}}}