{"buildCommand": "npm run build-storybook", "outputDirectory": "storybook-static", "framework": null, "headers": [{"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/storybook/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "cleanUrls": true, "trailingSlash": false}