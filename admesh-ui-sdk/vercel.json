{"buildCommand": "npm run build:all", "outputDirectory": "build", "framework": null, "rewrites": [{"source": "/storybook/(.*)", "destination": "/storybook/$1"}, {"source": "/storybook", "destination": "/storybook/index.html"}, {"source": "/(.*)", "destination": "/$1"}], "headers": [{"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/storybook/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "cleanUrls": true, "trailingSlash": false}