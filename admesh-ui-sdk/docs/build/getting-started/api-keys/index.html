<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-getting-started/api-keys" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.8.1">
<title data-rh="true">Getting Your API Key | AdMesh SDK Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://admesh-ui-sdk.vercel.app/img/admesh-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://admesh-ui-sdk.vercel.app/img/admesh-social-card.jpg"><meta data-rh="true" property="og:url" content="https://admesh-ui-sdk.vercel.app/getting-started/api-keys"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Getting Your API Key | AdMesh SDK Documentation"><meta data-rh="true" name="description" content="To use AdMesh APIs and SDKs, you&#x27;ll need an API key. This guide walks you through the process of creating an account and obtaining your API key."><meta data-rh="true" property="og:description" content="To use AdMesh APIs and SDKs, you&#x27;ll need an API key. This guide walks you through the process of creating an account and obtaining your API key."><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://admesh-ui-sdk.vercel.app/getting-started/api-keys"><link data-rh="true" rel="alternate" href="https://admesh-ui-sdk.vercel.app/getting-started/api-keys" hreflang="en"><link data-rh="true" rel="alternate" href="https://admesh-ui-sdk.vercel.app/getting-started/api-keys" hreflang="x-default"><link data-rh="true" rel="preconnect" href="https://YOUR_APP_ID-dsn.algolia.net" crossorigin="anonymous"><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Getting Your API Key","item":"https://admesh-ui-sdk.vercel.app/getting-started/api-keys"}]}</script><link rel="search" type="application/opensearchdescription+xml" title="AdMesh SDK Documentation" href="/opensearch.xml"><link rel="stylesheet" href="/assets/css/styles.0178e490.css">
<script src="/assets/js/runtime~main.84b1124b.js" defer="defer"></script>
<script src="/assets/js/main.dd844d21.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;"><defs>
<symbol id="theme-svg-external-link" viewBox="0 0 24 24"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></symbol>
</defs></svg>
<script>!function(){var t="light";var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();document.documentElement.setAttribute("data-theme",e||t),document.documentElement.setAttribute("data-theme-choice",e||t)}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="theme-layout-navbar navbar navbar--fixed-top"><div class="navbar__inner"><div class="theme-layout-navbar-left navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.svg" alt="AdMesh Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/img/logo.svg" alt="AdMesh Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">AdMesh</b></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/">Documentation</a><a class="navbar__item navbar__link" target="_self" href="/storybook">Storybook</a></div><div class="theme-layout-navbar-right navbar__items navbar__items--right"><a href="https://useadmesh.com" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Dashboard<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Python SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">TypeScript SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><a href="https://github.com/GouniManikumar12/admesh-ui-sdk" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">UI SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="system mode" aria-label="Switch between dark and light mode (currently system mode)"><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP systemToggleIcon_QzmC"><path fill="currentColor" d="m12 21c4.971 0 9-4.029 9-9s-4.029-9-9-9-9 4.029-9 9 4.029 9 9 9zm4.95-13.95c1.313 1.313 2.05 3.093 2.05 4.95s-0.738 3.637-2.05 4.95c-1.313 1.313-3.093 2.05-4.95 2.05v-14c1.857 0 3.637 0.737 4.95 2.05z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search (Command+K)"><span class="DocSearch-Button-Container"><svg width="20" height="20" class="DocSearch-Search-Icon" viewBox="0 0 20 20" aria-hidden="true"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">Search</span></span><span class="DocSearch-Button-Keys"></span></button></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="theme-layout-main main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/getting-started/overview">Getting Started</a></div><ul class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/overview">Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/getting-started/api-keys">Getting Your API Key</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/quick-start">Quick Start Guide</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/ad-formats">AdMesh Ad Formats</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/admesh-vs-traditional">AdMesh vs Traditional Advertising</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/python-sdk/installation">Python SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/typescript-sdk/installation">TypeScript SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/ui-sdk/installation">UI SDK</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/ai-integration/overview">AI Agent Integration</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/api/authentication">API Reference</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/examples/ai-assistant">Examples</a></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">Getting Started</span></li><li class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link">Getting Your API Key</span></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Getting Your API Key</h1></header>
<p>To use AdMesh APIs and SDKs, you&#x27;ll need an API key. This guide walks you through the process of creating an account and obtaining your API key.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="creating-an-account">Creating an Account<a href="#creating-an-account" class="hash-link" aria-label="Direct link to Creating an Account" title="Direct link to Creating an Account">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-1-sign-up">Step 1: Sign Up<a href="#step-1-sign-up" class="hash-link" aria-label="Direct link to Step 1: Sign Up" title="Direct link to Step 1: Sign Up">​</a></h3>
<ol>
<li>Visit <a href="https://useadmesh.com/agent" target="_blank" rel="noopener noreferrer">useadmesh.com/agent</a></li>
<li>Click <strong>&quot;Sign Up&quot;</strong> to create a new account</li>
<li>Fill in your details:<!-- -->
<ul>
<li>Email address</li>
<li>Password</li>
<li>Company/Project name (optional)</li>
</ul>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-2-email-verification">Step 2: Email Verification<a href="#step-2-email-verification" class="hash-link" aria-label="Direct link to Step 2: Email Verification" title="Direct link to Step 2: Email Verification">​</a></h3>
<ol>
<li>Check your email for a verification message from AdMesh</li>
<li>Click the verification link to activate your account</li>
<li>You&#x27;ll be redirected to the AdMesh dashboard</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="obtaining-your-api-key">Obtaining Your API Key<a href="#obtaining-your-api-key" class="hash-link" aria-label="Direct link to Obtaining Your API Key" title="Direct link to Obtaining Your API Key">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-1-access-the-dashboard">Step 1: Access the Dashboard<a href="#step-1-access-the-dashboard" class="hash-link" aria-label="Direct link to Step 1: Access the Dashboard" title="Direct link to Step 1: Access the Dashboard">​</a></h3>
<ol>
<li>Log in to your AdMesh account at <a href="https://useadmesh.com/agent" target="_blank" rel="noopener noreferrer">useadmesh.com/agent</a></li>
<li>Navigate to the <strong>API Keys</strong> section in the dashboard</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-2-generate-api-key">Step 2: Generate API Key<a href="#step-2-generate-api-key" class="hash-link" aria-label="Direct link to Step 2: Generate API Key" title="Direct link to Step 2: Generate API Key">​</a></h3>
<ol>
<li>Click <strong>&quot;Generate New API Key&quot;</strong></li>
<li>Give your API key a descriptive name (e.g., &quot;Production App&quot;, &quot;Development&quot;)</li>
<li>Select the appropriate permissions:<!-- -->
<ul>
<li><strong>Read</strong> - Get recommendations</li>
<li><strong>Write</strong> - Submit tracking data</li>
<li><strong>Admin</strong> - Manage offers and settings</li>
</ul>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="step-3-copy-and-secure-your-key">Step 3: Copy and Secure Your Key<a href="#step-3-copy-and-secure-your-key" class="hash-link" aria-label="Direct link to Step 3: Copy and Secure Your Key" title="Direct link to Step 3: Copy and Secure Your Key">​</a></h3>
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>Important</div><div class="admonitionContent_BuS1"><p>Your API key will only be shown once. Make sure to copy and store it securely.</p></div></div>
<ol>
<li>Copy the generated API key</li>
<li>Store it in a secure location (password manager, environment variables)</li>
<li>Never commit API keys to version control</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="api-key-types">API Key Types<a href="#api-key-types" class="hash-link" aria-label="Direct link to API Key Types" title="Direct link to API Key Types">​</a></h2>
<p>AdMesh provides different types of API keys for different environments:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="production-keys">Production Keys<a href="#production-keys" class="hash-link" aria-label="Direct link to Production Keys" title="Direct link to Production Keys">​</a></h3>
<ul>
<li>Used for live applications</li>
<li>Full rate limits apply</li>
<li>Real analytics and tracking</li>
<li>Revenue attribution enabled</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="test-keys">Test Keys<a href="#test-keys" class="hash-link" aria-label="Direct link to Test Keys" title="Direct link to Test Keys">​</a></h3>
<ul>
<li>Used for development and testing</li>
<li>Reduced rate limits</li>
<li>Sandbox environment</li>
<li>No revenue attribution</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="development-keys">Development Keys<a href="#development-keys" class="hash-link" aria-label="Direct link to Development Keys" title="Direct link to Development Keys">​</a></h3>
<ul>
<li>Local development only</li>
<li>Unlimited requests for testing</li>
<li>Mock data responses available</li>
<li>No analytics tracking</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="using-your-api-key">Using Your API Key<a href="#using-your-api-key" class="hash-link" aria-label="Direct link to Using Your API Key" title="Direct link to Using Your API Key">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="environment-variables-recommended">Environment Variables (Recommended)<a href="#environment-variables-recommended" class="hash-link" aria-label="Direct link to Environment Variables (Recommended)" title="Direct link to Environment Variables (Recommended)">​</a></h3>
<p>Store your API key as an environment variable:</p>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># .env file</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token assign-left variable" style="color:#36acaa">ADMESH_API_KEY</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">your_api_key_here</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="python-sdk">Python SDK<a href="#python-sdk" class="hash-link" aria-label="Direct link to Python SDK" title="Direct link to Python SDK">​</a></h3>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> os</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> admesh </span><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> Admesh</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Using environment variable (recommended)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">api_key</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">os</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">environ</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">get</span><span class="token punctuation" style="color:#393A34">(</span><span class="token string" style="color:#e3116c">&quot;ADMESH_API_KEY&quot;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Or pass directly (not recommended for production)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">api_key</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;your_api_key_here&quot;</span><span class="token punctuation" style="color:#393A34">)</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="typescript-sdk">TypeScript SDK<a href="#typescript-sdk" class="hash-link" aria-label="Direct link to TypeScript SDK" title="Direct link to TypeScript SDK">​</a></h3>
<div class="language-typescript codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-typescript codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword module" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports maybe-class-name">Admesh</span><span class="token plain"> </span><span class="token keyword module" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">// Using environment variable (recommended)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">new</span><span class="token plain"> </span><span class="token class-name maybe-class-name">Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  apiKey</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> process</span><span class="token punctuation" style="color:#393A34">.</span><span class="token property-access">env</span><span class="token punctuation" style="color:#393A34">.</span><span class="token constant" style="color:#36acaa">ADMESH_API_KEY</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">// Or pass directly (not recommended for production)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">new</span><span class="token plain"> </span><span class="token class-name maybe-class-name">Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  apiKey</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;your_api_key_here&#x27;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><br></span></code></pre></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="using-dotenv">Using dotenv<a href="#using-dotenv" class="hash-link" aria-label="Direct link to Using dotenv" title="Direct link to Using dotenv">​</a></h3>
<p>For local development, use dotenv to load environment variables:</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="python">Python<a href="#python" class="hash-link" aria-label="Direct link to Python" title="Direct link to Python">​</a></h4>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Install: pip install python-dotenv</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> dotenv </span><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> load_dotenv</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">load_dotenv</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">from</span><span class="token plain"> admesh </span><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> Admesh</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain">  </span><span class="token comment" style="color:#999988;font-style:italic"># Automatically uses ADMESH_API_KEY from .env</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="nodejs">Node.js<a href="#nodejs" class="hash-link" aria-label="Direct link to Node.js" title="Direct link to Node.js">​</a></h4>
<div class="language-typescript codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-typescript codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">// Install: npm install dotenv</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">require</span><span class="token punctuation" style="color:#393A34">(</span><span class="token string" style="color:#e3116c">&#x27;dotenv&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">.</span><span class="token method function property-access" style="color:#d73a49">config</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword module" style="color:#00009f">import</span><span class="token plain"> </span><span class="token imports maybe-class-name">Admesh</span><span class="token plain"> </span><span class="token keyword module" style="color:#00009f">from</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;admesh&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">const</span><span class="token plain"> client </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">new</span><span class="token plain"> </span><span class="token class-name maybe-class-name">Admesh</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">// Automatically uses ADMESH_API_KEY from .env</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="security-best-practices">Security Best Practices<a href="#security-best-practices" class="hash-link" aria-label="Direct link to Security Best Practices" title="Direct link to Security Best Practices">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-dos">✅ Do&#x27;s<a href="#-dos" class="hash-link" aria-label="Direct link to ✅ Do&#x27;s" title="Direct link to ✅ Do&#x27;s">​</a></h3>
<ul>
<li>Store API keys in environment variables</li>
<li>Use different keys for different environments</li>
<li>Rotate keys regularly</li>
<li>Monitor API key usage in the dashboard</li>
<li>Use the principle of least privilege (minimal required permissions)</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="-donts">❌ Don&#x27;ts<a href="#-donts" class="hash-link" aria-label="Direct link to ❌ Don&#x27;ts" title="Direct link to ❌ Don&#x27;ts">​</a></h3>
<ul>
<li>Never commit API keys to version control</li>
<li>Don&#x27;t share API keys in chat or email</li>
<li>Don&#x27;t use production keys in development</li>
<li>Don&#x27;t hardcode keys in your application code</li>
<li>Don&#x27;t expose keys in client-side code</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="rate-limits">Rate Limits<a href="#rate-limits" class="hash-link" aria-label="Direct link to Rate Limits" title="Direct link to Rate Limits">​</a></h2>
<p>Different subscription tiers have different rate limits:</p>
<table><thead><tr><th>Plan</th><th>Daily Requests</th><th>Monthly Requests</th><th>Rate Limit</th></tr></thead><tbody><tr><td><strong>Free</strong></td><td>10,000</td><td>100,000</td><td>100/minute</td></tr><tr><td><strong>Pro</strong></td><td>50,000</td><td>1,000,000</td><td>500/minute</td></tr><tr><td><strong>Enterprise</strong></td><td>Unlimited</td><td>Unlimited</td><td>1000/minute</td></tr></tbody></table>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="managing-api-keys">Managing API Keys<a href="#managing-api-keys" class="hash-link" aria-label="Direct link to Managing API Keys" title="Direct link to Managing API Keys">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="viewing-usage">Viewing Usage<a href="#viewing-usage" class="hash-link" aria-label="Direct link to Viewing Usage" title="Direct link to Viewing Usage">​</a></h3>
<p>Monitor your API key usage in the dashboard:</p>
<ul>
<li>Request count and rate limit status</li>
<li>Error rates and response times</li>
<li>Geographic distribution of requests</li>
<li>Most popular endpoints</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="rotating-keys">Rotating Keys<a href="#rotating-keys" class="hash-link" aria-label="Direct link to Rotating Keys" title="Direct link to Rotating Keys">​</a></h3>
<p>To rotate an API key:</p>
<ol>
<li>Generate a new API key in the dashboard</li>
<li>Update your application with the new key</li>
<li>Test that everything works correctly</li>
<li>Delete the old API key</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="revoking-keys">Revoking Keys<a href="#revoking-keys" class="hash-link" aria-label="Direct link to Revoking Keys" title="Direct link to Revoking Keys">​</a></h3>
<p>If an API key is compromised:</p>
<ol>
<li>Immediately revoke the key in the dashboard</li>
<li>Generate a new API key</li>
<li>Update all applications using the old key</li>
<li>Monitor for any unauthorized usage</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="troubleshooting">Troubleshooting<a href="#troubleshooting" class="hash-link" aria-label="Direct link to Troubleshooting" title="Direct link to Troubleshooting">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="common-issues">Common Issues<a href="#common-issues" class="hash-link" aria-label="Direct link to Common Issues" title="Direct link to Common Issues">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="invalid-api-key">Invalid API Key<a href="#invalid-api-key" class="hash-link" aria-label="Direct link to Invalid API Key" title="Direct link to Invalid API Key">​</a></h4>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;error&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;Invalid API key&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;code&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;INVALID_API_KEY&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;status&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token number" style="color:#36acaa">401</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><br></span></code></pre></div></div>
<p><strong>Solution</strong>: Verify your API key is correct and hasn&#x27;t been revoked.</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="rate-limit-exceeded">Rate Limit Exceeded<a href="#rate-limit-exceeded" class="hash-link" aria-label="Direct link to Rate Limit Exceeded" title="Direct link to Rate Limit Exceeded">​</a></h4>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;error&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;Rate limit exceeded&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;code&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;RATE_LIMIT_EXCEEDED&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;status&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token number" style="color:#36acaa">429</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><br></span></code></pre></div></div>
<p><strong>Solution</strong>: Implement exponential backoff or upgrade your plan.</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="insufficient-permissions">Insufficient Permissions<a href="#insufficient-permissions" class="hash-link" aria-label="Direct link to Insufficient Permissions" title="Direct link to Insufficient Permissions">​</a></h4>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token punctuation" style="color:#393A34">{</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;error&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;Insufficient permissions&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;code&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&quot;INSUFFICIENT_PERMISSIONS&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token property" style="color:#36acaa">&quot;status&quot;</span><span class="token operator" style="color:#393A34">:</span><span class="token plain"> </span><span class="token number" style="color:#36acaa">403</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">}</span><br></span></code></pre></div></div>
<p><strong>Solution</strong>: Check that your API key has the required permissions.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="getting-help">Getting Help<a href="#getting-help" class="hash-link" aria-label="Direct link to Getting Help" title="Direct link to Getting Help">​</a></h3>
<p>If you&#x27;re having trouble with API keys:</p>
<ul>
<li>Contact support at <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer"><EMAIL></a></li>
<li>Check our <a href="https://github.com/GouniManikumar12/admesh-python/issues" target="_blank" rel="noopener noreferrer">GitHub Issues</a></li>
<li>Join our community discussions</li>
</ul>
<hr>
<p>Now that you have your API key, let&#x27;s make your first API call in the <a href="/getting-started/quick-start">Quick Start Guide</a>!</p></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/getting-started/api-keys.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="docusaurus-mt-lg pagination-nav" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/getting-started/overview"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Overview</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/getting-started/quick-start"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Quick Start Guide</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#creating-an-account" class="table-of-contents__link toc-highlight">Creating an Account</a><ul><li><a href="#step-1-sign-up" class="table-of-contents__link toc-highlight">Step 1: Sign Up</a></li><li><a href="#step-2-email-verification" class="table-of-contents__link toc-highlight">Step 2: Email Verification</a></li></ul></li><li><a href="#obtaining-your-api-key" class="table-of-contents__link toc-highlight">Obtaining Your API Key</a><ul><li><a href="#step-1-access-the-dashboard" class="table-of-contents__link toc-highlight">Step 1: Access the Dashboard</a></li><li><a href="#step-2-generate-api-key" class="table-of-contents__link toc-highlight">Step 2: Generate API Key</a></li><li><a href="#step-3-copy-and-secure-your-key" class="table-of-contents__link toc-highlight">Step 3: Copy and Secure Your Key</a></li></ul></li><li><a href="#api-key-types" class="table-of-contents__link toc-highlight">API Key Types</a><ul><li><a href="#production-keys" class="table-of-contents__link toc-highlight">Production Keys</a></li><li><a href="#test-keys" class="table-of-contents__link toc-highlight">Test Keys</a></li><li><a href="#development-keys" class="table-of-contents__link toc-highlight">Development Keys</a></li></ul></li><li><a href="#using-your-api-key" class="table-of-contents__link toc-highlight">Using Your API Key</a><ul><li><a href="#environment-variables-recommended" class="table-of-contents__link toc-highlight">Environment Variables (Recommended)</a></li><li><a href="#python-sdk" class="table-of-contents__link toc-highlight">Python SDK</a></li><li><a href="#typescript-sdk" class="table-of-contents__link toc-highlight">TypeScript SDK</a></li><li><a href="#using-dotenv" class="table-of-contents__link toc-highlight">Using dotenv</a></li></ul></li><li><a href="#security-best-practices" class="table-of-contents__link toc-highlight">Security Best Practices</a><ul><li><a href="#-dos" class="table-of-contents__link toc-highlight">✅ Do&#39;s</a></li><li><a href="#-donts" class="table-of-contents__link toc-highlight">❌ Don&#39;ts</a></li></ul></li><li><a href="#rate-limits" class="table-of-contents__link toc-highlight">Rate Limits</a></li><li><a href="#managing-api-keys" class="table-of-contents__link toc-highlight">Managing API Keys</a><ul><li><a href="#viewing-usage" class="table-of-contents__link toc-highlight">Viewing Usage</a></li><li><a href="#rotating-keys" class="table-of-contents__link toc-highlight">Rotating Keys</a></li><li><a href="#revoking-keys" class="table-of-contents__link toc-highlight">Revoking Keys</a></li></ul></li><li><a href="#troubleshooting" class="table-of-contents__link toc-highlight">Troubleshooting</a><ul><li><a href="#common-issues" class="table-of-contents__link toc-highlight">Common Issues</a></li><li><a href="#getting-help" class="table-of-contents__link toc-highlight">Getting Help</a></li></ul></li></ul></div></div></div></div></main></div></div></div><footer class="theme-layout-footer footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Documentation</div><ul class="footer__items clean-list"><li class="footer__item"><a class="footer__link-item" href="/getting-started/overview">Getting Started</a></li><li class="footer__item"><a class="footer__link-item" href="/python-sdk/installation">Python SDK</a></li><li class="footer__item"><a class="footer__link-item" href="/typescript-sdk/installation">TypeScript SDK</a></li><li class="footer__item"><a class="footer__link-item" href="/ui-sdk/installation">UI SDK</a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">SDKs</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" rel="noopener noreferrer" class="footer__link-item">Python SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" rel="noopener noreferrer" class="footer__link-item">TypeScript SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12/admesh-ui-sdk" target="_blank" rel="noopener noreferrer" class="footer__link-item">UI SDK<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://useadmesh.com" target="_blank" rel="noopener noreferrer" class="footer__link-item">AdMesh Dashboard<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="https://github.com/GouniManikumar12" target="_blank" rel="noopener noreferrer" class="footer__link-item">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li><li class="footer__item"><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" class="footer__link-item">Support<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 AdMesh. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>