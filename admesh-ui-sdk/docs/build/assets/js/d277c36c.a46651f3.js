"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[6968],{5893:(n,e,t)=>{t.r(e),t.d(e,{assets:()=>d,contentTitle:()=>r,default:()=>l,frontMatter:()=>o,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"examples/ai-assistant","title":"AI Assistant Integration","description":"Technical implementation guide for building AI assistants with intelligent product recommendation capabilities using AdMesh. This example demonstrates production-ready conversational AI with contextual product suggestions.","source":"@site/docs/examples/ai-assistant.md","sourceDirName":"examples","slug":"/examples/ai-assistant","permalink":"/examples/ai-assistant","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/examples/ai-assistant.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{"sidebar_position":4},"sidebar":"tutorialSidebar","previous":{"title":"Authentication","permalink":"/api/authentication"}}');var a=t(4848),i=t(8453);const o={sidebar_position:4},r="AI Assistant Integration",d={},c=[{value:"Implementation Overview",id:"implementation-overview",level:2},{value:"Complete AI Assistant Example",id:"complete-ai-assistant-example",level:2},{value:"Backend (Python + FastAPI)",id:"backend-python--fastapi",level:3},{value:"Frontend (React + TypeScript)",id:"frontend-react--typescript",level:3},{value:"Key Features Demonstrated",id:"key-features-demonstrated",level:2},{value:"1. Intent Detection",id:"1-intent-detection",level:3},{value:"2. Contextual Recommendations",id:"2-contextual-recommendations",level:3},{value:"3. Citation-Based Display",id:"3-citation-based-display",level:3},{value:"4. Conversational Integration",id:"4-conversational-integration",level:3},{value:"5. Tracking and Analytics",id:"5-tracking-and-analytics",level:3},{value:"Implementation Examples",id:"implementation-examples",level:2},{value:"Enterprise Consultation",id:"enterprise-consultation",level:3},{value:"Technical Implementation",id:"technical-implementation",level:3},{value:"Deployment",id:"deployment",level:2},{value:"Docker Deployment",id:"docker-deployment",level:3},{value:"Environment Variables",id:"environment-variables",level:3},{value:"Next Steps",id:"next-steps",level:2}];function m(n){const e={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,i.R)(),...n.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(e.header,{children:(0,a.jsx)(e.h1,{id:"ai-assistant-integration",children:"AI Assistant Integration"})}),"\n",(0,a.jsx)(e.p,{children:"Technical implementation guide for building AI assistants with intelligent product recommendation capabilities using AdMesh. This example demonstrates production-ready conversational AI with contextual product suggestions."}),"\n",(0,a.jsx)(e.h2,{id:"implementation-overview",children:"Implementation Overview"}),"\n",(0,a.jsx)(e.p,{children:"This implementation includes:"}),"\n",(0,a.jsxs)(e.ul,{children:["\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:"Intent detection engine"})," for user query analysis"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:"Contextual recommendation system"})," based on conversation history"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:"Citation-based display system"})," for recommendations"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:"Automated suggestion triggers"})," during conversations"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:"Analytics and tracking"})," for recommendation performance"]}),"\n"]}),"\n",(0,a.jsx)(e.h2,{id:"complete-ai-assistant-example",children:"Complete AI Assistant Example"}),"\n",(0,a.jsx)(e.h3,{id:"backend-python--fastapi",children:"Backend (Python + FastAPI)"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{className:"language-python",children:'# ai_assistant.py\nfrom fastapi import FastAPI, HTTPException\nfrom pydantic import BaseModel\nfrom typing import List, Optional\nimport openai\nfrom admesh import AsyncAdmesh\nimport re\n\napp = FastAPI()\n\n# Initialize clients\nopenai.api_key = "your-openai-api-key"\nadmesh_client = AsyncAdmesh()\n\nclass ChatMessage(BaseModel):\n    role: str\n    content: str\n\nclass ChatRequest(BaseModel):\n    messages: List[ChatMessage]\n    include_recommendations: bool = True\n\nclass RecommendationResponse(BaseModel):\n    title: str\n    reason: str\n    intent_match_score: float\n    admesh_link: str\n    ad_id: str\n\nclass ChatResponse(BaseModel):\n    message: str\n    recommendations: List[RecommendationResponse] = []\n    recommendation_context: Optional[str] = None\n\nclass AIAssistant:\n    def __init__(self):\n        self.product_keywords = [\n            \'software\', \'tool\', \'platform\', \'service\', \'solution\',\n            \'app\', \'system\', \'product\', \'crm\', \'marketing\', \'sales\'\n        ]\n    \n    def should_recommend_products(self, query: str, ai_response: str) -> bool:\n        """Determine if we should show product recommendations"""\n        combined_text = f"{query} {ai_response}".lower()\n        keyword_count = sum(1 for keyword in self.product_keywords if keyword in combined_text)\n        \n        # Recommend if multiple product keywords or explicit request\n        return keyword_count >= 2 or any(phrase in combined_text for phrase in [\n            \'recommend\', \'suggest\', \'what should i use\', \'best tool\', \'help me find\'\n        ])\n    \n    def extract_recommendation_query(self, query: str, ai_response: str) -> str:\n        """Extract the best query for recommendations"""\n        # Look for specific product mentions or use cases\n        combined_text = f"{query} {ai_response}"\n        \n        # Extract key phrases for better recommendations\n        patterns = [\n            r\'need (?:a |an )?([^.!?]+)\',\n            r\'looking for (?:a |an )?([^.!?]+)\',\n            r\'recommend (?:a |an )?([^.!?]+)\',\n            r\'best ([^.!?]+) for\',\n        ]\n        \n        for pattern in patterns:\n            match = re.search(pattern, combined_text, re.IGNORECASE)\n            if match:\n                return match.group(1).strip()\n        \n        # Fallback to original query\n        return query\n    \n    async def get_ai_response(self, messages: List[ChatMessage]) -> str:\n        """Get response from OpenAI"""\n        try:\n            response = await openai.ChatCompletion.acreate(\n                model="gpt-4",\n                messages=[{"role": msg.role, "content": msg.content} for msg in messages],\n                max_tokens=500,\n                temperature=0.7\n            )\n            return response.choices[0].message.content\n        except Exception as e:\n            raise HTTPException(status_code=500, f"AI service error: {str(e)}")\n    \n    async def get_recommendations(self, query: str) -> List[RecommendationResponse]:\n        """Get product recommendations from AdMesh"""\n        try:\n            response = await admesh_client.recommend.get_recommendations(\n                query=query,\n                format="auto",\n                max_recommendations=3\n            )\n            \n            return [\n                RecommendationResponse(\n                    title=rec.title,\n                    reason=rec.reason,\n                    intent_match_score=rec.intent_match_score,\n                    admesh_link=rec.admesh_link,\n                    ad_id=rec.ad_id\n                )\n                for rec in response.response.recommendations\n            ]\n        except Exception as e:\n            print(f"Recommendation error: {e}")\n            return []\n\nassistant = AIAssistant()\n\<EMAIL>("/chat", response_model=ChatResponse)\nasync def chat_endpoint(request: ChatRequest):\n    """Main chat endpoint with AI and recommendations"""\n    \n    # Get AI response\n    user_query = request.messages[-1].content\n    ai_response = await assistant.get_ai_response(request.messages)\n    \n    # Check if we should include recommendations\n    recommendations = []\n    recommendation_context = None\n    \n    if request.include_recommendations and assistant.should_recommend_products(user_query, ai_response):\n        # Extract query for recommendations\n        rec_query = assistant.extract_recommendation_query(user_query, ai_response)\n        recommendations = await assistant.get_recommendations(rec_query)\n        \n        if recommendations:\n            recommendation_context = f"Based on your interest in {rec_query}"\n    \n    return ChatResponse(\n        message=ai_response,\n        recommendations=recommendations,\n        recommendation_context=recommendation_context\n    )\n\<EMAIL>("/health")\nasync def health_check():\n    return {"status": "healthy"}\n\nif __name__ == "__main__":\n    import uvicorn\n    uvicorn.run(app, host="0.0.0.0", port=8000)\n'})}),"\n",(0,a.jsx)(e.h3,{id:"frontend-react--typescript",children:"Frontend (React + TypeScript)"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{className:"language-tsx",children:"// AIAssistant.tsx\nimport React, { useState, useRef, useEffect } from 'react';\nimport { \n  AdMeshConversationalUnit, \n  AdMeshCitationUnit,\n  AdMeshRecommendation \n} from 'admesh-ui-sdk';\n\ninterface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  recommendations?: AdMeshRecommendation[];\n  recommendationContext?: string;\n}\n\ninterface ChatResponse {\n  message: string;\n  recommendations: AdMeshRecommendation[];\n  recommendation_context?: string;\n}\n\nexport default function AIAssistant() {\n  const [messages, setMessages] = useState<ChatMessage[]>([\n    {\n      id: '1',\n      role: 'assistant',\n      content: 'Hello! I\\'m your AI assistant. I can help you find the perfect tools and software for your needs. What can I help you with today?',\n      timestamp: new Date()\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const sendMessage = async () => {\n    if (!inputValue.trim() || isLoading) return;\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: inputValue,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          messages: [...messages, userMessage].map(msg => ({\n            role: msg.role,\n            content: msg.content\n          })),\n          include_recommendations: true\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get response');\n      }\n\n      const data: ChatResponse = await response.json();\n\n      const assistantMessage: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'assistant',\n        content: data.message,\n        timestamp: new Date(),\n        recommendations: data.recommendations,\n        recommendationContext: data.recommendation_context\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n      \n      const errorMessage: ChatMessage = {\n        id: (Date.now() + 1).toString(),\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    // Track the click\n    console.log('Recommendation clicked:', { adId, admeshLink });\n    \n    // Open in new tab\n    window.open(admeshLink, '_blank');\n  };\n\n  return (\n    <div className=\"ai-assistant\">\n      <div className=\"chat-header\">\n        <h1>\ud83e\udd16 AI Assistant with Smart Recommendations</h1>\n        <p>Ask me about tools, software, or business solutions!</p>\n      </div>\n\n      <div className=\"chat-messages\">\n        {messages.map((message) => (\n          <div key={message.id} className={`message ${message.role}`}>\n            <div className=\"message-content\">\n              <div className=\"message-text\">\n                {message.role === 'assistant' && message.recommendations?.length ? (\n                  // Use citation-based display for AI responses with recommendations\n                  <AdMeshCitationUnit\n                    recommendations={message.recommendations}\n                    conversationText={message.content}\n                    citationStyle=\"numbered\"\n                    showCitationList={true}\n                    onRecommendationClick={handleRecommendationClick}\n                  />\n                ) : (\n                  message.content\n                )}\n              </div>\n\n              {/* Show conversational recommendations if available */}\n              {message.recommendations?.length > 0 && (\n                <div className=\"recommendations-section\">\n                  {message.recommendationContext && (\n                    <p className=\"recommendation-context\">\n                      \ud83d\udca1 {message.recommendationContext}\n                    </p>\n                  )}\n                  \n                  <AdMeshConversationalUnit\n                    recommendations={message.recommendations}\n                    config={{\n                      displayMode: 'inline',\n                      context: 'assistant',\n                      maxRecommendations: 3,\n                      showPoweredBy: true\n                    }}\n                    onRecommendationClick={handleRecommendationClick}\n                  />\n                </div>\n              )}\n            </div>\n\n            <div className=\"message-timestamp\">\n              {message.timestamp.toLocaleTimeString()}\n            </div>\n          </div>\n        ))}\n\n        {isLoading && (\n          <div className=\"message assistant\">\n            <div className=\"message-content\">\n              <div className=\"typing-indicator\">\n                <span></span>\n                <span></span>\n                <span></span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      <div className=\"chat-input\">\n        <div className=\"input-container\">\n          <textarea\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me about tools, software, or business solutions...\"\n            disabled={isLoading}\n            rows={1}\n          />\n          <button \n            onClick={sendMessage} \n            disabled={!inputValue.trim() || isLoading}\n          >\n            Send\n          </button>\n        </div>\n      </div>\n\n      <style jsx>{`\n        .ai-assistant {\n          max-width: 800px;\n          margin: 0 auto;\n          height: 100vh;\n          display: flex;\n          flex-direction: column;\n          background: #f9fafb;\n        }\n\n        .chat-header {\n          padding: 1rem;\n          background: white;\n          border-bottom: 1px solid #e5e7eb;\n          text-align: center;\n        }\n\n        .chat-header h1 {\n          margin: 0 0 0.5rem 0;\n          color: #1f2937;\n        }\n\n        .chat-header p {\n          margin: 0;\n          color: #6b7280;\n        }\n\n        .chat-messages {\n          flex: 1;\n          overflow-y: auto;\n          padding: 1rem;\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .message {\n          display: flex;\n          flex-direction: column;\n        }\n\n        .message.user {\n          align-items: flex-end;\n        }\n\n        .message.assistant {\n          align-items: flex-start;\n        }\n\n        .message-content {\n          max-width: 70%;\n          padding: 1rem;\n          border-radius: 1rem;\n          background: white;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n        }\n\n        .message.user .message-content {\n          background: #8b5cf6;\n          color: white;\n        }\n\n        .message-text {\n          margin-bottom: 0.5rem;\n        }\n\n        .recommendations-section {\n          margin-top: 1rem;\n          padding-top: 1rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .recommendation-context {\n          font-size: 0.875rem;\n          color: #6b7280;\n          margin-bottom: 0.5rem;\n          font-style: italic;\n        }\n\n        .message-timestamp {\n          font-size: 0.75rem;\n          color: #9ca3af;\n          margin-top: 0.25rem;\n        }\n\n        .typing-indicator {\n          display: flex;\n          gap: 0.25rem;\n        }\n\n        .typing-indicator span {\n          width: 8px;\n          height: 8px;\n          border-radius: 50%;\n          background: #9ca3af;\n          animation: typing 1.4s infinite ease-in-out;\n        }\n\n        .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }\n        .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }\n\n        @keyframes typing {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .chat-input {\n          padding: 1rem;\n          background: white;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .input-container {\n          display: flex;\n          gap: 0.5rem;\n          align-items: flex-end;\n        }\n\n        .input-container textarea {\n          flex: 1;\n          padding: 0.75rem;\n          border: 1px solid #d1d5db;\n          border-radius: 0.5rem;\n          resize: none;\n          font-family: inherit;\n          font-size: 1rem;\n        }\n\n        .input-container button {\n          padding: 0.75rem 1.5rem;\n          background: #8b5cf6;\n          color: white;\n          border: none;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          font-weight: 500;\n        }\n\n        .input-container button:disabled {\n          background: #9ca3af;\n          cursor: not-allowed;\n        }\n\n        .input-container button:hover:not(:disabled) {\n          background: #7c3aed;\n        }\n      `}</style>\n    </div>\n  );\n}\n"})}),"\n",(0,a.jsx)(e.h2,{id:"key-features-demonstrated",children:"Key Features Demonstrated"}),"\n",(0,a.jsx)(e.h3,{id:"1-intent-detection",children:"1. Intent Detection"}),"\n",(0,a.jsx)(e.p,{children:"The AI assistant automatically detects when users are asking about products or tools and triggers recommendations."}),"\n",(0,a.jsx)(e.h3,{id:"2-contextual-recommendations",children:"2. Contextual Recommendations"}),"\n",(0,a.jsx)(e.p,{children:"Recommendations are based on both the user's query and the AI's response, providing more relevant suggestions."}),"\n",(0,a.jsx)(e.h3,{id:"3-citation-based-display",children:"3. Citation-Based Display"}),"\n",(0,a.jsx)(e.p,{children:"Recommendations are displayed as numbered citations within the AI's response, similar to academic papers."}),"\n",(0,a.jsx)(e.h3,{id:"4-conversational-integration",children:"4. Conversational Integration"}),"\n",(0,a.jsx)(e.p,{children:"Recommendations appear naturally within the conversation flow without interrupting the user experience."}),"\n",(0,a.jsx)(e.h3,{id:"5-tracking-and-analytics",children:"5. Tracking and Analytics"}),"\n",(0,a.jsx)(e.p,{children:"All recommendation clicks are tracked for performance analysis and revenue attribution."}),"\n",(0,a.jsx)(e.h2,{id:"implementation-examples",children:"Implementation Examples"}),"\n",(0,a.jsx)(e.h3,{id:"enterprise-consultation",children:"Enterprise Consultation"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{children:'User: "Enterprise e-commerce platform requirements for customer management"\n\nAI: "For enterprise e-commerce customer management, implement a CRM system such as HubSpot\xb9 for comprehensive free tier capabilities and e-commerce integrations. Consider Shopify Plus\xb2 for enterprise-level e-commerce functionality..."\n\n[1] HubSpot CRM - Enterprise-grade CRM with e-commerce integration capabilities\n[2] Shopify Plus - Enterprise e-commerce platform with advanced CRM features\n'})}),"\n",(0,a.jsx)(e.h3,{id:"technical-implementation",children:"Technical Implementation"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{children:'User: "SaaS application payment processing requirements"\n\nAI: "For SaaS payment processing implementation, Stripe\xb9 provides industry-standard developer tools and subscription management capabilities. For advanced billing requirements, Chargebee\xb2 offers specialized subscription billing functionality..."\n\n[1] Stripe - Developer-focused payment processing with SaaS capabilities\n[2] Chargebee - Advanced subscription billing and revenue management platform\n'})}),"\n",(0,a.jsx)(e.h2,{id:"deployment",children:"Deployment"}),"\n",(0,a.jsx)(e.h3,{id:"docker-deployment",children:"Docker Deployment"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{className:"language-dockerfile",children:'# Dockerfile\nFROM python:3.9-slim\n\nWORKDIR /app\n\nCOPY requirements.txt .\nRUN pip install -r requirements.txt\n\nCOPY . .\n\nEXPOSE 8000\n\nCMD ["uvicorn", "ai_assistant:app", "--host", "0.0.0.0", "--port", "8000"]\n'})}),"\n",(0,a.jsx)(e.h3,{id:"environment-variables",children:"Environment Variables"}),"\n",(0,a.jsx)(e.pre,{children:(0,a.jsx)(e.code,{className:"language-bash",children:"# .env\nOPENAI_API_KEY=your_openai_api_key\nADMESH_API_KEY=your_admesh_api_key\nADMESH_BASE_URL=https://api.useadmesh.com\n"})}),"\n",(0,a.jsx)(e.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,a.jsxs)(e.ul,{children:["\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:(0,a.jsx)(e.a,{href:"/ai-integration/overview",children:"AI Integration Overview"})})," - Complete AI integration guide"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:(0,a.jsx)(e.a,{href:"/python-sdk/installation",children:"Python SDK"})})," - Backend SDK implementation"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:(0,a.jsx)(e.a,{href:"/ui-sdk/installation",children:"UI SDK"})})," - Frontend component integration"]}),"\n",(0,a.jsxs)(e.li,{children:[(0,a.jsx)(e.strong,{children:(0,a.jsx)(e.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n"]}),"\n",(0,a.jsx)(e.p,{children:"This implementation provides a production-ready foundation for AI assistants with intelligent product recommendations. Customize based on specific enterprise requirements and use cases."})]})}function l(n={}){const{wrapper:e}={...(0,i.R)(),...n.components};return e?(0,a.jsx)(e,{...n,children:(0,a.jsx)(m,{...n})}):m(n)}}}]);