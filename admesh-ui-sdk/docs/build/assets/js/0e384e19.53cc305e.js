"use strict";(self.webpackChunkadmesh_docs=self.webpackChunkadmesh_docs||[]).push([[3976],{2053:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>d,contentTitle:()=>o,default:()=>h,frontMatter:()=>a,metadata:()=>s,toc:()=>l});const s=JSON.parse('{"id":"intro","title":"AdMesh SDK Documentation","description":"AdMesh SDK Documentation provides comprehensive technical guidance for integrating AI-powered product recommendation capabilities into enterprise applications and AI systems.","source":"@site/docs/intro.md","sourceDirName":".","slug":"/","permalink":"/","draft":false,"unlisted":false,"editUrl":"https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/intro.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1,"slug":"/"},"sidebar":"tutorialSidebar","next":{"title":"Overview","permalink":"/getting-started/overview"}}');var t=i(4848),r=i(8453);const a={sidebar_position:1,slug:"/"},o="AdMesh SDK Documentation",d={},l=[{value:"What is AdMesh?",id:"what-is-admesh",level:2},{value:"Available SDKs",id:"available-sdks",level:2},{value:"Python SDK",id:"python-sdk",level:3},{value:"TypeScript SDK",id:"typescript-sdk",level:3},{value:"UI SDK",id:"ui-sdk",level:3},{value:"AI Agent Integration",id:"ai-agent-integration",level:2},{value:"Key Features",id:"key-features",level:2},{value:"Smart Recommendation Engine",id:"smart-recommendation-engine",level:3},{value:"Analytics and Tracking",id:"analytics-and-tracking",level:3},{value:"UI Component Library",id:"ui-component-library",level:3},{value:"Developer Experience",id:"developer-experience",level:3},{value:"Quick Start",id:"quick-start",level:2},{value:"Documentation Structure",id:"documentation-structure",level:2},{value:"Support Resources",id:"support-resources",level:2},{value:"Resources",id:"resources",level:2}];function c(e){const n={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",hr:"hr",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"admesh-sdk-documentation",children:"AdMesh SDK Documentation"})}),"\n",(0,t.jsx)(n.p,{children:"AdMesh SDK Documentation provides comprehensive technical guidance for integrating AI-powered product recommendation capabilities into enterprise applications and AI systems."}),"\n",(0,t.jsx)(n.h2,{id:"what-is-admesh",children:"What is AdMesh?"}),"\n",(0,t.jsx)(n.p,{children:"AdMesh is an enterprise-grade recommendation engine that enables developers to integrate intelligent product suggestions into applications. The platform supports chatbots, AI assistants, e-commerce platforms, and business applications requiring contextual product recommendations."}),"\n",(0,t.jsx)(n.h2,{id:"available-sdks",children:"Available SDKs"}),"\n",(0,t.jsx)(n.p,{children:"AdMesh provides three production-ready SDKs for different development environments:"}),"\n",(0,t.jsx)(n.h3,{id:"python-sdk",children:"Python SDK"}),"\n",(0,t.jsx)(n.p,{children:"Backend integration for AI applications, data processing pipelines, and server-side implementations."}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from admesh import Admesh\n\nclient = Admesh(api_key="your-api-key")\nresponse = client.recommend.get_recommendations(\n    query="Enterprise CRM solutions for distributed teams",\n    format="auto"\n)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"typescript-sdk",children:"TypeScript SDK"}),"\n",(0,t.jsx)(n.p,{children:"Node.js integration for serverless functions, API services, and modern web backends."}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-typescript",children:"import Admesh from 'admesh';\n\nconst client = new Admesh({ apiKey: 'your-api-key' });\nconst response = await client.recommend.getRecommendations({\n  query: 'Enterprise CRM solutions for distributed teams',\n  format: 'auto'\n});\n"})}),"\n",(0,t.jsx)(n.h3,{id:"ui-sdk",children:"UI SDK"}),"\n",(0,t.jsx)(n.p,{children:"React component library for frontend recommendation display with integrated analytics and tracking."}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-tsx",children:"import { AdMeshLayout } from 'admesh-ui-sdk';\n\n<AdMeshLayout\n  recommendations={recommendations}\n  autoLayout={true}\n  onProductClick={(adId, admeshLink) => {\n    window.open(admeshLink, '_blank');\n  }}\n/>\n"})}),"\n",(0,t.jsx)(n.h2,{id:"ai-agent-integration",children:"AI Agent Integration"}),"\n",(0,t.jsx)(n.p,{children:"AdMesh provides enterprise-grade integration capabilities for AI applications and intelligent agents:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Intent Detection Engine"})," - Automated query analysis and categorization"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Contextual Recommendations"})," - Context-aware product suggestions"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Citation Integration"})," - Numbered reference system for conversational interfaces"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Automated Recommendations"})," - Trigger-based suggestion generation"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Conversational Components"})," - Chat-optimized UI elements"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"key-features",children:"Key Features"}),"\n",(0,t.jsx)(n.h3,{id:"smart-recommendation-engine",children:"Smart Recommendation Engine"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Machine learning-powered intent detection"}),"\n",(0,t.jsx)(n.li,{children:"Semantic matching using cosine similarity algorithms"}),"\n",(0,t.jsx)(n.li,{children:"Trust score-based quality filtering"}),"\n",(0,t.jsx)(n.li,{children:"Real-time recommendation processing"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"analytics-and-tracking",children:"Analytics and Tracking"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Automated view and interaction tracking"}),"\n",(0,t.jsx)(n.li,{children:"Conversion monitoring and attribution"}),"\n",(0,t.jsx)(n.li,{children:"Performance metrics and reporting"}),"\n",(0,t.jsx)(n.li,{children:"Revenue analytics and insights"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"ui-component-library",children:"UI Component Library"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Production-ready React components"}),"\n",(0,t.jsx)(n.li,{children:"Citation-based conversational interfaces"}),"\n",(0,t.jsx)(n.li,{children:"Sidebar and floating chat implementations"}),"\n",(0,t.jsx)(n.li,{children:"Automated recommendation widgets"}),"\n",(0,t.jsx)(n.li,{children:"Theme customization support"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"developer-experience",children:"Developer Experience"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Full TypeScript support with type safety"}),"\n",(0,t.jsx)(n.li,{children:"Comprehensive error handling and validation"}),"\n",(0,t.jsx)(n.li,{children:"Asynchronous operation support"}),"\n",(0,t.jsx)(n.li,{children:"Complete documentation and implementation examples"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"quick-start",children:"Quick Start"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Obtain API credentials"})," from the ",(0,t.jsx)(n.a,{href:"https://useadmesh.com/agent",children:"AdMesh Dashboard"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Select appropriate SDK"})," based on your technology stack"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Install SDK"})," using your package manager"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Implement integration"})," following our technical guides"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"documentation-structure",children:"Documentation Structure"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/getting-started/overview",children:"Getting Started"})})," - Setup and configuration"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/python-sdk/installation",children:"Python SDK"})})," - Python integration guide"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/ui-sdk/installation",children:"UI SDK"})})," - React component integration"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/ai-integration/overview",children:"AI Integration"})})," - AI agent implementation guides"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/api/authentication",children:"API Reference"})})," - Complete API documentation"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:(0,t.jsx)(n.a,{href:"/examples/ai-assistant",children:"Examples"})})," - Implementation examples"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"support-resources",children:"Support Resources"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Documentation"}),": Complete technical documentation"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Issues"}),": ",(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-python/issues",children:"GitHub Issues"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Support"}),": ",(0,t.jsx)(n.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Dashboard"}),": ",(0,t.jsx)(n.a,{href:"https://useadmesh.com",children:"useadmesh.com"})]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"resources",children:"Resources"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"https://useadmesh.com",children:"AdMesh Dashboard"})," - API key management and analytics"]}),"\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-python",children:"Python SDK Repository"})}),"\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-typescript",children:"TypeScript SDK Repository"})}),"\n",(0,t.jsx)(n.li,{children:(0,t.jsx)(n.a,{href:"https://github.com/GouniManikumar12/admesh-ui-sdk",children:"UI SDK Repository"})}),"\n"]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsxs)(n.p,{children:["Begin integration by following our ",(0,t.jsx)(n.a,{href:"/getting-started/overview",children:"Getting Started Guide"}),"."]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(c,{...e})}):c(e)}}}]);