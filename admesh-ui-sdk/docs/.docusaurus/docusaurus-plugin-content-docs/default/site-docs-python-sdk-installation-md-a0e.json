{"id": "python-sdk/installation", "title": "Python SDK Installation", "description": "The AdMesh Python SDK provides programmatic access to the AdMesh REST API for Python 3.8+ applications. The SDK includes comprehensive type definitions for all request parameters and response fields, with support for both synchronous and asynchronous operations.", "source": "@site/docs/python-sdk/installation.md", "sourceDirName": "python-sdk", "slug": "/python-sdk/installation", "permalink": "/python-sdk/installation", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/python-sdk/installation.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "AdMesh vs Traditional Advertising", "permalink": "/getting-started/admesh-vs-traditional"}, "next": {"title": "TypeScript SDK Installation", "permalink": "/typescript-sdk/installation"}}