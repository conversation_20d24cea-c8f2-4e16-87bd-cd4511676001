{"id": "examples/ai-assistant", "title": "AI Assistant Integration", "description": "Technical implementation guide for building AI assistants with intelligent product recommendation capabilities using AdMesh. This example demonstrates production-ready conversational AI with contextual product suggestions.", "source": "@site/docs/examples/ai-assistant.md", "sourceDirName": "examples", "slug": "/examples/ai-assistant", "permalink": "/examples/ai-assistant", "draft": false, "unlisted": false, "editUrl": "https://github.com/GouniManikumar12/admesh-ui-sdk/tree/main/docs/docs/docs/examples/ai-assistant.md", "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"sidebar_position": 4}, "sidebar": "tutorialSidebar", "previous": {"title": "Authentication", "permalink": "/api/authentication"}}